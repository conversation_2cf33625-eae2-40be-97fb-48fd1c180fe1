# 问卷/量表填写与历史答卷界面
import os
import sys
from typing import Literal

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # mobile目录
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from kivy.uix.screenmanager import Screen
from kivy.properties import ListProperty, ObjectProperty, StringProperty
from kivy.clock import Clock
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.dialog import MDDialog
from kivymd.uix.list import MDList, MDListItem, MDListItemHeadlineText, MDListItemSupportingText, MDListItemTrailingIcon
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from utils.survey_manager import get_survey_manager
from utils.user_manager import get_user_manager
from kivy.lang import Builder
from kivy.uix.scrollview import ScrollView
from functools import partial
import threading
import json
import logging
from datetime import datetime
from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.factory import Factory
from screens.base_screen import BaseScreen

# 导入API客户端
from api.api_client import APIClient

# 导入认证管理器
from utils.auth_manager import get_auth_manager, AuthManager

# 导入云端API客户端
from utils.cloud_api import get_cloud_api

# 导入主题
from theme import AppTheme, AppMetrics

# 导入选选项卡组件
from kivymd.uix.tab import MDTabsItem

# 导入Logo组件
from widgets.logo import HealthLogo

# 设置日志
logger = logging.getLogger(__name__)

KV = '''
<SurveyScreen>:
    # BaseScreen会提供main_layout，这里不定义根节点内容
    # 内容将通过setup_content方法动态创建
'''

class SurveyScreen(BaseScreen):
    questionnaire_list = ListProperty([])
    questionnaires = ListProperty([])  # 添加缺失的questionnaires属性
    assessment_list = ListProperty([])
    assessments = ListProperty([])  # 添加缺失的assessments属性
    current_user = ObjectProperty(None)
    selected_questionnaire = ObjectProperty(None)
    selected_assessment = ObjectProperty(None)
    response_history = ListProperty([])
    api_client = None
    current_tab = StringProperty("assessments")
    _is_active = False
    _last_assessments_result = None
    _last_assessment_list = None
    _last_questionnaires_result = None
    _last_questionnaire_list = None

    def __init__(self, **kwargs):
        # 设置导航栏属性
        kwargs['screen_title'] = '评估量表与问卷'
        kwargs['show_top_bar'] = True
        kwargs['top_bar_action_icon'] = 'refresh'
        super().__init__(**kwargs)
        self.api_client = APIClient()
        self.current_tab = "assessments"
        self.assessment_list = []
        self.assessments = []
        self.questionnaire_list = []
        self.questionnaires = []
        self.response_history = []
        self.auth_manager = get_auth_manager()
        self.app = MDApp.get_running_app()
        self._is_active = False
        self._data_loaded = False
        self._ui_initialized = False
        self.cloud_api = get_cloud_api()  # 缓存API实例
        # 动态创建的列表组件引用
        self.response_history_list_widget = None
        self.assessment_list_widget = None
        self.questionnaire_list_widget = None

    def init_ui(self, dt: int = 0) -> Literal[True]:
        """初始化UI"""
        try:
            logger.info("[SurveyScreen] 开始初始化UI")
            
            # 首先调用父类的init_ui方法，确保顶部导航栏被正确设置
            super().init_ui(dt)
            
            # 检查main_layout是否存在，如果不存在则延迟初始化
            if not hasattr(self, 'ids') or not hasattr(self.ids, 'main_layout'):
                logger.warning("[SurveyScreen] main_layout尚未准备好，延迟0.2秒再初始化")
                Clock.schedule_once(self.init_ui, 0.2)
                return True
            
            # 创建并添加内容到main_layout
            self.setup_content()
            
            # 标记UI已初始化
            self._ui_initialized = True
            logger.info("[SurveyScreen] UI初始化完成")
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 初始化UI失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
        
        return True

    def setup_content(self):
        """设置屏幕内容，保持与原始布局一致"""
        try:
            logger.debug("[SurveyScreen] 开始设置屏幕内容")
            
            # 获取main_layout
            main_layout = getattr(self.ids, 'main_layout', None)
            if not main_layout:
                logger.error("[SurveyScreen] 无法找到main_layout")
                return
                
            # 创建滚动视图
            from kivymd.uix.scrollview import MDScrollView
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.card import MDCard
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.list import MDList
            
            scroll_view = MDScrollView(
                do_scroll_x=False,
                do_scroll_y=True
            )
            
            # 主内容容器 - 与health_data_management_screen保持一致
            content_layout = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                padding=[dp(20), dp(10), dp(20), dp(20)],
                spacing=dp(6)
            )
            content_layout.bind(minimum_height=content_layout.setter('height'))
            
            # 主要内容卡片
            main_card = MDCard(
                orientation='vertical',
                size_hint_y=None,
                height=dp(600),
                radius=[dp(12)],
                elevation=3,
                padding=[dp(16), dp(16), dp(16), dp(16)]
            )
            
            # 设置卡片背景色
            try:
                from theme import AppTheme
                main_card.md_bg_color = AppTheme.CARD_BACKGROUND
            except:
                main_card.md_bg_color = (1, 1, 1, 1)
            
            # 选项卡标题区域
            tab_layout = MDBoxLayout(
                size_hint_y=None,
                height=dp(48),
                spacing=dp(10)
            )
            
            # 评估量表按钮
            self.assessment_tab_btn = MDButton(
                size_hint_x=0.33,
                on_release=lambda x: self.switch_tab("assessments")
            )
            self.assessment_tab_btn.add_widget(MDButtonText(text="评估量表"))
            
            # 问卷按钮
            self.questionnaire_tab_btn = MDButton(
                size_hint_x=0.33,
                on_release=lambda x: self.switch_tab("questionnaires")
            )
            self.questionnaire_tab_btn.add_widget(MDButtonText(text="问卷"))
            
            # 历史记录按钮
            self.history_tab_btn = MDButton(
                size_hint_x=0.33,
                on_release=lambda x: self.switch_tab("history")
            )
            self.history_tab_btn.add_widget(MDButtonText(text="历史记录"))
            
            # 添加按钮到标签栏
            tab_layout.add_widget(self.assessment_tab_btn)
            tab_layout.add_widget(self.questionnaire_tab_btn)
            tab_layout.add_widget(self.history_tab_btn)
            
            # 内容区域
            self.content_area = MDBoxLayout(
                orientation='vertical'
            )
            
            # 评估量表内容
            self.assessments_content = self.create_tab_content("assessments")
            
            # 问卷内容  
            self.questionnaires_content = self.create_tab_content("questionnaires")
            
            # 历史记录内容
            self.history_content = self.create_tab_content("history")
            
            # 注意：不在这里添加内容到content_area，由update_tab_display控制显示
            
            # 组装主卡片
            main_card.add_widget(tab_layout)
            main_card.add_widget(self.content_area)
            
            # 添加到内容布局
            content_layout.add_widget(main_card)
            
            # 添加到滚动视图
            scroll_view.add_widget(content_layout)
            
            # 添加到主布局
            main_layout.add_widget(scroll_view)
            
            # 初始化标签页显示
            self.update_tab_display()
            
            logger.debug("[SurveyScreen] 屏幕内容设置完成")
            logger.debug(f"[SurveyScreen] 组件创建 - assessment: {hasattr(self, 'assessment_list_widget')}, questionnaire: {hasattr(self, 'questionnaire_list_widget')}, history: {hasattr(self, 'response_history_list_widget')}")
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 设置屏幕内容失败: {e}")
            import traceback
            traceback.print_exc()
            
    def create_tab_content(self, tab_name):
        """创建标签页内容 - 不包含单独的刷新按钮"""
        content = MDBoxLayout(
            orientation='vertical',
            padding=dp(10),
            spacing=dp(10),
            size_hint_y=None,
            height=dp(500)  # 固定高度以保持布局稳定
        )
        
        # 列表滚动视图
        from kivymd.uix.scrollview import MDScrollView
        list_scroll = MDScrollView(
            do_scroll_x=False,
            do_scroll_y=True,
            size_hint_y=1
        )
        
        # 列表容器
        list_widget = MDList(
            padding=dp(0),
            spacing=dp(1)
        )
        
        # 设置ID以便后续访问
        if tab_name == "assessments":
            list_widget.name = "assessment_list"
            self.assessment_list_widget = list_widget
        elif tab_name == "questionnaires":
            list_widget.name = "questionnaire_list"
            self.questionnaire_list_widget = list_widget
        elif tab_name == "history":
            list_widget.name = "response_history_list"
            self.response_history_list_widget = list_widget
        
        list_scroll.add_widget(list_widget)
        content.add_widget(list_scroll)
        
        return content
        
    def on_action(self):
        """处理右上角刷新按钮点击事件。根据当前tab刷新对应的数据。"""
        try:
            logger.info(f"[刷新按钮] 在{self.current_tab}标签页中点击刷新")
            
            # 使用线程加载数据以避免阻塞UI
            if self.current_tab == "assessments":
                logger.info("[刷新按钮] 刷新评估量表列表")
                threading.Thread(target=self.load_assessments).start()
            elif self.current_tab == "questionnaires":
                logger.info("[刷新按钮] 刷新问卷列表")
                threading.Thread(target=self.load_questionnaires).start()
            elif self.current_tab == "history":
                logger.info("[刷新按钮] 刷新历史记录")
                threading.Thread(target=self.load_response_history).start()
            else:
                logger.warning(f"[刷新按钮] 未知的标签页: {self.current_tab}")
                
        except Exception as e:
            logger.error(f"[刷新按钮] 刷新数据时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
        
    def update_tab_display(self):
        """更新标签页显示"""
        try:
            # 获取主题颜色
            try:
                from theme import AppTheme
                primary_color = AppTheme.PRIMARY_COLOR
                text_light = (1, 1, 1, 1)
            except:
                primary_color = (0.13, 0.59, 0.95, 1)
                text_light = (1, 1, 1, 1)
            
            # 更新按钮样式
            for btn, tab in [(self.assessment_tab_btn, "assessments"),
                           (self.questionnaire_tab_btn, "questionnaires"), 
                           (self.history_tab_btn, "history")]:
                if self.current_tab == tab:
                    btn.style = "filled"
                    btn.md_bg_color = primary_color
                    # 更新按钮文本颜色
                    for child in btn.children:
                        if hasattr(child, 'text_color'):
                            child.text_color = text_light
                else:
                    btn.style = "outlined"
                    btn.md_bg_color = (0, 0, 0, 0)
                    # 更新按钮文本颜色
                    for child in btn.children:
                        if hasattr(child, 'text_color'):
                            child.text_color = primary_color
            
            # 更新内容区域显示 - 改用remove/add_widget方式
            if hasattr(self, 'content_area'):
                # 清空内容区域
                self.content_area.clear_widgets()
                
                # 根据当前标签添加对应内容
                if self.current_tab == "assessments" and hasattr(self, 'assessments_content'):
                    self.content_area.add_widget(self.assessments_content)
                    logger.debug("[SurveyScreen] 显示评估量表内容")
                elif self.current_tab == "questionnaires" and hasattr(self, 'questionnaires_content'):
                    self.content_area.add_widget(self.questionnaires_content)
                    logger.debug("[SurveyScreen] 显示问卷内容")
                elif self.current_tab == "history" and hasattr(self, 'history_content'):
                    self.content_area.add_widget(self.history_content)
                    logger.debug("[SurveyScreen] 显示历史记录内容")
            
        except Exception as e:
            logger.error(f"[SurveyScreen] 更新标签页显示失败: {e}")
            
    def create_tabs_content(self, parent_layout):
        """创建标签页内容 - 现在由setup_content统一处理"""
        # 这个方法不再需要，由setup_content替代
        pass
        
    def load_initial_data(self):
        """加载初始数据"""
        try:
            logger.info(f"[SurveyScreen] 加载初始数据，当前标签: {self.current_tab}")
            
            # 使用线程加载数据以避免阻塞UI
            if self.current_tab == "assessments":
                threading.Thread(target=self.load_assessments).start()
            elif self.current_tab == "questionnaires":
                threading.Thread(target=self.load_questionnaires).start()
            elif self.current_tab == "history":
                threading.Thread(target=self.load_response_history).start()
                
        except Exception as e:
            logger.error(f"[SurveyScreen] 加载初始数据失败: {e}")

    def go_back(self):
        """返回上一个屏幕"""
        self.manager.current = 'health_data_management_screen'

    def switch_tab(self, tab_name):
        """切换标签页

        Args:
            tab_name: 标签页名称，可以是"assessments", "questionnaires", "history"
        """
        try:
            # 避免重复切换到同一标签页
            if self.current_tab == tab_name:
                logger.debug(f"[SurveyScreen] 已在标签页 {tab_name}，跳过切换")
                return
                
            logger.info(f"[SurveyScreen] 切换到标签页 {tab_name}")
            
            # 更新当前标签
            old_tab = self.current_tab
            self.current_tab = tab_name
            
            # 更新标签页视觉显示
            self.update_tab_display()

            # 检查页面状态
            if not self or not hasattr(self, 'ids'):
                logger.warning("[SurveyScreen] 页面已被移除或ids不存在，无法切换标签页")
                return

            # 根据标签页加载数据，使用线程避免阻塞UI
            if tab_name == "assessments":
                # 评估量表需要每次都刷新，因为可能有新的分发
                threading.Thread(target=self.load_assessments).start()
            elif tab_name == "questionnaires":
                # 问卷需要每次都刷新，因为可能有新的分发
                threading.Thread(target=self.load_questionnaires).start()
            elif tab_name == "history":
                # 历史记录需要每次都刷新，因为可能有新的完成数据
                threading.Thread(target=self.load_response_history).start()
                    
        except Exception as e:
            logger.error(f"[SurveyScreen] 切换标签页时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def on_enter(self, *args):
        """进入屏幕时的处理"""
        try:
            # 防止重复初始化
            if self._is_active:
                logger.debug("[SurveyScreen] 页面已激活，跳过重复初始化")
                return
                
            logger.info("[SurveyScreen] 进入页面")
            
            # 调用父类方法确保正确初始化
            super().on_enter(*args)
            
            # 标记页面为活跃状态
            self._is_active = True

            # 获取当前用户信息（仅在首次进入时）
            if not self.current_user:
                from utils.user_manager import get_user_manager
                user_manager = get_user_manager()
                self.current_user = user_manager.get_current_user()
                if self.current_user:
                    logger.info(f"[SurveyScreen] 当前用户custom_id: {getattr(self.current_user, 'custom_id', None)}")

            # 只在首次进入时加载数据
            if not self._data_loaded:
                self.load_initial_data()
                self._data_loaded = True
                
        except Exception as e:
            logger.error(f"[SurveyScreen] 进入页面时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
    def on_leave(self, *args):
        """离开屏幕时的处理"""
        logger.info("[SurveyScreen] 离开页面")
        # 重置激活状态，但保留初始化状态
        self._is_active = False

    def load_assessments(self, dt=None):
        """加载分发给用户的评估量表列表"""
        try:
            logger.info("开始加载分发评估量表列表")
            if not self.cloud_api.is_authenticated():
                logger.warning("用户未登录，无法加载评估量表")
                # 在主线程中显示错误信息
                Clock.schedule_once(lambda dt: self.show_error("用户未登录，无法加载评估量表"), 0)
                return
            
            # 根据当前标签页调用不同的API端点，参考问卷的处理逻辑
            if self.current_tab == "assessments":
                # 评估量表Tab：调用pending-assessments端点获取待完成量表
                result = self.cloud_api._make_request(
                    method="GET",
                    endpoint="mobile/pending-assessments",
                    headers={
                        'Authorization': f"Bearer {self.cloud_api.token}" if self.cloud_api.token else None,
                        'X-User-ID': self.cloud_api.custom_id if self.cloud_api.custom_id else None
                    }
                )
            elif self.current_tab == "history":
                # 历史记录Tab：调用history-assessments端点获取已完成量表
                result = self.cloud_api._make_request(
                    method="GET",
                    endpoint="mobile/history-assessments",
                    headers={
                        'Authorization': f"Bearer {self.cloud_api.token}" if self.cloud_api.token else None,
                        'X-User-ID': self.cloud_api.custom_id if self.cloud_api.custom_id else None
                    }
                )
            else:
                # 其他情况：使用原有的API
                result = self.cloud_api.get_mobile_assessments()

            if result and result.get('status') == 'success':
                data = result.get('data', {})
                assessments_data = data if isinstance(data, list) else data.get('assessments', []) if isinstance(data, dict) else []
                
                # 对于pending-assessments端点返回的数据，需要进行数据结构处理
                if self.current_tab == "assessments" and assessments_data:
                    # 对pending评估量表进行数据结构转换，确保问题去重等处理
                    wrapped_result = {"status": "success", "data": assessments_data}
                    processed_result = self.cloud_api._process_assessment_data_structure(wrapped_result)
                    assessments_data = processed_result.get('data', [])
                    logger.debug(f"Pending评估量表数据处理完成: {len(assessments_data)}条")
                
                logger.info(f"成功获取分发评估量表列表，共 {len(assessments_data)} 条")
                
                # 在主线程中更新数据和UI
                def update_ui(dt):
                    self.assessments = assessments_data

                    # 统计不同状态的量表数量
                    pending_count = sum(1 for a in assessments_data if isinstance(a, dict) and a.get('status') == 'pending')
                    completed_count = sum(1 for a in assessments_data if isinstance(a, dict) and a.get('status') == 'completed')
                    logger.info(f"[量表状态统计      ] pending={pending_count}, completed={completed_count}")

                    # 添加详细的数据调试信息
                    for i, assessment in enumerate(assessments_data[:3]):  # 只显示前3条以避免日志过长
                        if isinstance(assessment, dict):
                            logger.debug(f"量表{i+1}: id={assessment.get('id')}, name={assessment.get('name')}, title={assessment.get('title')}, status={assessment.get('status')}")

                    # 根据当前标签页更新UI
                    self._update_assessment_list_by_tab()
                    logger.info(f"[总评估量表数量     ] {len(self.assessments)}")
                
                Clock.schedule_once(update_ui, 0)
            else:
                logger.warning(f"获取分发评估量表失败: {result.get('message', '') if result else '无响应'}")
                # 在主线程中显示错误信息
                Clock.schedule_once(lambda dt: self.show_error("获取评估量表失败"), 0)
        except Exception as e:
            logger.error(f"加载分发评估量表时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 在主线程中显示错误信息
            Clock.schedule_once(lambda dt: self.show_error("加载评估量表时出错"), 0)

    def load_questionnaires(self, dt=None):
        """加载分发给用户的问卷列表"""
        try:
            logger.info("开始加载分发问卷列表")
            if not self.cloud_api.is_authenticated():
                logger.warning("用户未登录，无法加载问卷")
                # 在主线程中显示错误信息
                Clock.schedule_once(lambda dt: self.show_error("用户未登录，无法加载问卷"), 0)
                return

            # 根据当前标签页调用不同的API端点
            if self.current_tab == "questionnaires":
                # 问卷Tab：调用pending-questionnaires端点获取待完成问卷
                result = self.cloud_api._make_request(
                    method="GET",
                    endpoint="mobile/pending-questionnaires",
                    headers={
                        'Authorization': f"Bearer {self.cloud_api.token}" if self.cloud_api.token else None,
                        'X-User-ID': self.cloud_api.custom_id if self.cloud_api.custom_id else None
                    }
                )
            elif self.current_tab == "history":
                # 历史记录Tab：调用history-questionnaires端点获取已完成问卷
                result = self.cloud_api._make_request(
                    method="GET",
                    endpoint="mobile/history-questionnaires",
                    headers={
                        'Authorization': f"Bearer {self.cloud_api.token}" if self.cloud_api.token else None,
                        'X-User-ID': self.cloud_api.custom_id if self.cloud_api.custom_id else None
                    }
                )
            else:
                # 其他情况：使用原有的API
                result = self.cloud_api.get_mobile_questionnaires()

            if result and result.get('status') == 'success':
                data = result.get('data', {})
                questionnaires_data = data if isinstance(data, list) else data.get('questionnaires', []) if isinstance(data, dict) else []
                logger.info(f"成功获取分发问卷列表，共 {len(questionnaires_data)} 条")
                
                # 在主线程中更新数据和UI
                def update_ui(dt):
                    self.questionnaires = questionnaires_data

                    pending_count = sum(1 for q in questionnaires_data if isinstance(q, dict) and q.get('status') == 'pending')
                    completed_count = sum(1 for q in questionnaires_data if isinstance(q, dict) and q.get('status') == 'completed')
                    logger.info(f"问卷状态统计: pending={pending_count}, completed={completed_count}")

                    # 根据当前标签页更新UI
                    self._update_questionnaire_list_by_tab()
                    logger.info(f"总问卷数量: {len(self.questionnaires)}")
                
                Clock.schedule_once(update_ui, 0)
            else:
                logger.warning(f"获取分发问卷失败: {result.get('message', '') if result else '无响应'}")
                # 在主线程中显示错误信息
                Clock.schedule_once(lambda dt: self.show_error("获取问卷失败"), 0)
        except Exception as e:
            logger.error(f"加载分发问卷时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 在主线程中显示错误信息
            Clock.schedule_once(lambda dt: self.show_error("加载问卷时出错"), 0)

    def load_response_history(self, dt=None):
        """加载响应历史记录"""
        try:
            logger.info("开始加载响应历史记录")
            if not self.cloud_api.is_authenticated():
                logger.warning("用户未登录，无法加载历史记录")
                # 在主线程中显示错误信息
                Clock.schedule_once(lambda dt: self.show_error("用户未登录，无法加载历史记录"), 0)
                return

            # 分别获取历史评估量表和问卷报告
            # 获取历史评估量表报告
            assessment_result = self.cloud_api._make_request(
                method="GET",
                endpoint="mobile/history-assessments",
                headers={
                    'Authorization': f"Bearer {self.cloud_api.token}" if self.cloud_api.token else None,
                    'X-User-ID': self.cloud_api.custom_id if self.cloud_api.custom_id else None
                }
            )

            # 获取历史问卷报告
            questionnaire_result = self.cloud_api._make_request(
                method="GET",
                endpoint="mobile/history-questionnaires",
                headers={
                    'Authorization': f"Bearer {self.cloud_api.token}" if self.cloud_api.token else None,
                    'X-User-ID': self.cloud_api.custom_id if self.cloud_api.custom_id else None
                }
            )

            # 合并两个结果
            history_data = []
            
            # 处理评估量表历史记录
            if assessment_result and assessment_result.get('status') == 'success':
                assessment_data = assessment_result.get('data', [])
                if isinstance(assessment_data, list):
                    history_data.extend(assessment_data)
                elif isinstance(assessment_data, dict) and 'data' in assessment_data:
                    history_data.extend(assessment_data['data'])
                logger.info(f"成功获取历史评估量表记录，共 {len(assessment_data) if isinstance(assessment_data, list) else len(assessment_data.get('data', []))} 条")
            
            # 处理问卷历史记录
            if questionnaire_result and questionnaire_result.get('status') == 'success':
                questionnaire_data = questionnaire_result.get('data', [])
                if isinstance(questionnaire_data, list):
                    history_data.extend(questionnaire_data)
                elif isinstance(questionnaire_data, dict) and 'data' in questionnaire_data:
                    history_data.extend(questionnaire_data['data'])
                logger.info(f"成功获取历史问卷记录，共 {len(questionnaire_data) if isinstance(questionnaire_data, list) else len(questionnaire_data.get('data', []))} 条")
            
            # 按完成时间排序
            try:
                history_data.sort(key=lambda x: x.get('completed_at', ''), reverse=True)
            except Exception as e:
                logger.warning(f"排序历史记录时出错: {e}")
            
            logger.info(f"合并后历史记录总数: {len(history_data)}")
                
            # 在主线程中更新数据和UI
            def update_ui(dt):
                self.response_history = history_data
                self._update_response_history_ui()
                logger.info(f"历史记录数量: {len(self.response_history)}")
            
            Clock.schedule_once(update_ui, 0)
        except Exception as e:
            logger.error(f"加载响应历史记录时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            # 在主线程中显示错误信息
            Clock.schedule_once(lambda dt: self.show_error("加载历史记录时出错"), 0)

    def _update_assessment_list_by_tab(self):
        """根据当前标签页更新评估量表列表"""
        try:
            if self.current_tab == "assessments":
                # 显示待完成的评估量表
                self.assessment_list = [a for a in self.assessments if isinstance(a, dict) and a.get('status') == 'pending']
            elif self.current_tab == "history":
                # 显示已完成的评估量表
                self.assessment_list = [a for a in self.assessments if isinstance(a, dict) and a.get('status') == 'completed']
            else:
                # 默认显示所有评估量表
                self.assessment_list = self.assessments
                
            logger.info(f"根据标签页过滤后，评估量表数量: {len(self.assessment_list)}")
            self._refresh_assessment_list_ui()
        except Exception as e:
            logger.error(f"根据标签页更新评估量表列表时出错: {e}")

    def _update_questionnaire_list_by_tab(self):
        """根据当前标签页更新问卷列表"""
        try:
            if self.current_tab == "questionnaires":
                # 显示待完成的问卷
                self.questionnaire_list = [q for q in self.questionnaires if isinstance(q, dict) and q.get('status') == 'pending']
            elif self.current_tab == "history":
                # 显示已完成的问卷
                self.questionnaire_list = [q for q in self.questionnaires if isinstance(q, dict) and q.get('status') == 'completed']
            else:
                # 默认显示所有问卷
                self.questionnaire_list = self.questionnaires
                
            logger.info(f"根据标签页过滤后，问卷数量: {len(self.questionnaire_list)}")
            self._refresh_questionnaire_list_ui()
        except Exception as e:
            logger.error(f"根据标签页更新问卷列表时出错: {e}")

    def _update_response_history_ui(self, dt=None):
        """更新历史记录UI"""
        try:
            logger.debug(f"[SurveyScreen] 更新历史记录UI，数据数量: {len(self.response_history)}")
            
            # 使用新的动态创建的列表组件引用
            if not hasattr(self, 'response_history_list_widget') or not self.response_history_list_widget:
                logger.error("SurveyScreen: response_history_list_widget不存在")
                return

            response_history_list_widget = self.response_history_list_widget
            if not response_history_list_widget:
                logger.error("SurveyScreen: response_history_list_widget为空")
                return

            try:
                response_history_list_widget.clear_widgets()
            except ReferenceError:
                logger.error("SurveyScreen: response_history_list_widget已被销毁，跳过UI刷新")
                return

            if not self.response_history:
                try:
                    from kivymd.uix.label import MDLabel
                    response_history_list_widget.add_widget(MDLabel(
                        text='暂无历史记录', 
                        halign='center', 
                        theme_text_color='Hint'
                    ))
                except ReferenceError:
                    logger.error("SurveyScreen: response_history_list_widget已被销毁，跳过空状态UI")
                return

            # 显示历史记录
            for item in self.response_history:
                try:
                    from kivymd.uix.list import MDListItem, MDListItemHeadlineText, MDListItemSupportingText
                    
                    list_item = MDListItem(on_release=lambda x, item=item: self.show_response_detail(item))
                    
                    # 根据类型设置标题
                    item_type = item.get('item_type', '') if isinstance(item, dict) else ''
                    if 'assessment' in item_type:
                        title = f"评估量表: {item.get('title', '未命名量表') if isinstance(item, dict) else '未命名量表'}"
                    elif 'questionnaire' in item_type:
                        title = f"问卷: {item.get('title', '未命名问卷') if isinstance(item, dict) else '未命名问卷'}"
                    else:
                        title = item.get('title', '未命名项目') if isinstance(item, dict) else '未命名项目'
                    
                    list_item.add_widget(MDListItemHeadlineText(text=title))
                    
                    # 设置描述信息
                    submitted_at = item.get('submitted_at', '') if isinstance(item, dict) else ''
                    if submitted_at:
                        # 格式化时间
                        try:
                            dt_obj = datetime.fromisoformat(submitted_at.replace('Z', '+00:00'))
                            formatted_time = dt_obj.strftime('%Y-%m-%d %H:%M')
                            description = f"提交时间: {formatted_time}"
                        except:
                            description = f"提交时间: {submitted_at}"
                    else:
                        description = "无提交时间"
                    
                    list_item.add_widget(MDListItemSupportingText(text=description))
                    response_history_list_widget.add_widget(list_item)
                except ReferenceError:
                    logger.error("SurveyScreen: 添加历史记录项时发生ReferenceError，跳过该项")
                    continue
                    
            logger.info(f"[SurveyScreen] 历史记录UI更新完成，共添加 {len(self.response_history)} 个项目")
        except ReferenceError:
            logger.error("SurveyScreen: response_history_list_widget已被销毁，跳过历史记录列表UI刷新")
        except Exception as e:
            logger.error(f"SurveyScreen: 刷新历史记录列表UI时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _refresh_assessment_list_ui(self):
        """刷新评估量表列表UI"""
        try:
            logger.debug(f"[SurveyScreen] 刷新评估量表UI，数据数量: {len(self.assessment_list) if hasattr(self, 'assessment_list') else 0}")
            
            # 使用新的动态创建的列表组件引用
            if not hasattr(self, 'assessment_list_widget') or not self.assessment_list_widget:
                logger.error("SurveyScreen: assessment_list_widget不存在")
                return

            assessment_list_widget = self.assessment_list_widget
            if not assessment_list_widget:
                logger.error("SurveyScreen: assessment_list_widget为空")
                return

            try:
                assessment_list_widget.clear_widgets()
            except ReferenceError:
                logger.error("SurveyScreen: assessment_list_widget已被销毁，跳过UI刷新")
                return

            if not self.assessment_list:
                try:
                    from kivymd.uix.label import MDLabel
                    assessment_list_widget.add_widget(MDLabel(
                        text='暂无分发的量表', 
                        halign='center', 
                        theme_text_color='Hint'
                    ))
                except ReferenceError:
                    logger.error("SurveyScreen: assessment_list_widget已被销毁，跳过空状态UI")
                return

            # 使用集合去重，仅保留不同标题的量表
            unique_titles = set()
            unique_assessments = []

            for item in self.assessment_list:
                if isinstance(item, dict):
                    title = item.get('title') or item.get('name') or '未命名量表'
                    if title not in unique_titles:
                        unique_titles.add(title)
                        unique_assessments.append(item)

            logger.info(f"SurveyScreen: 量表去重后数量: {len(unique_assessments)}/{len(self.assessment_list)}")

            for item in unique_assessments:
                try:
                    from kivymd.uix.list import MDListItem, MDListItemHeadlineText, MDListItemSupportingText
                    
                    list_item = MDListItem(on_release=lambda x, item=item: self.on_assessment_selected(item))
                    list_item.add_widget(MDListItemHeadlineText(text=item.get('title') or item.get('name') or '未命名量表'))
                    # 优先显示template.description，确保template是字典类型
                    template = item.get('template', {})
                    if isinstance(template, dict):
                        template_description = template.get('description')
                    else:
                        template_description = None

                    description = (
                        template_description
                        or item.get('description')
                        or '无描述'
                    )
                    if len(description) > 100:
                        description = description[:100] + '...'
                    list_item.add_widget(MDListItemSupportingText(text=description))
                    assessment_list_widget.add_widget(list_item)
                except ReferenceError:
                    logger.error("SurveyScreen: 添加量表项时发生ReferenceError，跳过该项")
                    continue
                    
            logger.info(f"[SurveyScreen] 评估量表UI刷新完成，共添加 {len(unique_assessments)} 个项目")
        except ReferenceError:
            logger.error("SurveyScreen: assessment_list_widget已被销毁，跳过量表列表UI刷新")
        except Exception as e:
            logger.error(f"SurveyScreen: 刷新量表列表UI时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _refresh_questionnaire_list_ui(self):
        """刷新问卷列表UI"""
        try:
            # 使用新的动态创建的列表组件引用
            if not hasattr(self, 'questionnaire_list_widget') or not self.questionnaire_list_widget:
                logger.error("SurveyScreen: questionnaire_list_widget不存在")
                return

            questionnaire_list_widget = self.questionnaire_list_widget
            if not questionnaire_list_widget:
                logger.error("SurveyScreen: questionnaire_list_widget为空")
                return

            try:
                questionnaire_list_widget.clear_widgets()
            except ReferenceError:
                logger.error("SurveyScreen: questionnaire_list_widget已被销毁，跳过UI刷新")
                return

            if not self.questionnaire_list:
                try:
                    from kivymd.uix.label import MDLabel
                    questionnaire_list_widget.add_widget(MDLabel(text='暂无分发的问卷', halign='center', theme_text_color='Hint'))
                except ReferenceError:
                    logger.error("SurveyScreen: questionnaire_list_widget已被销毁，跳过空状态UI")
                return

            # 使用集合去重，仅保留不同标题的问卷
            unique_titles = set()
            unique_questionnaires = []

            for item in self.questionnaire_list:
                if isinstance(item, dict):
                    title = item.get('title') or item.get('name') or '未命名问卷'
                    if title not in unique_titles:
                        unique_titles.add(title)
                        unique_questionnaires.append(item)

            logger.info(f"SurveyScreen: 问卷去重后数量: {len(unique_questionnaires)}/{len(self.questionnaire_list)}")

            for item in unique_questionnaires:
                try:
                    from kivymd.uix.list import MDListItem, MDListItemHeadlineText, MDListItemSupportingText
                    
                    list_item = MDListItem(on_release=lambda x, item=item: self.on_questionnaire_selected(item))
                    list_item.add_widget(MDListItemHeadlineText(text=item.get('title') or item.get('name') or '未命名问卷'))
                    # 优先显示template.description，确保template是字典类型
                    template = item.get('template', {})
                    if isinstance(template, dict):
                        template_description = template.get('description')
                    else:
                        template_description = None

                    description = (
                        template_description
                        or item.get('description')
                        or '无描述'
                    )
                    if len(description) > 100:
                        description = description[:100] + '...'
                    list_item.add_widget(MDListItemSupportingText(text=description))
                    questionnaire_list_widget.add_widget(list_item)
                except ReferenceError:
                    logger.error("SurveyScreen: 添加问卷项时发生ReferenceError，跳过该项")
                    continue
        except ReferenceError:
            logger.error("SurveyScreen: questionnaire_list_widget已被销毁，跳过问卷列表UI刷新")
        except Exception as e:
            logger.error(f"SurveyScreen: 刷新问卷列表UI时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def on_assessment_selected(self, item):
        """评估量表选择事件处理"""
        logger.info(f"选中评估量表: {item.get('title', '') if isinstance(item, dict) else ''}")
        self.selected_assessment = item
        self.open_assessment(item)

    def open_assessment(self, assessment_data):
        """打开评估量表

        Args:
            assessment_data: 评估量表数据
        """
        try:
            logger.info(f"打开评估量表: {assessment_data}")

            # 获取问题列表
            has_questions, questions_source = self._get_assessment_questions(assessment_data)
            
            if has_questions:
                logger.info(f"评估量表问题来源: {questions_source}")

            # 设置当前评估量表并导航
            self._set_current_assessment_and_navigate(assessment_data)
            
        except Exception as e:
            logger.error(f"打开评估量表时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"打开评估量表时出错: {str(e)}")
    
    def _get_assessment_questions(self, assessment_data):
        """获取评估量表问题列表
        
        Args:
            assessment_data: 评估量表数据
            
        Returns:
            tuple: (是否有问题, 问题来源)
        """
        # 尝试直接获取问题
        has_questions, questions_source = self._try_get_direct_questions(assessment_data)
        if has_questions:
            return has_questions, questions_source
        
        # 尝试从模板获取问题
        has_questions, questions_source = self._try_get_template_questions(assessment_data)
        if has_questions:
            return has_questions, questions_source
        
        # 尝试从API获取问题
        has_questions, questions_source = self._try_get_api_questions(assessment_data)
        
        return has_questions, questions_source
    
    def _try_get_direct_questions(self, assessment_data):
        """尝试直接从评估量表数据获取问题
        
        Args:
            assessment_data: 评估量表数据
            
        Returns:
            tuple: (是否有问题, 问题来源)
        """
        if (isinstance(assessment_data, dict) and 
            'questions' in assessment_data and 
            assessment_data['questions']):
            
            logger.info(f"从评估量表直接获取问题列表，共 {len(assessment_data['questions'])} 个问题")
            return True, "直接获取"
        
        return False, "未知"
    
    def _try_get_template_questions(self, assessment_data):
        """尝试从模板获取问题
        
        Args:
            assessment_data: 评估量表数据
            
        Returns:
            tuple: (是否有问题, 问题来源)
        """
        if (isinstance(assessment_data, dict) and 
            'template' in assessment_data and 
            isinstance(assessment_data['template'], dict)):
            
            template = assessment_data['template']
            
            if 'questions' in template:
                assessment_data['questions'] = template['questions']
                logger.info(f"从评估量表模板获取问题列表，共 {len(assessment_data['questions'])} 个问题")
                
                # 确保有template_id字段
                self._ensure_template_id(assessment_data, template)
                
                return True, "从模板获取"
        
        return False, "未知"
    
    def _ensure_template_id(self, assessment_data, template):
        """确保评估量表数据中有template_id字段
        
        Args:
            assessment_data: 评估量表数据
            template: 模板数据
        """
        if ('template_id' not in assessment_data and 
            isinstance(template, dict) and 
            'id' in template):
            
            assessment_data['template_id'] = template['id']
            logger.info(f"添加template_id: {template['id']}")
    
    def _try_get_api_questions(self, assessment_data):
        """尝试从API获取问题
        
        Args:
            assessment_data: 评估量表数据
            
        Returns:
            tuple: (是否有问题, 问题来源)
        """
        if not isinstance(assessment_data, dict):
            return False, "未知"
        
        assessment_id = assessment_data.get('id')
        if not assessment_id:
            logger.warning(f"评估量表 {assessment_data.get('title', '未知')} 没有ID，无法获取问题列表")
            return False, "未知"
        
        logger.info(f"评估量表 {assessment_data.get('title')} (ID: {assessment_id}) 没有问题列表，尝试从 API 获取")
        
        # 尝试从评估量表列表API获取
        has_questions, questions_source = self._try_get_from_assessments_api(assessment_data, assessment_id)
        if has_questions:
            return has_questions, questions_source
        
        # 尝试从单独的问题API获取
        has_questions, questions_source = self._try_get_from_questions_api(assessment_data, assessment_id)
        if has_questions:
            return has_questions, questions_source
        
        logger.warning(f"评估量表 {assessment_data.get('title')} (ID: {assessment_id}) 没有问题列表，将在表单页面尝试获取")
        return False, "未知"
    
    def _try_get_from_assessments_api(self, assessment_data, assessment_id):
        """尝试从评估量表列表API获取问题
        
        Args:
            assessment_data: 评估量表数据
            assessment_id: 评估量表ID
            
        Returns:
            tuple: (是否有问题, 问题来源)
        """
        try:
            result = self.cloud_api.get_mobile_assessments(
                custom_id=getattr(assessment_data, 'custom_id', None)
            )
            
            if result and isinstance(result, dict) and 'data' in result:
                for item in result['data']:
                    if isinstance(item, dict) and item.get('id') == assessment_id:
                        return self._update_assessment_from_api_item(assessment_data, item)
            
        except Exception as e:
            logger.error(f"从评估量表列表API获取问题时出错: {e}")
        
        return False, "未知"
    
    def _update_assessment_from_api_item(self, assessment_data, api_item):
        """从API项目更新评估量表数据
        
        Args:
            assessment_data: 评估量表数据
            api_item: API返回的项目数据
            
        Returns:
            tuple: (是否有问题, 问题来源)
        """
        if 'questions' in api_item and api_item['questions']:
            assessment_data['questions'] = api_item['questions']
            logger.info(f"成功从 API 获取评估量表问题，共 {len(api_item['questions'])} 个问题")
            
            # 同时更新template信息
            if 'template' in api_item:
                assessment_data['template'] = api_item['template']
            if 'template_id' in api_item:
                assessment_data['template_id'] = api_item['template_id']
            
            return True, "API重新获取"
        
        return False, "未知"
    
    def _try_get_from_questions_api(self, assessment_data, assessment_id):
        """尝试从单独的问题API获取问题
        
        Args:
            assessment_data: 评估量表数据
            assessment_id: 评估量表ID
            
        Returns:
            tuple: (是否有问题, 问题来源)
        """
        try:
            logger.info(f"通过列表未找到问题，尝试单独获取评估量表问题")
            result = self.cloud_api.get_assessment_questions(assessment_id)
            
            if result and 'questions' in result and result['questions']:
                assessment_data['questions'] = result['questions']
                logger.info(f"成功从 get_assessment_questions API 获取问题，共 {len(result['questions'])} 个问题")
                return True, "get_assessment_questions API"
                
        except Exception as e:
            logger.error(f"从问题API获取问题时出错: {e}")
        
        return False, "未知"
    
    def _set_current_assessment_and_navigate(self, assessment_data):
        """设置当前评估量表并导航到表单页面
        
        Args:
            assessment_data: 评估量表数据
        """
        app = MDApp.get_running_app()
        if app:
            app.assessment_to_fill = assessment_data

        # 导航到评估量表表单页面
        self.manager.current = "assessment_form_screen"

    def on_questionnaire_selected(self, item):
        """问卷选择事件处理"""
        logger.info(f"选中问卷: {item.get('title', '') if isinstance(item, dict) else ''}")
        self.selected_questionnaire = item
        self.open_questionnaire(item)

    def navigate_to_assessment_form(self, assessment_data):
        """导航到评估量表表单"""
        try:
            # 保存当前选中的评估量表数据
            app = MDApp.get_running_app()
            if app:
                app.assessment_to_fill = assessment_data

            logger.info(f"准备导航到评估表单，数据ID: {assessment_data.get('id', 'unknown') if isinstance(assessment_data, dict) else 'unknown'}")

            # 动态导入并创建评估表单屏幕
            if app and hasattr(app, 'root') and not app.root.has_screen('assessment_form_screen'):
                logger.info("创建新的评估表单屏幕")
                from screens.assessment_form_screen import AssessmentFormScreen
                assessment_form = AssessmentFormScreen(name='assessment_form_screen')
                app.root.add_widget(assessment_form)
            elif app and hasattr(app, 'root'):
                logger.info("使用已存在的评估表单屏幕")
                # 获取已存在的评估表单屏幕，on_enter方法会自动处理初始化
                assessment_form = app.root.get_screen('assessment_form_screen')

            # 切换到评估表单屏幕
            logger.info("切换到评估表单屏幕")
            if app and hasattr(app, 'root'):
                app.root.transition.direction = 'left'
                app.root.current = 'assessment_form_screen'
        except Exception as e:
            logger.error(f"导航到评估表单时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"导航到评估表单时出错: {str(e)}")

    def show_questionnaire_detail(self, questionnaire, *args):
        """显示问卷详情"""
        try:
            logger.info(f"显示问卷详情: {questionnaire}")

            # 准备问卷数据
            questionnaire_id = questionnaire.get('id') if isinstance(questionnaire, dict) else None

            # 获取问卷标题
            title = ""
            if isinstance(questionnaire, dict) and 'questionnaire_info' in questionnaire and 'title' in questionnaire['questionnaire_info']:
                title = questionnaire['questionnaire_info']['title']
            else:
                title = questionnaire.get('title', '未命名问卷') if isinstance(questionnaire, dict) else '未命名问卷'

            # 获取问题列表
            questions = []
            if isinstance(questionnaire, dict) and 'questions' in questionnaire:
                questions = questionnaire['questions']

            # 如果没有问题列表，先不报错，让表单页面尝试从API获取
            if not questions:
                logger.warning(f"问卷 {title} (ID: {questionnaire_id}) 没有问题列表，将在表单页面尝试获取")

            # 显示确认对话框
            self.show_dialog(
                f"{title}",
                f"准备开始填写问卷" + (f"，共 {len(questions)} 个问题" if questions else ""),
                actions=[
                    ["取消", lambda *args: None],
                    ["开始", lambda *args: self.navigate_to_questionnaire_form(questionnaire)]
                ]
            )
        except Exception as e:
            logger.error(f"显示问卷详情时出错: {e}")
            self.show_error("显示问卷详情时出错，请稍后重试")

    def navigate_to_questionnaire_form(self, questionnaire_data):
        """导航到问卷表单"""
        try:
            # 保存当前选中的问卷数据
            app = MDApp.get_running_app()
            if app:
                app.questionnaire_to_fill = questionnaire_data

            logger.info(f"准备导航到问卷表单，数据ID: {questionnaire_data.get('id', 'unknown') if isinstance(questionnaire_data, dict) else 'unknown'}")

            # 动态导入并创建问卷表单屏幕
            if app and hasattr(app, 'root') and not app.root.has_screen('questionnaire_form_screen'):
                logger.info("创建新的问卷表单屏幕")
                from screens.questionnaire_form_screen import QuestionnaireFormScreen
                questionnaire_form = QuestionnaireFormScreen(name='questionnaire_form_screen')
                app.root.add_widget(questionnaire_form)
            elif app and hasattr(app, 'root'):
                logger.info("使用已存在的问卷表单屏幕")
                # 确保问卷表单屏幕被重新初始化
                questionnaire_form = app.root.get_screen('questionnaire_form_screen')
                # 立即初始化UI，不使用Clock延迟
                questionnaire_form.init_ui()

            # 切换到问卷表单屏幕
            logger.info("切换到问卷表单屏幕")
            if app and hasattr(app, 'root'):
                app.root.transition.direction = 'left'
                app.root.current = 'questionnaire_form_screen'
        except Exception as e:
            logger.error(f"导航到问卷表单时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"导航到问卷表单时出错: {str(e)}")

    def show_response_detail(self, response, *args):
        """显示历史记录详情"""
        try:
            logger.info(f"显示回复详情: {response}")

            # 直接调用navigate_to_response_detail方法显示详情
            self.navigate_to_response_detail(response)
        except Exception as e:
            logger.error(f"显示历史记录详情时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error("显示历史记录详情时出错，请稍后重试")

    def navigate_to_response_detail(self, response):
        """导航到报告详情页面"""
        try:
            logger.info(f"导航到报告详情: {response}")

            # 确定报告类型
            item_type = response.get('item_type', '') if isinstance(response, dict) else ''
            report_type = "assessment" if "assessment" in item_type else "questionnaire"

            # 获取报告ID
            report_id = response.get('id') if isinstance(response, dict) else None
            if not report_id:
                self.show_error("无效的报告ID")
                return

            # 确保custom_id存在于报告数据中
            custom_id = response.get('custom_id') or (self.current_user.custom_id if self.current_user else None)
            if custom_id and isinstance(response, dict) and 'custom_id' not in response:
                response['custom_id'] = custom_id

            # 保存当前选中的报告数据到应用实例和屏幕管理器
            app = MDApp.get_running_app()
            if app:
                # 优先保存到应用实例，这是ReportDetailScreen首先检查的地方
                app.current_response = response
                # 同时保存到屏幕管理器，确保兼容性
                if hasattr(app, 'root'):
                    app.root.current_report_data = response
                    app.root.current_report_type = report_type
                    app.root.current_report_id = report_id
                    app.root.current_user_id = custom_id

            # 导航到报告详情页面
            if app and hasattr(app, 'root') and not app.root.has_screen('report_detail'):
                from screens.report_detail_screen import ReportDetailScreen
                report_screen = ReportDetailScreen(name='report_detail')
                app.root.add_widget(report_screen)
            
            if app and hasattr(app, 'root'):
                app.root.transition.direction = 'left'
                app.root.current = 'report_detail'
        except Exception as e:
            logger.error(f"导航到报告详情时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error("导航到报告详情时出错")

    def show_dialog(self, title, text, actions=None):
        """显示对话框"""
        try:
            if not actions:
                actions = [["确定", lambda *args: None]]
                
            # 创建按钮
            buttons = []
            for action in actions:
                btn_text, callback = action
                button = MDButton(
                    MDButtonText(text=btn_text),
                    style="text"
                )
                button.bind(on_release=callback)
                buttons.append(button)
            
            # 创建对话框
            dialog = MDDialog(
                title=title,
                text=text,
                buttons=buttons
            )
            
            dialog.open()
            return dialog
        except Exception as e:
            logger.error(f"显示对话框时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def open_questionnaire(self, questionnaire_data):
        """打开问卷

        Args:
            questionnaire_data: 问卷数据
        """
        try:
            logger.info(f"打开问卷: {questionnaire_data}")
            
            # 获取问题列表
            questions = self._get_questionnaire_questions(questionnaire_data)
            
            # 确保问卷数据包含问题列表
            self._ensure_questionnaire_has_questions(questionnaire_data, questions)
            
            # 设置当前问卷并导航
            self._navigate_to_questionnaire_form(questionnaire_data)
            
        except Exception as e:
            logger.error(f"打开问卷时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error(f"打开问卷时出错: {str(e)}")
    
    def _get_questionnaire_questions(self, questionnaire_data):
        """获取问卷问题列表
        
        Args:
            questionnaire_data: 问卷数据
            
        Returns:
            list: 问题列表
        """
        questions = []
        
        # 尝试从本地数据获取问题
        questions = self._try_get_local_questions(questionnaire_data)
        
        # 如果本地没有问题，从API获取
        if not questions:
            questions = self._try_get_api_questions(questionnaire_data)
            
        return questions
    
    def _try_get_local_questions(self, questionnaire_data):
        """尝试从本地数据获取问题列表
        
        Args:
            questionnaire_data: 问卷数据
            
        Returns:
            list: 问题列表
        """
        questions = []
        
        if isinstance(questionnaire_data, dict):
            # 直接从问卷数据获取
            if 'questions' in questionnaire_data and questionnaire_data['questions']:
                questions = questionnaire_data['questions']
                logger.info(f"从问卷直接获取问题列表，共 {len(questions)} 个问题")
            # 从模板获取
            elif ('template' in questionnaire_data and 
                  isinstance(questionnaire_data['template'], dict) and
                  'questions' in questionnaire_data['template']):
                questions = questionnaire_data['template']['questions']
                logger.info(f"从问卷模板获取问题列表，共 {len(questions)} 个问题")
                
        return questions
    
    def _try_get_api_questions(self, questionnaire_data):
        """尝试从API获取问题列表
        
        Args:
            questionnaire_data: 问卷数据
            
        Returns:
            list: 问题列表
        """
        questions = []
        questionnaire_id = questionnaire_data.get('id') if isinstance(questionnaire_data, dict) else None
        
        if not questionnaire_id:
            logger.warning(f"问卷 {questionnaire_data.get('title') if isinstance(questionnaire_data, dict) else '未知'} 没有ID，无法从API获取问题列表")
            return questions
            
        if not (self.cloud_api and self.cloud_api.is_authenticated()):
            logger.warning("云API未认证或不可用")
            return questions
            
        logger.info(f"问卷ID: {questionnaire_id}，强制尝试获取问题列表")
        
        try:
            # 尝试直接获取问题
            questions = self._try_direct_api_questions(questionnaire_id, questionnaire_data)
            
            # 如果直接获取失败，尝试从完整数据获取
            if not questions:
                questions = self._try_full_data_questions(questionnaire_id, questionnaire_data)
                
            # 如果还是没有，尝试从详情获取
            if not questions:
                questions = self._try_detail_questions(questionnaire_id, questionnaire_data)
                
        except Exception as api_error:
            logger.error(f"从API获取问题列表失败: {api_error}")
            import traceback
            logger.error(traceback.format_exc())
            
        return questions
    
    def _try_direct_api_questions(self, questionnaire_id, questionnaire_data):
        """尝试直接从API获取问题
        
        Args:
            questionnaire_id: 问卷ID
            questionnaire_data: 问卷数据
            
        Returns:
            list: 问题列表
        """
        logger.info(f"直接调用get_questionnaire_questions获取问题")
        result = self.cloud_api.get_questionnaire_questions(questionnaire_id)
        
        if result and 'questions' in result and result['questions']:
            questions = result['questions']
            if isinstance(questionnaire_data, dict):
                questionnaire_data['questions'] = questions
            logger.info(f"成功从API获取问卷问题，共 {len(questions)} 个问题")
            return questions
            
        return []
    
    def _try_full_data_questions(self, questionnaire_id, questionnaire_data):
        """尝试从完整数据获取问题
        
        Args:
            questionnaire_id: 问卷ID
            questionnaire_data: 问卷数据
            
        Returns:
            list: 问题列表
        """
        logger.info(f"通过get_questionnaire_questions未找到问题，尝试获取完整数据")
        result = self.cloud_api.get_mobile_questionnaires(
            custom_id=getattr(questionnaire_data, 'custom_id', None)
        )
        
        if not (result and isinstance(result, dict) and 'data' in result):
            return []
            
        # 在返回的数据中查找对应ID的问卷
        for item in result['data']:
            if not (isinstance(item, dict) and item.get('id') == questionnaire_id):
                continue
                
            # 找到匹配的问卷，尝试获取问题
            questions = self._extract_questions_from_item(item, questionnaire_data)
            if questions:
                return questions
                
        return []
    
    def _extract_questions_from_item(self, item, questionnaire_data):
        """从数据项中提取问题
        
        Args:
            item: 数据项
            questionnaire_data: 问卷数据
            
        Returns:
            list: 问题列表
        """
        # 直接从item获取问题
        if 'questions' in item and item['questions']:
            questions = item['questions']
            if isinstance(questionnaire_data, dict):
                questionnaire_data['questions'] = questions
            logger.info(f"成功获取问卷问题，共 {len(questions)} 个问题")
            return questions
            
        # 从模板获取问题
        if ('template' in item and isinstance(item['template'], dict) and 
            'questions' in item['template']):
            questions = item['template']['questions']
            if isinstance(questionnaire_data, dict):
                questionnaire_data['questions'] = questions
            logger.info(f"成功从模板获取问卷问题，共 {len(questions)} 个问题")
            return questions
            
        return []
    
    def _try_detail_questions(self, questionnaire_id, questionnaire_data):
        """尝试从详情获取问题
        
        Args:
            questionnaire_id: 问卷ID
            questionnaire_data: 问卷数据
            
        Returns:
            list: 问题列表
        """
        logger.info(f"尝试获取问卷详情")
        result = self.cloud_api.get_questionnaire_detail(questionnaire_id)
        
        if not (result and isinstance(result, dict)):
            return []
            
        # 检查详情中的问题
        if 'questions' in result:
            questions = result['questions']
            if isinstance(questionnaire_data, dict):
                questionnaire_data['questions'] = questions
            logger.info(f"从问卷详情获取问题，共 {len(questions)} 个问题")
            return questions
            
        # 检查详情中模板的问题
        if ('template' in result and isinstance(result['template'], dict) and 
            'questions' in result['template']):
            questions = result['template']['questions']
            if isinstance(questionnaire_data, dict):
                questionnaire_data['questions'] = questions
            logger.info(f"从问卷详情模板获取问题，共 {len(questions)} 个问题")
            return questions
            
        # 检查data字段
        if 'data' in result and isinstance(result['data'], dict):
            return self._extract_questions_from_data(result['data'], questionnaire_data)
            
        return []
    
    def _extract_questions_from_data(self, data, questionnaire_data):
        """从data字段提取问题
        
        Args:
            data: 数据字段
            questionnaire_data: 问卷数据
            
        Returns:
            list: 问题列表
        """
        # 直接从data获取问题
        if 'questions' in data:
            questions = data['questions']
            if isinstance(questionnaire_data, dict):
                questionnaire_data['questions'] = questions
            logger.info(f"从问卷详情data获取问题，共 {len(questions)} 个问题")
            return questions
            
        # 从data的模板获取问题
        if ('template' in data and isinstance(data['template'], dict) and 
            'questions' in data['template']):
            questions = data['template']['questions']
            if isinstance(questionnaire_data, dict):
                questionnaire_data['questions'] = questions
            logger.info(f"从问卷详情data模板获取问题，共 {len(questions)} 个问题")
            return questions
            
        return []
    
    def _ensure_questionnaire_has_questions(self, questionnaire_data, questions):
        """确保问卷数据包含问题列表
        
        Args:
            questionnaire_data: 问卷数据
            questions: 问题列表
        """
        if questions and isinstance(questionnaire_data, dict):
            questionnaire_data['questions'] = questions
            logger.info(f"最终问卷包含 {len(questions)} 个问题")
        else:
            logger.warning("未能获取到问卷问题列表，将在表单页面显示空状态")
            self._create_default_questions(questionnaire_data)
    
    def _create_default_questions(self, questionnaire_data):
        """创建默认问题
        
        Args:
            questionnaire_data: 问卷数据
        """
        if not (isinstance(questionnaire_data, dict) and 
                questionnaire_data.get('question_count', 0) > 0):
            return
            
        count = questionnaire_data.get('question_count', 0)
        if count is None:
            return
            
        default_questions = []
        for i in range(count):
            default_questions.append({
                'id': i + 1,
                'question_id': f'q_{i+1}',
                'text': f'问题 {i + 1}',
                'question_text': f'问题 {i + 1}',
                'type': 'radio',
                'question_type': 'radio',
                'options': [
                    {'value': '1', 'text': '选项1', 'label': '选项1'},
                    {'value': '2', 'text': '选项2', 'label': '选项2'},
                    {'value': '3', 'text': '选项3', 'label': '选项3'},
                    {'value': '4', 'text': '选项4', 'label': '选项4'},
                    {'value': '5', 'text': '选项5', 'label': '选项5'}
                ],
                'order': i,
                'is_required': True
            })
        questionnaire_data['questions'] = default_questions
        logger.info(f"创建了 {len(default_questions)} 个默认问题")
    
    def _navigate_to_questionnaire_form(self, questionnaire_data):
        """导航到问卷表单页面
        
        Args:
            questionnaire_data: 问卷数据
        """
        app = MDApp.get_running_app()
        if app:
            app.questionnaire_to_fill = questionnaire_data
        
        self.manager.current = "questionnaire_form_screen"

    def update_assessment_list(self):
        """更新评估量表列表UI"""
        try:
            logger.info(f"更新评估量表列表UI，数量: {len(self.assessments)}")

            # 清空现有列表
            # 尝试查找不同的ID命名
            if hasattr(self.ids, 'assessment_list_container'):
                self.ids.assessment_list_container.clear_widgets()
            elif hasattr(self.ids, 'assessment_list'):
                self.ids.assessment_list.clear_widgets()
            else:
                logger.error("找不到assessment_list_container或assessment_list控件")
                return

            # 如果没有评估量表，显示空状态
            if not self.assessments:
                empty_label = MDLabel(
                    text="暂无评估量表",
                    theme_text_color="Secondary",
                    halign="center",
                    size_hint_y=None,
                    height=dp(100)
                )
                # 尝试找到正确的容器
                if hasattr(self.ids, 'assessment_list_container'):
                    self.ids.assessment_list_container.add_widget(empty_label)
                elif hasattr(self.ids, 'assessment_list'):
                    self.ids.assessment_list.add_widget(empty_label)
                return

            # 添加评估量表卡片
            for assessment in self.assessments:
                # 获取评估量表信息
                if isinstance(assessment, dict):
                    assessment_id = assessment.get('id')
                    title = assessment.get('title') or assessment.get('name') or "未命名量表"
                    description = assessment.get('description') or assessment.get('notes') or ""

                    # 获取模板信息（如果有）
                    template = assessment.get('template', {})
                    if isinstance(template, dict):
                        if not title or title == "未命名量表":
                            title = template.get('name', "未命名量表")
                        if not description:
                            description = template.get('description', "")

            logger.info(f"评估量表列表UI更新完成，添加了 {len(self.assessments)} 个卡片")
        except Exception as e:
            logger.error(f"更新评估量表列表UI时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def update_questionnaire_list(self):
        """更新问卷列表UI"""
        try:
            logger.info(f"更新问卷列表UI，数量: {len(self.questionnaires)}")

            # 清空现有列表
            # 尝试查找不同的ID命名
            if hasattr(self.ids, 'questionnaire_list_container'):
                self.ids.questionnaire_list_container.clear_widgets()
            elif hasattr(self.ids, 'questionnaire_list'):
                self.ids.questionnaire_list.clear_widgets()
            else:
                logger.error("找不到questionnaire_list_container或questionnaire_list控件")
                return

            # 如果没有问卷，显示空状态
            if not self.questionnaires:
                empty_label = MDLabel(
                    text="暂无问卷",
                    theme_text_color="Secondary",
                    halign="center",
                    size_hint_y=None,
                    height=dp(100)
                )
                # 尝试找到正确的容器
                if hasattr(self.ids, 'questionnaire_list_container'):
                    self.ids.questionnaire_list_container.add_widget(empty_label)
                elif hasattr(self.ids, 'questionnaire_list'):
                    self.ids.questionnaire_list.add_widget(empty_label)
                return

            # 添加问卷卡片
            for questionnaire in self.questionnaires:
                # 获取问卷信息
                if isinstance(questionnaire, dict):
                    questionnaire_id = questionnaire.get('id')
                    title = questionnaire.get('title') or questionnaire.get('name') or "未命名问卷"
                    description = questionnaire.get('description') or questionnaire.get('notes') or ""

                    # 获取模板信息（如果有）
                    template = questionnaire.get('template', {})
                    if isinstance(template, dict):
                        if not title or title == "未命名问卷":
                            title = template.get('name', "未命名问卷")
                        if not description:
                            description = template.get('description', "")

            logger.info(f"问卷列表UI更新完成，添加了 {len(self.questionnaires)} 个卡片")
        except Exception as e:
            logger.error(f"更新问卷列表UI时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def refresh_data(self):
        """刷新数据 - 重新加载评估量表和问卷数据"""
        try:
            logger.info("开始刷新调查问卷/评估量表数据")
            # 重新加载评估量表
            self.load_assessments()
            # 重新加载问卷
            self.load_questionnaires()
            # 显示刷新成功提示
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="数据已刷新"))
            snackbar.open()
            logger.info("调查问卷/评估量表数据刷新完成")
        except Exception as e:
            logger.error(f"刷新数据失败: {str(e)}")
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text=f"刷新失败: {str(e)}"))
            snackbar.open()

    def show_error(self, message):
        """显示错误信息"""
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                bg_color=(0.8, 0.2, 0.2, 1)  # 红色背景
            )
            snackbar.open()
        except Exception as e:
            logger.error(f"显示错误信息失败: {e}")

# 在类定义后注册Factory
Factory.register('SurveyScreen', cls=SurveyScreen)
Builder.load_string(KV)