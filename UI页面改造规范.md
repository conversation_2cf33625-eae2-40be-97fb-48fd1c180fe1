# UI页面改造规范

## 1. 概述

本文档旨在规范移动端UI页面的改造工作，确保所有页面遵循统一的设计模式和架构规范。通过继承BaseScreen基类，所有页面将具有一致的布局结构，包括顶端容器（顶端导航栏+Logo）、内容容器和底端容器（底端导航栏）。

## 2. BaseScreen基类设计规范

### 2.1 基类结构
BaseScreen基类定义了统一的页面结构：
- **顶端容器(header_container)**：包含顶端导航栏和Logo
- **内容容器(content_container)**：位于ScrollView中，用于放置页面主要内容
- **底端容器(nav_bar_container)**：包含底部导航栏

### 2.2 继承规范
所有页面必须继承BaseScreen基类，并遵循以下规范：
1. 在类初始化时设置页面标题和导航栏属性
2. 通过覆盖`do_content_setup`方法在content_container中添加页面内容
3. 不要直接修改BaseScreen的KV模板结构

### 2.3 初始化流程
页面初始化遵循以下流程：
1. `__init__`方法：设置页面基本属性
2. `on_pre_enter`方法：调用`init_ui`进行UI初始化
3. `init_ui`方法：调用父类初始化，设置导航栏、Logo和底部导航
4. `do_content_setup`方法：子类覆盖此方法以添加具体内容

## 3. 页面布局规范

### 3.1 顶端容器（顶端导航栏+Logo）
- **顶端导航栏**：由BaseScreen自动添加，显示页面标题和操作按钮
- **Logo**：由BaseScreen自动添加，位于顶端导航栏下方
- **高度管理**：BaseScreen会自动管理顶端容器的高度

### 3.2 内容容器
- **滚动支持**：BaseScreen已在content_container外层添加了ScrollView，页面无需再次添加
- **布局方式**：使用MDBoxLayout垂直布局
- **内边距**：BaseScreen已设置默认内边距，页面可根据需要调整
- **内容添加**：在`do_content_setup`方法中向content_container添加内容

### 3.3 底端容器（底端导航栏）
- **自动添加**：BaseScreen会自动添加底部导航栏
- **导航项配置**：通过nav_buttons_config属性配置导航项
- **状态管理**：BaseScreen会自动管理当前页面的导航项激活状态

## 4. 页面内容设计规范

### 4.1 do_content_setup方法规范
所有页面内容必须在`do_content_setup`方法中添加，遵循以下规范：
1. 获取content_container引用
2. 清空现有内容
3. 创建主布局容器
4. 向主布局容器添加页面组件
5. 将主布局容器添加到content_container

### 4.2 组件设计规范
- **卡片组件**：使用MDCard作为内容区域的容器
- **布局组件**：优先使用MDBoxLayout和MDGridLayout
- **文本组件**：使用MDLabel显示文本内容
- **按钮组件**：使用MDIconButton、MDButton等KivyMD组件
- **自定义组件**：为可复用的UI元素创建自定义组件类，继承自KivyMD组件

### 4.3 响应式设计
- **高度管理**：使用bind(minimum_height=setter('height'))确保组件高度自适应内容
- **尺寸单位**：使用dp()函数确保在不同屏幕密度下的显示一致性
- **方向管理**：合理使用size_hint和固定尺寸

### 4.4 卡片组件设计规范
- **问题背景**：卡片是UI设计中的重要元素，需要统一的设计规范
- **设计要求**：
  1. 统一设置卡片的圆角（radius）、阴影（elevation）和内边距（padding）
  2. 合理使用背景颜色，通过主题获取或提供默认值
  3. 正确绑定minimum_height以支持内容自适应
  4. 卡片内组件间距保持一致性

## 5. 主题和样式规范

### 5.1 主题使用
- **主题颜色**：通过app.theme获取主题颜色
- **颜色回退**：提供默认颜色值以防主题不可用
- **文本样式**：使用KivyMD的font_style和role属性

### 5.2 样式一致性
- **圆角设置**：统一使用radius属性设置圆角
- **阴影效果**：统一使用elevation属性设置阴影
- **内边距**：统一使用padding属性设置内边距

### 5.3 组件引用安全访问
- **问题背景**：直接通过self.ids访问组件时，可能会出现组件未找到或为None的情况
- **解决方案**：
  1. 使用getattr(self, 'component_name', None)方式安全访问组件实例
  2. 在访问组件前检查组件是否为None
  3. 对于关键组件，提供备用方案或错误处理机制

## 6. 功能实现规范

### 6.1 导航功能
- **页面跳转**：使用self.manager.current进行页面跳转
- **导航参数**：通过页面属性传递导航参数
- **返回处理**：实现on_back方法处理返回逻辑
- **动态页面创建**：在导航前检查目标页面是否已存在，如不存在则动态创建并添加到屏幕管理器

### 6.2 数据加载
- **初始化加载**：在on_enter方法中加载页面数据
- **刷新功能**：实现refresh_data方法处理数据刷新
- **异步加载**：使用线程或调度器进行异步数据加载
- **用户反馈**：在数据加载过程中提供进度提示，加载完成后给出完成提示

### 6.3 用户交互
- **事件绑定**：使用bind方法绑定事件处理函数
- **回调函数**：确保回调函数的可用性和安全性
- **错误处理**：对所有用户交互进行异常处理

### 6.4 回调函数安全处理
- **问题背景**：在实际开发中，经常出现回调函数不可用或被意外覆盖的情况
- **解决方案**：
  1. 在组件初始化时为on_release等事件属性设置默认回调函数
  2. 在事件处理方法中检查回调函数是否可用（callable检查）
  3. 使用try-except包装回调函数调用，防止程序崩溃
  4. 提供日志记录，便于调试回调函数问题

## 7. 代码质量规范

### 7.1 代码结构
- **模块导入**：按功能分组导入模块
- **常量定义**：将魔法数值定义为常量
- **函数划分**：按功能划分函数，保持函数单一职责

### 7.2 错误处理
- **异常捕获**：对关键操作进行异常捕获
- **日志记录**：使用Logger记录操作日志
- **用户提示**：通过Snackbar等方式向用户提示操作结果

### 7.3 性能优化
- **重复初始化检查**：使用标志位避免重复初始化
- **资源释放**：及时释放不需要的资源
- **延迟加载**：对非关键内容进行延迟加载

### 7.4 登录状态检查
- **问题背景**：页面访问时需要验证用户登录状态，未登录用户应重定向到登录页面
- **解决方案**：
  1. 在on_enter方法中检查用户登录状态
  2. 未登录时自动跳转到登录页面
  3. 提供友好的提示信息

### 7.5 数据验证规范
- **问题背景**：在保存用户输入的数据前需要进行验证，确保数据的完整性和正确性
- **解决方案**：
  1. 实现数据验证方法，检查必填字段
  2. 验证数据格式和范围
  3. 提供清晰的错误提示信息

### 7.6 屏幕管理规范
- **问题背景**：在多页面应用中，需要有效管理屏幕实例，避免重复创建和内存泄漏
- **解决方案**：
  1. 在导航前检查目标屏幕是否已存在
  2. 仅在目标屏幕不存在时才创建新实例
  3. 合理设置屏幕过渡动画方向

## 8. 示例代码

### 8.1 页面类定义
```
class ExampleScreen(BaseScreen):
    def __init__(self, **kwargs):
        # 设置页面标题
        kwargs['screen_title'] = '示例页面'
        # 设置是否显示顶端导航栏
        kwargs['show_top_bar'] = True
        # 设置顶端导航栏操作按钮图标
        kwargs['top_bar_action_icon'] = 'refresh'
        super().__init__(**kwargs)
        
        # 添加初始化状态标记
        self._ui_initialized = False
    
    def do_content_setup(self):
        """在content_container中添加内容"""
        # 安全地获取content_container
        content_container = None
        if hasattr(self, 'ids') and isinstance(self.ids, dict):
            content_container = self.ids.get('content_container')
        
        if not content_container:
            Logger.error(f"[ExampleScreen] ERROR: 无法找到content_container")
            return
        
        # 清空content_container中的现有内容
        content_container.clear_widgets()
        
        # 创建主布局
        main_layout = MDBoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=int(self.height if self.height > 0 else dp(800)),
            padding=[int(dp(16)), int(dp(16)), int(dp(16)), int(dp(100))],
            spacing=int(dp(20))
        )
        main_layout.bind(minimum_height=main_layout.setter('height'))
        
        # 添加内容组件
        # ...
        
        # 将主布局添加到content_container
        content_container.add_widget(main_layout)

    def on_enter(self, *args):
        """进入屏幕时调用"""
        # 强制校验登录状态
        app = MDApp.get_running_app()
        is_logged_in = False
        user_data = getattr(app, 'user_data', None)
        if user_data is not None and user_data.get('username'):
            try:
                from utils.cloud_api import get_cloud_api
                cloud_api = get_cloud_api()
                if cloud_api and cloud_api.is_authenticated():
                    is_logged_in = True
            except Exception as e:
                print(f"检查cloud_api认证状态时出错: {e}")

        if not is_logged_in:
            # 未登录或认证无效，强制跳转回登录页并提示
            if self.manager:
                self.manager.current = 'login_screen'
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="请先登录"))
            snackbar.open()
            return
```

### 8.2 组件设计
```
# 卡片组件设计
card = MDCard(
    orientation='vertical',
    size_hint_y=None,
    padding=[int(dp(16)), int(dp(16)), int(dp(16)), int(dp(16))],
    spacing=int(dp(12)),
    radius=[int(dp(16))],
    elevation=2,
    md_bg_color=getattr(self.app.theme, 'CARD_BACKGROUND', [0.95, 0.95, 0.95, 1]) if self.app and hasattr(self.app, 'theme') else [0.95, 0.95, 0.95, 1]
)
card.bind(minimum_height=card.setter('height'))

# 自定义组件类示例
class CustomCard(MDCard):
    icon = StringProperty("heart-pulse")
    title = StringProperty("功能")
    description = StringProperty("")
    icon_color = ListProperty(None)
    on_release = ObjectProperty(None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 初始化时确保on_release有一个默认值
        if self.on_release is None:
            self.on_release = self._default_on_release
        # 绑定MDCard的on_release事件
        self.bind(on_release=self._on_release)

    def _default_on_release(self, *args):
        """默认的on_release处理函数"""
        Logger.info(f"[CustomCard] 默认on_release处理函数被调用: {self.title}")

    def _on_release(self, instance):
        """处理MDCard的on_release事件"""
        # 确保on_release不为None
        if self.on_release is None:
            self.on_release = self._default_on_release
            
        # 更安全的事件处理方式
        if callable(self.on_release):
            try:
                self.on_release()
            except Exception as e:
                Logger.error(f"[CustomCard] on_release 调用失败: {e}")
        else:
            Logger.warning(f"[CustomCard] on_release 事件处理函数不可用: {self.title}")
```


## 9. 常见问题及解决方案

### 9.1 布局问题
- **问题**：组件高度不正确
- **解决方案**：使用bind(minimum_height=setter('height'))绑定高度，并确保在组件添加到父容器后再进行高度计算

### 9.2 主题问题
- **问题**：主题颜色获取失败
- **解决方案**：提供默认颜色值作为回退，使用getattr(self.app.theme, 'COLOR_NAME', [default_color])模式

### 9.3 导航问题
- **问题**：页面跳转失败
- **解决方案**：检查manager引用和目标页面名称，确保目标页面已添加到屏幕管理器

### 9.4 性能问题
- **问题**：页面加载缓慢
- **解决方案**：使用异步加载和延迟初始化，避免在UI线程进行耗时操作

### 9.5 组件访问问题
- **问题**：无法访问组件或组件为None
- **解决方案**：使用安全访问模式，如getattr(self, 'component_name', None)并检查None值

### 9.6 重复初始化问题
- **问题**：组件被重复初始化
- **解决方案**：使用初始化标志位（如self._ui_initialized）避免重复初始化
