# -*- coding: utf-8 -*-
"""
文件上传下载管理器 - KivyMD 2.0.1 dev0 兼容

统一管理所有文件上传下载功能，减少重复代码，实现功能模块化和标准化。
支持多种上传方式、文件验证、上传队列、进度显示等功能。

作者: Health-Trea Team
版本: 2.0.0
兼容: KivyMD 2.0.1 dev0
"""

import os
import json
import time
import logging
import threading
from typing import Optional, Dict, List, Callable, Tuple, Any
from pathlib import Path

from kivy.clock import Clock
from kivy.event import EventDispatcher
from kivy.properties import StringProperty, BooleanProperty, NumericProperty
from kivymd.uix.filemanager import MDFileManager
from kivymd.uix.snackbar import MDSnackbar
from kivymd.uix.label import MDLabel
from plyer import filechooser
from kivy.core.window import Window 

# 导入项目工具模块
from .cloud_api import get_cloud_api
from .user_manager import get_user_manager
from .toast import show_toast  # 使用跨平台兼容的toast实现
from mobile.theme import HealthAppTheme

# 配置日志
logger = logging.getLogger(__name__)

# 支持的文件格式配置
SUPPORTED_FILE_FORMATS = {
    # 文档格式
    'documents': {
        'extensions': ['.pdf', '.doc', '.docx', '.txt', '.rtf'],
        'mime_types': [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'application/rtf'
        ],
        'max_size': 50 * 1024 * 1024,  # 50MB
        'description': '文档文件 (PDF, DOC, DOCX, TXT, RTF)'
    },
    # 图片格式
    'images': {
        'extensions': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'],
        'mime_types': [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/bmp',
            'image/tiff',
            'image/webp'
        ],
        'max_size': 20 * 1024 * 1024,  # 20MB
        'description': '图片文件 (JPG, PNG, GIF, BMP, TIFF, WEBP)'
    },
    # 医疗文件格式
    'medical': {
        'extensions': ['.dcm', '.dicom', '.hl7'],
        'mime_types': [
            'application/dicom',
            'application/hl7-v2'
        ],
        'max_size': 100 * 1024 * 1024,  # 100MB
        'description': '医疗文件 (DICOM, HL7)'
    }
}

# 上传方式枚举
class UploadMethod:
    """上传方式枚举类"""
    DIRECT = 'direct_upload'  # 直接上传
    QRCODE = 'qr_upload'      # 二维码上传
    CAMERA = 'camera_upload'  # 拍照上传
    FILE_MANAGER = 'file_manager'  # 文件管理器选择

class FileUploadDownloadManager(EventDispatcher):
    """文件上传下载管理器
    
    统一管理所有文件上传下载功能的核心类。
    提供文件选择、验证、上传、下载、队列处理等完整功能。
    
    Events:
        on_upload_start: 上传开始事件
        on_upload_progress: 上传进度事件 (progress: float)
        on_upload_complete: 上传完成事件 (success: bool, result: dict)
        on_download_start: 下载开始事件
        on_download_progress: 下载进度事件 (progress: float)
        on_download_complete: 下载完成事件 (success: bool, file_path: str)
        on_file_selected: 文件选择事件 (file_path: str)
        on_error: 错误事件 (error_message: str)
    """
    
    # 事件注册
    __events__ = (
        'on_upload_start',
        'on_upload_progress', 
        'on_upload_complete',
        'on_download_start',
        'on_download_progress',
        'on_download_complete',
        'on_file_selected',
        'on_error'
    )
    
    # Kivy属性
    current_file_path = StringProperty(None, allownone=True)
    is_uploading = BooleanProperty(False)
    is_downloading = BooleanProperty(False)
    upload_progress = NumericProperty(0.0)
    download_progress = NumericProperty(0.0)
    
    def __init__(self, **kwargs):
        """初始化文件上传下载管理器
        
        Args:
            **kwargs: 其他参数
        """
        super().__init__(**kwargs)
        
        # 初始化组件
        self.cloud_api = get_cloud_api()
        self.user_manager = get_user_manager()
        self.theme = HealthAppTheme()
        
        # 文件管理相关目录
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.downloads_dir = os.path.join(self.base_dir, "downloads")
        if not os.path.exists(self.downloads_dir):
            os.makedirs(self.downloads_dir)
        
        # 文件管理器
        self.file_manager = None
        self._init_file_manager()
        
        # 上传队列相关
        self.upload_queue = []
        self.queue_lock = threading.Lock()
        
        # 回调函数
        self.upload_callback = None
        self.download_callback = None
        self.progress_callback = None
        
        # 当前操作状态
        self.current_operation = None
        self.last_error = None
        
        logger.info("文件上传下载管理器初始化完成")
    
    def _init_file_manager(self):
        """初始化MDFileManager组件"""
        try:
            self.file_manager = MDFileManager(
                exit_manager=self._file_manager_close,
                select_path=self._file_manager_select_path,
                preview=True,
                ext=self._get_all_supported_extensions()
            )
            # 绑定模态事件（遵循 KivyMD 2.0.1 dev0 规范）
            self.file_manager.bind(on_pre_dismiss=self._on_pre_dismiss,
                                   on_dismiss=self._on_dismiss)
            logger.debug("MDFileManager初始化成功")
        except Exception as e:
            logger.error(f"初始化MDFileManager失败: {str(e)}")
            self.file_manager = None
    
    def _get_all_supported_extensions(self) -> List[str]:
        """获取所有支持的文件扩展名
        
        Returns:
            List[str]: 支持的文件扩展名列表
        """
        extensions = []
        for category in SUPPORTED_FILE_FORMATS.values():
            extensions.extend(category['extensions'])
        return extensions
    
    def _on_pre_dismiss(self, *args):
        """预关闭事件处理（可选，增强模态监控）"""
        logger.info("文件管理器预关闭")

    def _on_dismiss(self, *args):
        """关闭事件处理（可选，清理资源）"""
        logger.info("文件管理器已关闭")
        # 可在此重置状态

    def _file_manager_close(self, *args):  # 添加 *args 以兼容 exit_manager 回调
        """文件管理器关闭回调"""
        if self.file_manager:
            try:
                self.file_manager.close()  # 标准调用，遵循 KivyMD 2.0.1 dev0
                logger.info("退出文件管理器")  # 添加匹配用户日志的 INFO
            except AttributeError as e:
                logger.warning(f"[关闭文件管理器异常] {str(e)}")  # 匹配用户 WARNING 日志
                # 回退：手动移除小部件，确保关闭（兼容非模态情况）
                if self.file_manager in Window.children:
                    Window.remove_widget(self.file_manager)
            except Exception as e:
                logger.error(f"关闭文件管理器失败: {str(e)}")
    
    def _file_manager_select_path(self, path: str):
        """文件管理器选择文件回调
        
        Args:
            path (str): 选择的文件路径
        """
        self.current_file_path = path
        self.dispatch('on_file_selected', path)
        self._file_manager_close()
        logger.info(f"用户选择文件: {path}")
    
    # ==================== 文件选择方法 ====================
    
    def open_file_manager(self, path: str = None) -> bool:
        """打开文件管理器
        
        Args:
            path (str, optional): 初始路径
            
        Returns:
            bool: 是否成功打开文件管理器
        """
        try:
            if not self.file_manager:
                self._init_file_manager()
            if not self.file_manager:
                self._show_error("文件管理器初始化失败")
                return False
            if not path:
                path = os.path.expanduser('~')
            self.file_manager.show(path)
            logger.info(f"打开文件管理器，初始路径: {path}")
            return True
        except Exception as e:
            error_msg = f"打开文件管理器失败: {str(e)}"
            logger.error(error_msg)
            self._show_error(error_msg)
            return False
    
    def select_file_with_plyer(self, file_types: List[str] = None) -> bool:
        """使用plyer选择文件
        
        Args:
            file_types (List[str], optional): 文件类型过滤器
            
        Returns:
            bool: 是否成功打开文件选择器
        """
        try:
            # 准备文件类型过滤器
            if not file_types:
                file_types = self._get_all_supported_extensions()
            
            # 使用plyer选择文件
            def on_selection(selection):
                if selection:
                    file_path = selection[0]
                    self.current_file_path = file_path
                    self.dispatch('on_file_selected', file_path)
                    logger.info(f"用户通过plyer选择文件: {file_path}")
            
            filechooser.open_file(
                on_selection=on_selection,
                filters=[("支持的文件", "*" + ";".join(file_types))]
            )
            return True
            
        except Exception as e:
            error_msg = f"打开文件选择器失败: {str(e)}"
            logger.error(error_msg)
            self._show_error(error_msg)
            return False
    
    # ==================== 文件验证方法 ====================
    
    def validate_file(self, file_path: str, category: str = None) -> Tuple[bool, str]:
        """验证文件是否符合要求
        
        Args:
            file_path (str): 文件路径
            category (str, optional): 文件类别，如果不指定则自动检测
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return False, "文件不存在"
            
            # 检查是否为文件
            if not os.path.isfile(file_path):
                return False, "选择的不是文件"
            
            # 获取文件信息
            file_size = os.path.getsize(file_path)
            file_ext = os.path.splitext(file_path)[1].lower()
            
            # 如果没有指定类别，自动检测
            if not category:
                category = self._detect_file_category(file_ext)
                
            if not category:
                return False, f"不支持的文件格式: {file_ext}"
            
            # 获取类别配置
            category_config = SUPPORTED_FILE_FORMATS.get(category)
            if not category_config:
                return False, f"未知的文件类别: {category}"
            
            # 检查文件扩展名
            if file_ext not in category_config['extensions']:
                return False, f"不支持的文件格式: {file_ext}"
            
            # 检查文件大小
            max_size = category_config['max_size']
            if file_size > max_size:
                max_size_mb = max_size / (1024 * 1024)
                current_size_mb = file_size / (1024 * 1024)
                return False, f"文件过大: {current_size_mb:.1f}MB，最大允许: {max_size_mb:.1f}MB"
            
            # 检查文件是否可读
            try:
                with open(file_path, 'rb') as f:
                    f.read(1024)  # 尝试读取前1KB
            except Exception as e:
                return False, f"文件无法读取: {str(e)}"
            
            logger.info(f"文件验证通过: {file_path} ({category})")
            return True, ""
            
        except Exception as e:
            error_msg = f"文件验证失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def _detect_file_category(self, file_ext: str) -> Optional[str]:
        """根据文件扩展名检测文件类别
        
        Args:
            file_ext (str): 文件扩展名
            
        Returns:
            Optional[str]: 文件类别，如果无法检测则返回None
        """
        file_ext = file_ext.lower()
        for category, config in SUPPORTED_FILE_FORMATS.items():
            if file_ext in config['extensions']:
                return category
        return None
    
    # ==================== 文件上传方法 ====================
    
    def upload_file(self, file_path: str = None, metadata: Dict = None, 
                   callback: Callable = None, method: str = UploadMethod.DIRECT) -> bool:
        """上传文件
        
        Args:
            file_path (str, optional): 文件路径，如果不指定则使用当前选择的文件
            metadata (Dict, optional): 文件元数据
            callback (Callable, optional): 上传完成回调函数
            method (str): 上传方式
            
        Returns:
            bool: 是否成功开始上传
        """
        try:
            # 使用指定文件或当前选择的文件
            target_file = file_path or self.current_file_path
            if not target_file:
                self._show_error("请先选择要上传的文件")
                return False
            
            # 验证文件
            is_valid, error_msg = self.validate_file(target_file)
            if not is_valid:
                self._show_error(f"文件验证失败: {error_msg}")
                return False
            
            # 检查用户认证
            if not self._check_authentication():
                return False
            
            # 设置回调
            if callback:
                self.upload_callback = callback
            
            # 准备元数据
            upload_metadata = self._prepare_upload_metadata(target_file, metadata, method)
            
            # 开始上传
            self.is_uploading = True
            self.upload_progress = 0.0
            self.current_operation = 'upload'
            self.dispatch('on_upload_start')
            
            # 在后台线程中执行上传
            threading.Thread(
                target=self._upload_file_thread,
                args=(target_file, upload_metadata),
                daemon=True
            ).start()
            
            logger.info(f"开始上传文件: {target_file} (方式: {method})")
            return True
            
        except Exception as e:
            error_msg = f"上传文件失败: {str(e)}"
            logger.error(error_msg)
            self._show_error(error_msg)
            return False
    
    def _upload_file_thread(self, file_path: str, metadata: Dict):
        """上传文件的后台线程
        
        Args:
            file_path (str): 文件路径
            metadata (Dict): 文件元数据
        """
        try:
            # 调用云API上传文件（CloudAPI.upload_file不支持progress_callback参数）
            result = self.cloud_api.upload_file(
                file_path, 
                metadata
            )
            
            Clock.schedule_once(lambda dt: self._handle_upload_result(result, file_path), 0)
            
        except Exception as e:
            error_msg = f"上传文件时出错: {str(e)}"
            logger.error(error_msg)
            Clock.schedule_once(
                lambda dt: self._handle_upload_result(False, file_path, error_msg),
                0
            )
    
    def _update_upload_progress(self, progress):
        """更新上传进度
        
        Args:
            progress (float): 上传进度 (0-100)
        """
        self.upload_progress = progress
        self.dispatch('on_upload_progress', progress)
    
    def _handle_upload_result(self, result: Any, file_path: str, error_msg: str = None):
        """处理上传结果
        
        Args:
            result: 上传结果
            file_path (str): 文件路径
            error_msg (str, optional): 错误信息
        """
        self.is_uploading = False
        self.upload_progress = 100.0 if result else 0.0
        self.current_operation = None
        
        success = bool(result)
        
        if success:
            logger.info(f"文件上传成功: {file_path}")
            self._show_success("文件上传成功")
        else:
            error = error_msg or getattr(self.cloud_api, 'last_error', '未知错误')
            logger.error(f"文件上传失败: {file_path}, 错误: {error}")
            self._show_error(f"文件上传失败: {error}")
        
        # 触发事件
        self.dispatch('on_upload_complete', success, result or {})
        
        # 调用回调函数
        if self.upload_callback:
            try:
                self.upload_callback(success, result)
            except Exception as e:
                logger.error(f"上传回调函数执行失败: {str(e)}")
    
    def _prepare_upload_metadata(self, file_path: str, metadata: Dict = None, 
                               method: str = UploadMethod.DIRECT) -> Dict:
        """准备上传元数据
        
        Args:
            file_path (str): 文件路径
            metadata (Dict, optional): 用户提供的元数据
            method (str): 上传方式
            
        Returns:
            Dict: 完整的上传元数据
        """
        # 基础元数据
        base_metadata = {
            'upload_method': method,
            'upload_time': int(time.time()),
            'file_name': os.path.basename(file_path),
            'file_size': os.path.getsize(file_path),
            'file_extension': os.path.splitext(file_path)[1].lower()
        }
        
        # 添加用户ID
        if hasattr(self.user_manager, 'get_current_user_custom_id'):
            custom_id = self.user_manager.get_current_user_custom_id()
            if custom_id:
                base_metadata['user_id'] = custom_id
        
        # 合并用户提供的元数据
        if metadata:
            base_metadata.update(metadata)
        
        return base_metadata
    
    # ==================== 文件下载方法 ====================
    
    def download_file(self, document_id: str, save_path: str = None, 
                     callback: Callable = None) -> bool:
        """下载文件
        
        Args:
            document_id (str): 文档ID
            save_path (str, optional): 保存路径，如果不指定则使用默认路径
            callback (Callable, optional): 下载完成回调函数
            
        Returns:
            bool: 是否成功开始下载
        """
        try:
            # 检查用户认证
            if not self._check_authentication():
                return False
            
            # 准备保存路径
            if not save_path:
                save_path = self._generate_download_path(document_id)
            
            # 确保保存目录存在
            save_dir = os.path.dirname(save_path)
            if not os.path.exists(save_dir):
                os.makedirs(save_dir, exist_ok=True)
            
            # 设置回调
            if callback:
                self.download_callback = callback
            
            # 开始下载
            self.is_downloading = True
            self.download_progress = 0.0
            self.current_operation = 'download'
            self.dispatch('on_download_start')
            
            # 在后台线程中执行下载
            threading.Thread(
                target=self._download_file_thread,
                args=(document_id, save_path),
                daemon=True
            ).start()
            
            logger.info(f"开始下载文件: {document_id} -> {save_path}")
            return True
            
        except Exception as e:
            error_msg = f"下载文件失败: {str(e)}"
            logger.error(error_msg)
            self._show_error(error_msg)
            return False
    
    def _download_file_thread(self, document_id: str, save_path: str):
        """下载文件的后台线程
        
        Args:
            document_id (str): 文档ID
            save_path (str): 保存路径
        """
        try:
            # 调用云API下载文件（CloudAPI.download_document不支持progress_callback参数）
            result = self.cloud_api.download_document(
                document_id, 
                save_path
            )
            
            # 在主线程中处理结果
            Clock.schedule_once(
                lambda dt: self._handle_download_result(result, save_path),
                0
            )
            
        except Exception as e:
            error_msg = f"下载文件时出错: {str(e)}"
            logger.error(error_msg)
            Clock.schedule_once(
                lambda dt: self._handle_download_result(False, save_path, error_msg),
                0
            )
    
    def _update_download_progress(self, progress):
        """更新下载进度
        
        Args:
            progress (float): 下载进度 (0-100)
        """
        self.download_progress = progress
        self.dispatch('on_download_progress', progress)
    
    def _handle_download_result(self, result: bool, save_path: str, error_msg: str = None):
        """处理下载结果
        
        Args:
            result (bool): 下载结果
            save_path (str): 保存路径
            error_msg (str, optional): 错误信息
        """
        self.is_downloading = False
        self.download_progress = 100.0 if result else 0.0
        self.current_operation = None
        
        if result:
            logger.info(f"文件下载成功: {save_path}")
            self._show_success(f"文件下载成功: {os.path.basename(save_path)}")
        else:
            error = error_msg or getattr(self.cloud_api, 'last_error', '未知错误')
            logger.error(f"文件下载失败: {save_path}, 错误: {error}")
            self._show_error(f"文件下载失败: {error}")
        
        # 触发事件
        self.dispatch('on_download_complete', result, save_path if result else "")
        
        # 调用回调函数
        if self.download_callback:
            try:
                self.download_callback(result, save_path)
            except Exception as e:
                logger.error(f"下载回调函数执行失败: {str(e)}")
    
    def _generate_download_path(self, document_id: str) -> str:
        """生成下载文件路径
        
        Args:
            document_id (str): 文档ID
            
        Returns:
            str: 下载文件路径
        """
        # 生成下载文件路径
        filename = f"document_{document_id}_{int(time.time())}"
        return os.path.join(self.downloads_dir, filename)
    
    # ==================== 便捷上传方法 ====================
    
    def upload_file_with_manager(self, file_types: List[str] = None, metadata: Dict = None) -> bool:
        """通过文件管理器选择并上传文件
        
        Args:
            file_types (List[str], optional): 文件类型过滤器
            metadata (Dict, optional): 文件元数据
            
        Returns:
            bool: 是否成功打开文件管理器
        """
        # 绑定文件选择事件
        def on_file_selected(file_path):
            self.upload_file(file_path, metadata)
        
        self.bind(on_file_selected=lambda instance, path: on_file_selected(path))
        
        # 打开文件管理器
        return self.open_file_manager()
    
    def upload_file_with_camera(self, metadata: Dict = None) -> bool:
        """通过相机拍照并上传
        
        Args:
            metadata (Dict, optional): 文件元数据
            
        Returns:
            bool: 是否成功启动相机
        """
        try:
            # 模拟相机功能
            self._show_info("相机功能待实现")
            return False
        except Exception as e:
            error_msg = f"启动相机失败: {str(e)}"
            logger.error(error_msg)
            self._show_error(error_msg)
            return False
    
    def upload_file_with_qr(self, metadata: Dict = None) -> bool:
        """通过二维码扫描并上传
        
        Args:
            metadata (Dict, optional): 文件元数据
            
        Returns:
            bool: 是否成功启动二维码扫描
        """
        try:
            # 模拟二维码扫描功能
            self._show_info("二维码扫描功能待实现")
            return False
        except Exception as e:
            error_msg = f"启动二维码扫描失败: {str(e)}"
            logger.error(error_msg)
            self._show_error(error_msg)
            return False
    
    # ==================== 上传队列方法 ====================
    
    def add_to_upload_queue(self, file_path: str, metadata: Dict = None) -> bool:
        """添加文件到上传队列
        
        Args:
            file_path (str): 文件路径
            metadata (Dict, optional): 文件元数据
            
        Returns:
            bool: 是否成功添加到队列
        """
        try:
            # 验证文件
            is_valid, error_msg = self.validate_file(file_path)
            if not is_valid:
                self._show_error(f"无法添加到队列: {error_msg}")
                return False
            
            # 调用云API添加到队列
            result = self.cloud_api.add_to_upload_queue(file_path, metadata)
            
            if result:
                logger.info(f"文件已添加到上传队列: {file_path}")
                self._show_info("文件已添加到上传队列，将在网络恢复后自动上传")
                return True
            else:
                error = getattr(self.cloud_api, 'last_error', '未知错误')
                self._show_error(f"添加到上传队列失败: {error}")
                return False
                
        except Exception as e:
            error_msg = f"添加到上传队列失败: {str(e)}"
            logger.error(error_msg)
            self._show_error(error_msg)
            return False
    
    def process_upload_queue(self, max_items: int = 5) -> Tuple[int, int]:
        """处理上传队列
        
        Args:
            max_items (int): 最大处理项数
            
        Returns:
            Tuple[int, int]: (成功数量, 失败数量)
        """
        try:
            # 检查用户认证
            if not self._check_authentication():
                return 0, 0
            
            # 调用云API处理队列
            success_count, fail_count = self.cloud_api.process_upload_queue(max_items)
            
            if success_count > 0 or fail_count > 0:
                message = f"队列处理完成: {success_count}个成功, {fail_count}个失败"
                logger.info(message)
                self._show_info(message)
            
            return success_count, fail_count
            
        except Exception as e:
            error_msg = f"处理上传队列失败: {str(e)}"
            logger.error(error_msg)
            self._show_error(error_msg)
            return 0, 0
    
    # ==================== 辅助方法 ====================
    
    def _check_authentication(self) -> bool:
        """检查用户认证状态
        
        Returns:
            bool: 是否已认证
        """
        try:
            # 检查云API认证状态
            if not self.cloud_api.is_authenticated():
                # 尝试加载认证信息
                self.cloud_api.load_auth_info()
                
                # 再次检查
                if not self.cloud_api.is_authenticated():
                    self._show_error("用户未登录，请先登录")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查认证状态失败: {str(e)}")
            self._show_error("认证检查失败")
            return False
    
    def _show_success(self, message: str):
        """显示成功消息
        
        Args:
            message (str): 消息内容
        """
        try:
            show_toast(message)
            logger.info(f"成功消息: {message}")
        except Exception as e:
            logger.error(f"显示成功消息失败: {str(e)}")
    
    def _show_info(self, message: str):
        """显示信息消息
        
        Args:
            message (str): 消息内容
        """
        try:
            show_toast(message)
            logger.info(f"信息消息: {message}")
        except Exception as e:
            logger.error(f"显示信息消息失败: {str(e)}")
    
    def _show_error(self, message: str):
        """显示错误消息
        
        Args:
            message (str): 错误消息
        """
        try:
            self.last_error = message
            self.dispatch('on_error', message)
            show_toast(f"错误: {message}")
            logger.error(f"错误消息: {message}")
        except Exception as e:
            logger.error(f"显示错误消息失败: {str(e)}")
    
    def get_supported_formats_info(self) -> Dict[str, Dict]:
        """获取支持的文件格式信息
        
        Returns:
            Dict[str, Dict]: 支持的文件格式信息
        """
        return SUPPORTED_FILE_FORMATS.copy()
    
    def get_upload_methods(self) -> List[str]:
        """获取支持的上传方式
        
        Returns:
            List[str]: 支持的上传方式列表
        """
        return [UploadMethod.DIRECT, UploadMethod.QRCODE, UploadMethod.CAMERA, UploadMethod.FILE_MANAGER]
    
    def reset_state(self):
        """重置管理器状态"""
        self.current_file_path = None
        self.is_uploading = False
        self.is_downloading = False
        self.upload_progress = 0.0
        self.download_progress = 0.0
        self.current_operation = None
        self.last_error = None
        self.upload_callback = None
        self.download_callback = None
        logger.info("文件上传下载管理器状态已重置")
    
    # ==================== 事件处理方法 ====================
    
    def on_upload_start(self, *args):
        """上传开始事件处理"""
        pass
    
    def on_upload_progress(self, *args):
        """上传进度事件处理
        
        Args:
            *args: 事件参数，第一个参数是进度值
        """
        pass
    
    def on_upload_complete(self, *args):
        """上传完成事件处理
        
        Args:
            *args: 事件参数，第一个参数是success，第二个是result
        """
        pass
    
    def on_download_start(self, *args):
        """下载开始事件处理"""
        pass
    
    def on_download_progress(self, *args):
        """下载进度事件处理
        
        Args:
            *args: 事件参数，第一个参数是进度值
        """
        pass
    
    def on_download_complete(self, *args):
        """下载完成事件处理
        
        Args:
            *args: 事件参数，第一个参数是success，第二个是file_path
        """
        pass
    
    def on_file_selected(self, *args):
        """文件选择事件处理
        
        Args:
            *args: 事件参数，第一个参数是文件路径
        """
        pass
    
    def on_error(self, *args):
        """错误事件处理
        
        Args:
            *args: 事件参数，第一个参数是错误消息
        """
        pass


# ==================== 单例模式 ====================

_file_upload_download_manager_instance = None

def get_file_upload_download_manager() -> FileUploadDownloadManager:
    """获取文件上传下载管理器实例（单例模式）
    
    Returns:
        FileUploadDownloadManager: 文件上传下载管理器实例
    """
    global _file_upload_download_manager_instance
    
    if _file_upload_download_manager_instance is None:
        _file_upload_download_manager_instance = FileUploadDownloadManager()
        logger.info("创建文件上传下载管理器单例实例")
    
    return _file_upload_download_manager_instance


# ==================== 便捷函数 ====================

def upload_file_simple(file_path: str, metadata: Dict = None, callback: Callable = None) -> bool:
    """简单的文件上传函数
    
    Args:
        file_path (str): 文件路径
        metadata (Dict, optional): 文件元数据
        callback (Callable, optional): 上传完成回调函数
    
    Returns:
        bool: 是否成功开始上传
    """
    manager = get_file_upload_download_manager()
    return manager.upload_file(file_path, metadata, callback)

def download_file_simple(document_id: str, save_path: str = None, callback: Callable = None) -> bool:
    """简单的文件下载函数
    
    Args:
        document_id (str): 文档ID
        save_path (str, optional): 保存路径
        callback (Callable, optional): 下载完成回调函数
    
    Returns:
        bool: 是否成功开始下载
    """
    manager = get_file_upload_download_manager()
    return manager.download_file(document_id, save_path, callback)

def select_and_upload_file(callback: Callable = None, metadata: Dict = None) -> bool:
    """选择并上传文件的便捷函数
    
    Args:
        callback (Callable, optional): 上传完成回调函数
        metadata (Dict, optional): 文件元数据
    
    Returns:
        bool: 是否成功打开文件选择器
    """
    manager = get_file_upload_download_manager()
    
    def on_file_selected(file_path: str):
        manager.upload_file(file_path, metadata, callback)
    
    # 绑定文件选择事件
    manager.bind(on_file_selected=lambda instance, path: on_file_selected(path))
    
    # 打开文件管理器
    return manager.open_file_manager()