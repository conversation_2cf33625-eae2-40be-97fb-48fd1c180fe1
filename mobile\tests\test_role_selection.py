import unittest
from mobile.utils.role_selection import compute_role_selection

class TestRoleSelection(unittest.TestCase):
    def test_personal_toggle(self):
        roles, identity, changed, msg = compute_role_selection([], "", "本人注册", "个人用户")
        self.assertEqual(roles, ["个人用户"])  # 首次选中
        self.assertEqual(identity, "个人用户")
        self.assertFalse(changed)
        self.assertIsNone(msg)

        roles, identity, changed, msg = compute_role_selection(roles, identity, "本人注册", "个人用户")
        self.assertEqual(roles, [])  # 再次点击取消
        self.assertEqual(identity, "")

    def test_companion_requires_personal(self):
        roles, identity, changed, msg = compute_role_selection([], "", "本人注册", "陪诊师")
        self.assertEqual(set(roles), {"个人用户", "陪诊师"})
        self.assertEqual(identity, "陪诊师")
        # 再次点击陪诊师 -> 只保留个人用户
        roles2, identity2, changed2, msg2 = compute_role_selection(roles, identity, "本人注册", "陪诊师")
        self.assertEqual(roles2, ["个人用户"])  # 取消陪诊师
        self.assertEqual(identity2, "个人用户")
        # 若已选陪诊师，再点单位管理员应被拒绝
        roles3, identity3, changed3, msg3 = compute_role_selection(["个人用户", "陪诊师"], "陪诊师", "本人注册", "单位管理员")
        self.assertEqual(set(roles3), {"个人用户", "陪诊师"})
        self.assertIsNotNone(msg3)

    def test_other_register_mode(self):
        # 替他人注册点击非个人 -> 无变化
        roles, identity, changed, msg = compute_role_selection([], "", "替他人注册", "单位管理员")
        self.assertEqual(roles, [])
        self.assertIsNotNone(msg)
        # 点击个人 -> 选中个人
        roles, identity, changed, msg = compute_role_selection([], "", "替他人注册", "个人用户")
        self.assertEqual(roles, ["个人用户"])
        self.assertEqual(identity, "个人用户")
        # 再点个人 -> 不允许取消
        roles2, identity2, changed2, msg2 = compute_role_selection(["个人用户"], "个人用户", "替他人注册", "个人用户")
        self.assertEqual(roles2, ["个人用户"])  # 不变
        self.assertIsNotNone(msg2)

    def test_health_advisor_change_flag(self):
        roles, identity, changed, msg = compute_role_selection([], "", "本人注册", "健康顾问")
        self.assertTrue(changed)
        roles2, identity2, changed2, msg2 = compute_role_selection(roles, identity, "本人注册", "健康顾问")
        self.assertTrue(changed2)

if __name__ == "__main__":
    unittest.main()

