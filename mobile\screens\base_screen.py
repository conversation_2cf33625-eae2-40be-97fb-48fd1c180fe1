"""
Global BaseScreen for consistent UI structure across all screens.
- Fixed layout: Top header_container + ScrollView(content_container) + Bottom nav_bar_container
- Inherit and add content to self.ids['content_container']
- Compatible with KivyMD 2.0.1, theme.py fallbacks
- Public API: setup_top_app_bar, setup_logo, etc.
"""

import logging
from typing import List, Dict, Any, Optional
from kivy.uix.screenmanager import Screen
from kivy.lang import Builder
from kivy.metrics import dp
from kivy.properties import BooleanProperty, StringProperty, ObjectProperty, ListProperty
from kivy.graphics import Color, Rectangle

from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDIconButton

# Theme fallbacks
try:
    from mobile.theme import AppTheme as ThemeAppTheme, AppMetrics as ThemeAppMetrics
    # Create module-level variables to store theme values
    _PRIMARY_COLOR = getattr(ThemeAppTheme, "PRIMARY_COLOR", [0.133, 0.46, 0.82, 1])
    _TEXT_SECONDARY = getattr(ThemeAppTheme, "TEXT_SECONDARY", [0.45, 0.45, 0.45, 1])
    _PRIMARY_LIGHT = getattr(ThemeAppTheme, "PRIMARY_LIGHT", [0.95, 0.97, 1, 1])
    _NAVBAR_HEIGHT = getattr(ThemeAppMetrics, "NAVBAR_HEIGHT", 56)
except ImportError:
    _PRIMARY_COLOR = [0.133, 0.46, 0.82, 1]
    _TEXT_SECONDARY = [0.45, 0.45, 0.45, 1]
    _PRIMARY_LIGHT = [0.95, 0.97, 1, 1]
    _NAVBAR_HEIGHT = 56

# Create simple classes to hold the values
class AppTheme:
    PRIMARY_COLOR = _PRIMARY_COLOR
    TEXT_SECONDARY = _TEXT_SECONDARY
    PRIMARY_LIGHT = _PRIMARY_LIGHT

class AppMetrics:
    NAVBAR_HEIGHT = _NAVBAR_HEIGHT

# Widgets fallbacks
try:
    from mobile.widgets.logo import HealthLogo
except ImportError:
    from kivy.uix.widget import Widget
    class HealthLogo(Widget):
        pass

# Try to import GlobalTopAppBar, fallback to a simple class
_GlobalTopAppBarClass = None
try:
    from mobile.widgets.top_app_bar import GlobalTopAppBar
    _GlobalTopAppBarClass = GlobalTopAppBar
except ImportError:
    # Last resort fallback - create a minimal class
    class _MinimalGlobalTopAppBar:
        def __init__(self, **kwargs):
            # Set attributes that might be accessed
            self.parent = None
            self.title = ""
            self.right_action_items = []
    _GlobalTopAppBarClass = _MinimalGlobalTopAppBar

# Logo manager fallbacks
_logo_manager_instance = None
try:
    from mobile.widgets.logo_manager import get_logo_manager as imported_get_logo_manager
    def get_logo_manager():
        global _logo_manager_instance
        if _logo_manager_instance is None:
            try:
                _logo_manager_instance = imported_get_logo_manager()
            except Exception:
                _logo_manager_instance = _create_default_logo_manager()
        return _logo_manager_instance
except ImportError:
    def get_logo_manager():
        global _logo_manager_instance
        if _logo_manager_instance is None:
            _logo_manager_instance = _create_default_logo_manager()
        return _logo_manager_instance

def _create_default_logo_manager():
    class LogoManager:
        def cleanup_duplicate_logos(self) -> int:
            return 0
    return LogoManager()

# KV for NavBarButton
NAV_BAR_BUTTON_KV = """
<NavBarButton>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(56)
    spacing: dp(2)
    padding: [dp(2), dp(4), dp(2), dp(4)]

    MDIconButton:
        icon: root.icon
        user_font_size: "16sp"
        theme_icon_color: "Custom"
        icon_color: root.icon_color
        pos_hint: {'center_x': 0.5}
        on_release: root.on_press()

    MDLabel:
        text: root.text
        halign: 'center'
        theme_text_color: "Custom"
        text_color: root.text_color
        font_size: "10sp"
        size_hint_y: None
        height: self.texture_size[1]
"""

# KV for BaseScreen - Fixed global structure
BASE_SCREEN_KV = """
<BaseScreen>:
    MDBoxLayout:
        orientation: 'vertical'

        # Top header container (app bar + logo)
        MDBoxLayout:
            id: header_container
            orientation: 'vertical'
            size_hint_y: None
            height: max(self.minimum_height, dp(56))  # 确保至少有56dp高度以显示顶端导航栏
            padding: dp(0)

        # Content: ScrollView with container
        ScrollView:
            id: content_scroll
            bar_width: dp(10)
            scroll_type: ['content']
            do_scroll_x: False
            do_scroll_y: True

            MDBoxLayout:
                id: content_container
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(16), dp(16), dp(16)]
                spacing: dp(16)

        # Bottom nav container
        MDBoxLayout:
            id: nav_bar_container
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            spacing: 0
"""

Builder.load_string(NAV_BAR_BUTTON_KV)
Builder.load_string(BASE_SCREEN_KV)

class NavBarButton(MDBoxLayout):
    icon = StringProperty("home")
    text = StringProperty("首页")
    is_active = BooleanProperty(False)
    callback = ObjectProperty(None)
    icon_color = ObjectProperty([0.45, 0.45, 0.45, 1])  # Default to TEXT_SECONDARY
    text_color = ObjectProperty([0.45, 0.45, 0.45, 1])  # Default to TEXT_SECONDARY

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Set initial colors
        self.update_colors(None, self.is_active)
        self.bind(is_active=self.update_colors)

    def update_colors(self, instance, value):
        try:
            primary_color = getattr(AppTheme, "PRIMARY_COLOR", [0.133, 0.46, 0.82, 1])
            text_secondary = getattr(AppTheme, "TEXT_SECONDARY", [0.45, 0.45, 0.45, 1])
            color = primary_color if value else text_secondary
            self.icon_color = color[:]
            self.text_color = color[:]
        except Exception:
            # Safe defaults
            color = [0.133, 0.46, 0.82, 1] if value else [0.45, 0.45, 0.45, 1]
            self.icon_color = color[:]
            self.text_color = color[:]

    def on_press(self):
        if callable(self.callback):
            try:
                self.callback()
            except Exception as e:
                logging.getLogger(__name__).error(f"导航按钮点击事件处理失败: {e}")

class BaseScreen(Screen):
    """
    Base class for all screens: Ensures consistent UI structure.
    Subclasses: Inherit, add widgets to self.ids['content_container'] in init_ui or on_pre_enter.
    """
    is_initialized = BooleanProperty(False)
    screen_title = StringProperty("标题")
    show_top_bar = BooleanProperty(True)
    top_bar_action_icon = StringProperty("refresh")
    top_app_bar = ObjectProperty(None)
    skip_bottom_nav = BooleanProperty(False)  # For login etc.
    
    # Global nav config, override in subclass if needed
    nav_buttons_config: List[Dict[str, str]] = ListProperty([
        {'icon': 'home', 'text': '首页', 'screen': 'homepage_screen'},
        {'icon': 'heart-pulse', 'text': '健康数据', 'screen': 'health_data_management'},
        {'icon': 'shield-alert', 'text': '风险管理', 'screen': 'health_risk_management'},
        {'icon': 'account', 'text': '我的', 'screen': 'profile_page'}
    ])

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.logger = logging.getLogger(self.__class__.__name__)
        self._logo_added = False
        self.setup_background()

    def on_pre_enter(self, *args):
        """Global init hook: Ensures structure on enter."""
        if not self.is_initialized:
            self.init_ui()
        else:
            self.update_navigation_state()

    def init_ui(self, dt=0):
        """Initialize UI components."""
        try:
            self.logger.info(f"[BaseScreen] 初始化UI，屏幕: {self.__class__.__name__}")
            # Cleanup first
            self.cleanup_duplicate_nav_bars()
            
            # Setup header
            if self.show_top_bar:
                self.logger.info("[BaseScreen] 设置顶端导航栏")
                self.setup_top_app_bar()
            else:
                self.logger.info("[BaseScreen] 跳过顶端导航栏设置")
                
            self.setup_logo()
            
            # Setup nav if not skipped
            if not self.skip_bottom_nav and not self.__class__.__name__.lower().startswith('login'):
                self.logger.info("[BaseScreen] 设置底部导航栏")
                self.setup_bottom_navigation()
            else:
                self.logger.info("[BaseScreen] 跳过底部导航栏设置")
            
            # Hook for subclass content setup
            self.do_content_setup()
            
            self.is_initialized = True
            self.logger.info("[BaseScreen] UI初始化完成")
        except (AttributeError, ValueError) as e:
            self.logger.error(f"init_ui error: {e}")

    def do_content_setup(self):
        """Override in subclasses to add custom content to content_container."""
        pass

    def setup_top_app_bar(self):
        """Setup top app bar in header_container."""
        if self.top_app_bar:
            self.logger.info("[BaseScreen] 顶端导航栏已存在，跳过添加")
            return
        try:
            # Try project-specific GlobalTopAppBar first
            if _GlobalTopAppBarClass:
                self.logger.info(f"[BaseScreen] 创建顶端导航栏，标题: {self.screen_title}")
                self.top_app_bar = _GlobalTopAppBarClass(
                    title=self.screen_title,
                    action_icon=self.top_bar_action_icon,
                    parent_screen=self  # 传递父屏幕引用
                )
                self.ids['header_container'].add_widget(self.top_app_bar)
                self.logger.info("[BaseScreen] 成功添加顶端导航栏")
                # 强制更新header_container的高度
                self.ids['header_container'].height = max(self.ids['header_container'].height, dp(56))
                # 强制刷新布局
                self.ids['header_container'].do_layout()
            else:
                self.logger.warning("[BaseScreen] 未找到GlobalTopAppBar类")
        except (AttributeError, TypeError, Exception) as e:
            # Final fallback - no top app bar
            self.logger.error(f"[BaseScreen] 创建顶端导航栏失败: {e}")
            self.top_app_bar = None

    def setup_logo(self):
        """Add logo to header if not present."""
        if self._logo_added or self.has_logo():
            return
        try:
            logo = HealthLogo(size_hint_y=None, height=dp(120))
            # Add below top bar if present
            if self.top_app_bar and hasattr(self.top_app_bar, 'parent'):
                try:
                    self.ids['header_container'].add_widget(logo, index=0)
                except Exception:
                    self.ids['header_container'].add_widget(logo)
            else:
                self.ids['header_container'].add_widget(logo)
            self._logo_added = True
        except (AttributeError, Exception):
            pass

    def has_logo(self) -> bool:
        """Check if logo exists in header."""
        def check(widget):
            if isinstance(widget, HealthLogo):
                return True
            return any(check(child) for child in getattr(widget, 'children', []))
        try:
            return check(self.ids['header_container'])
        except Exception:
            return False

    def navigate_to_screen(self, screen_name: str):
        """Navigate to screen."""
        try:
            app = MDApp.get_running_app()
            if app and app.root:
                # 记录导航信息
                self.logger.info(f"[BaseScreen] 导航到屏幕: {screen_name}")
                app.root.current = screen_name
        except Exception as e:
            self.logger.error(f"[BaseScreen] 导航到屏幕 {screen_name} 失败: {e}")

    def update_navigation_state(self):
        """Update active nav button."""
        if self.skip_bottom_nav:
            return
        nav_container = self.ids.get('nav_bar_container')
        if not nav_container:
            return
        current_screen = self.__class__.__name__.lower().replace('_screen', '')
        screen_map = {
            '首页': 'homepage',
            '健康数据': 'health_data_management',
            '风险管理': 'health_risk_management',
            '我的': 'profile_page'
        }
        for child in nav_container.children:
            if isinstance(child, NavBarButton):
                target = screen_map.get(child.text, '')
                child.is_active = current_screen.startswith(target)

    def setup_bottom_navigation(self):
        """Setup bottom nav in nav_bar_container."""
        nav_container = self.ids.get('nav_bar_container')
        if not nav_container:
            self.logger.warning("[BaseScreen] 未找到nav_bar_container")
            return
            
        # 检查是否已经有导航按钮，避免重复添加
        if nav_container.children:
            self.logger.info("[BaseScreen] 底部导航栏已存在，跳过添加")
            return
            
        current_screen = self.__class__.__name__.lower().replace('_screen', '')
        self.logger.info(f"[BaseScreen] 设置底部导航栏，当前屏幕: {current_screen}")
        
        for cfg in self.nav_buttons_config:
            btn = NavBarButton(
                icon=cfg['icon'],
                text=cfg['text'],
                callback=lambda s=cfg['screen']: self.navigate_to_screen(s)
            )
            btn.is_active = current_screen == cfg['screen'].replace('_screen', '')
            try:
                nav_container.add_widget(btn)
                self.logger.info(f"[BaseScreen] 添加导航按钮: {cfg['text']}")
            except Exception as e:
                self.logger.error(f"[BaseScreen] 添加导航按钮失败: {e}")

    def setup_background(self):
        """Setup background color."""
        try:
            if hasattr(self, 'canvas') and self.canvas and hasattr(self.canvas, 'before'):
                with self.canvas.before:
                    # 使用BACKGROUND_COLOR而不是PRIMARY_LIGHT以保持一致性
                    bg_color = getattr(AppTheme, "BACKGROUND_COLOR", [0.96, 0.96, 0.96, 1])
                    Color(*bg_color)
                    self.rect = Rectangle(size=self.size, pos=self.pos)
                # Skip binding to avoid linter errors - position updates will be handled differently if needed
        except Exception:
            pass

    def cleanup_duplicate_nav_bars(self):
        """Remove duplicate nav containers."""
        def find_navs(widget, navs=None):
            if navs is None:
                navs = []
            if isinstance(widget, MDBoxLayout) and getattr(widget, 'id', None) == 'nav_bar_container':
                navs.append(widget)
            for child in getattr(widget, 'children', []):
                find_navs(child, navs)
            return navs

        try:
            all_navs = find_navs(self)
            self.logger.info(f"[BaseScreen] 找到 {len(all_navs)} 个导航栏容器")
            
            # 如果没有找到导航栏容器或只有一个，直接返回
            if len(all_navs) <= 1:
                self.logger.info("[BaseScreen] 导航栏数量正常，无需清理")
                return
                
            # 确定哪个是主导航栏容器（在ids中的那个）
            main_nav = self.ids.get('nav_bar_container') if hasattr(self, 'ids') else None
            self.logger.info(f"[BaseScreen] 主导航栏: {main_nav}")
            
            removed_count = 0
            for nav in all_navs:
                # 如果找到了主导航栏，并且当前导航栏不是主导航栏，并且有父组件
                if main_nav and nav != main_nav and hasattr(nav, 'parent') and nav.parent:
                    try:
                        nav.parent.remove_widget(nav)
                        self.logger.info(f"[BaseScreen] 移除了重复的导航栏: {nav}")
                        removed_count += 1
                    except Exception as e:
                        self.logger.error(f"[BaseScreen] 移除重复导航栏失败: {e}")
                # 如果没有找到主导航栏（可能已经被移除），但仍有多个导航栏
                elif not main_nav and len(all_navs) > 1 and hasattr(nav, 'parent') and nav.parent:
                    # 保留第一个，移除其他的
                    if nav != all_navs[0]:
                        try:
                            nav.parent.remove_widget(nav)
                            self.logger.info(f"[BaseScreen] 移除了重复的导航栏: {nav}")
                            removed_count += 1
                        except Exception as e:
                            self.logger.error(f"[BaseScreen] 移除重复导航栏失败: {e}")
                        
            self.logger.info(f"[BaseScreen] 共移除 {removed_count} 个重复导航栏")
        except Exception as e:
            self.logger.error(f"[BaseScreen] 清理重复导航栏时出错: {e}")

    def _update_rect(self, instance, value):
        try:
            if hasattr(self, 'rect'):
                self.rect.pos = self.pos
                self.rect.size = self.size
        except Exception:
            pass

    def on_action(self):
        """Handle top bar action (e.g., refresh)."""
        if hasattr(self, 'refresh_data'):
            try:
                self.refresh_data()
            except Exception:
                pass

    def refresh_data(self):
        """Override for data refresh."""
        pass

    def on_back(self):
        """Default back navigation."""
        try:
            app = MDApp.get_running_app()
            root = app.root if app else None
            if root and hasattr(root, 'go_back'):
                try:
                    success = root.go_back()
                    if success:
                        return
                except Exception:
                    pass
            if root:
                root.current = 'homepage_screen'
        except Exception:
            pass

    def hide_top_bar(self):
        """Hide top bar."""
        self.show_top_bar = False
        if self.top_app_bar and hasattr(self.top_app_bar, 'parent') and self.top_app_bar.parent:
            try:
                self.top_app_bar.parent.remove_widget(self.top_app_bar)
            except Exception:
                pass

    def show_top_bar_widget(self):
        """Show top bar."""
        self.show_top_bar = True
        if not getattr(self, 'top_app_bar', None):
            self.setup_top_app_bar()
