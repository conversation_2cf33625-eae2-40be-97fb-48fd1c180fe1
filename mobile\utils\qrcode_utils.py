"""
QR码工具模块 - 提供二维码生成和扫描功能。
该模块可以为用户创建动态二维码，并允许其他用户通过扫描二维码添加为家庭成员。
还支持通过二维码查看个人健康状况。
"""

import os
import json
import time
import uuid
import threading
import base64
from io import BytesIO
from datetime import datetime, timedelta

# 第三方库依赖
import qrcode
from PIL import Image, ImageDraw, ImageFont
from pyzbar import pyzbar
#import cv2
import numpy as np

# Kivy相关导入
from kivy.core.image import Image as CoreImage
from kivy.graphics.texture import Texture
from kivy.clock import Clock
from kivy.utils import platform

# 应用程序路径
MODULE_DIR = os.path.dirname(os.path.abspath(__file__))
APP_DIR = os.path.dirname(MODULE_DIR)
TEMP_DIR = os.path.join(APP_DIR, "temp")

# 确保临时目录存在
if not os.path.exists(TEMP_DIR):
    os.makedirs(TEMP_DIR)

# QR码配置
class QRConfig:
    # 二维码数据类型
    TYPE_USER_INFO = "user_info"        # 用户基本信息
    TYPE_HEALTH_DATA = "health_data"    # 健康数据
    TYPE_ADD_MEMBER = "add_member"      # 添加成员

    # 二维码有效期（秒）
    DEFAULT_EXPIRY = 120  # 2分钟

    # 二维码样式参数
    QR_VERSION = 3                  # 二维码版本，1-40
    QR_SIZE = 300                   # 二维码尺寸像素
    QR_BORDER = 4                   # 二维码边距
    QR_LOGO_SIZE_RATIO = 0.25       # logo占二维码大小的比例

    # 健康状态颜色
    HEALTH_COLOR_NORMAL = (32, 184, 64)  # 绿色，健康
    HEALTH_COLOR_WARNING = (255, 205, 0)  # 黄色，警告
    HEALTH_COLOR_DANGER = (230, 0, 0)     # 红色，危险

    # 应用标识符
    APP_PREFIX = "HEALTH_TREA:"

# 二维码数据加密与解密
class QRCodeCrypto:
    @staticmethod
    def encrypt_data(data):
        """简单的数据"加密"，仅用于演示"""
        # 在实际应用中应使用真正的加密算法
        # 这里仅将数据转为JSON并进行Base64编码
        json_data = json.dumps(data, ensure_ascii=False)
        return base64.b64encode(json_data.encode('utf-8')).decode('utf-8')

    @staticmethod
    def decrypt_data(encrypted_data):
        """解密数据"""
        try:
            # 解码Base64并解析JSON
            decoded = base64.b64decode(encrypted_data).decode('utf-8')
            return json.loads(decoded)
        except Exception as e:
            print(f"解密数据失败: {e}")
            return None

# 二维码生成器
class QRCodeGenerator:
    @staticmethod
    def create_qr_data(type, user_data, expiry=None):
        """创建二维码数据"""
        expiry_time = int(time.time() + (expiry or QRConfig.DEFAULT_EXPIRY))

        qr_data = {
            "type": type,
            "data": user_data,
            "expiry": expiry_time,
            "uuid": str(uuid.uuid4())
        }

        # 添加应用程序标识前缀，确保只有本应用能识别
        return f"{QRConfig.APP_PREFIX}{QRCodeCrypto.encrypt_data(qr_data)}"

    @staticmethod
    def create_user_info_qr(user_data, expiry=None, logo_path=None):
        """生成包含用户信息的二维码"""
        qr_data = QRCodeGenerator.create_qr_data(
            QRConfig.TYPE_USER_INFO,
            user_data,
            expiry
        )
        return QRCodeGenerator.generate_qr_image(qr_data, logo_path)

    @staticmethod
    def create_health_data_qr(health_data, expiry=None, logo_path=None, health_status="normal"):
        """生成包含健康数据的二维码"""
        qr_data = QRCodeGenerator.create_qr_data(
            QRConfig.TYPE_HEALTH_DATA,
            health_data,
            expiry
        )

        # 根据健康状态选择颜色
        if health_status == "warning":
            color = QRConfig.HEALTH_COLOR_WARNING
        elif health_status == "danger":
            color = QRConfig.HEALTH_COLOR_DANGER
        else:
            color = QRConfig.HEALTH_COLOR_NORMAL

        return QRCodeGenerator.generate_qr_image(qr_data, logo_path, color)

    @staticmethod
    def create_add_member_qr(custom_id, user_name, expiry=None, logo_path=None):
        """生成添加成员的二维码"""
        member_data = {
            "user_id": custom_id,
            "name": user_name,
            "timestamp": int(time.time())
        }

        qr_data = QRCodeGenerator.create_qr_data(
            QRConfig.TYPE_ADD_MEMBER,
            member_data,
            expiry
        )
        return QRCodeGenerator.generate_qr_image(qr_data, logo_path)

    @staticmethod
    def _create_placeholder_image():
        """创建占位符图像"""
        try:
            print("创建占位符图像...")
            # 创建一个简单的占位符图像
            qr_img = Image.new('RGBA', (QRConfig.QR_SIZE, QRConfig.QR_SIZE), (255, 255, 255, 255))
            
            # 在图像上绘制错误文本，便于调试
            try:
                if ImageDraw is not None:
                    draw = ImageDraw.Draw(qr_img)
                    if draw is not None:
                        # 绘制文本
                        text = "QR\nError"
                        # 计算文本位置
                        width, height = qr_img.size
                        try:
                            bbox = draw.textbbox((0, 0), text)
                            text_width = bbox[2] - bbox[0]
                            text_height = bbox[3] - bbox[1]
                            x = (width - text_width) // 2
                            y = (height - text_height) // 2
                            draw.text((x, y), text, fill=(255, 0, 0, 255))
                        except Exception as bbox_error:
                            print(f"textbbox失败: {bbox_error}")
                            # 使用简单的位置
                            draw.text((10, QRConfig.QR_SIZE//2-10), text, fill=(255, 0, 0, 255))
            except Exception as text_error:
                print(f"添加错误文本失败: {text_error}")
            
            print(f"占位符图像创建成功: {type(qr_img)}")
            return qr_img
        except Exception as placeholder_error:
            print(f"创建占位符图像失败: {placeholder_error}")
            import traceback
            traceback.print_exc()
            # 确保总是返回一个图像
            try:
                return Image.new('RGBA', (QRConfig.QR_SIZE, QRConfig.QR_SIZE), (255, 255, 255, 255))
            except Exception:
                # 最后的保障
                return None

    @staticmethod
    def manual_draw_qr(qr, fill_color="black", back_color="white"):
        """手动绘制QR码（备用）"""
        try:
            print("开始手动绘制QR码...")
            
            # 检查必要组件
            if qr is None or not hasattr(qr, 'modules'):
                raise ValueError("QR对象或modules属性无效")
                
            modules = qr.modules
            if modules is None:
                raise ValueError("QR modules为空")
                
            module_count = len(modules)
            if module_count <= 0:
                raise ValueError("QR模块数量无效")
                
            box_size = 10  # 与QRCode box_size一致
            border = getattr(qr, 'border', QRConfig.QR_BORDER)
            size = (module_count + 2 * border) * box_size
            
            print(f"创建图像，尺寸: {size}x{size}")
            img = Image.new('RGB', (size, size), back_color)
            
            # 检查ImageDraw是否可用
            if ImageDraw is None:
                raise ValueError("ImageDraw模块不可用")
                
            # 尝试创建绘图对象，如果失败则使用简单方法
            draw = None
            try:
                draw = ImageDraw.Draw(img)
            except Exception as draw_init_error:
                print(f"ImageDraw.Draw初始化失败: {draw_init_error}")
                # 如果ImageDraw失败，直接返回基础图像
                print("使用基础图像作为后备")
                # 在基础图像上绘制网格图案表示QR码
                base_img = Image.new('RGBA', (QRConfig.QR_SIZE, QRConfig.QR_SIZE), (255, 255, 255, 255))
                # 绘制简单的网格图案
                try:
                    if ImageDraw is not None:
                        base_draw = ImageDraw.Draw(base_img)
                        if base_draw is not None:
                            # 绘制网格线
                            for i in range(0, QRConfig.QR_SIZE, 10):
                                base_draw.line([(i, 0), (i, QRConfig.QR_SIZE)], fill=(200, 200, 200, 255))
                                base_draw.line([(0, i), (QRConfig.QR_SIZE, i)], fill=(200, 200, 200, 255))
                            # 绘制一些方块表示QR模块
                            for i in range(5):
                                for j in range(5):
                                    if (i + j) % 2 == 0:
                                        x, y = i * 20, j * 20
                                        base_draw.rectangle([x, y, x + 15, y + 15], fill=(50, 50, 50, 255))
                except Exception:
                    pass
                return base_img
            
            # 检查draw对象是否有效
            if draw is None:
                raise ValueError("无法创建绘图对象")
            
            # 绘制QR码模块
            for row in range(module_count):
                for col in range(module_count):
                    if modules[row][col]:  # True表示黑色模块
                        x = (col + border) * box_size
                        y = (row + border) * box_size
                        # 检查rectangle方法是否可用
                        if hasattr(draw, 'rectangle') and callable(getattr(draw, 'rectangle')):
                            draw.rectangle(
                                [x, y, x + box_size, y + box_size],
                                fill=fill_color
                            )
                        else:
                            # 如果rectangle方法不可用，尝试其他方式
                            print("rectangle方法不可用，跳过绘制")
                            return img
            
            print("手动绘制QR码成功")
            return img.convert('RGBA')
        except Exception as e:
            print(f"手动绘制QR码失败: {e}")
            import traceback
            traceback.print_exc()
            # 返回基础图像作为最后的后备
            try:
                size = QRConfig.QR_SIZE
                img = Image.new('RGBA', (size, size), (255, 255, 255, 255))
                # 在图像上绘制网格图案表示QR码
                try:
                    if ImageDraw is not None:
                        draw = ImageDraw.Draw(img)
                        if draw is not None:
                            # 绘制网格线
                            for i in range(0, size, 10):
                                draw.line([(i, 0), (i, size)], fill=(200, 200, 200, 255))
                                draw.line([(0, i), (size, i)], fill=(200, 200, 200, 255))
                            # 绘制一些方块表示QR模块
                            for i in range(5):
                                for j in range(5):
                                    if (i + j) % 2 == 0:
                                        x, y = i * 20, j * 20
                                        draw.rectangle([x, y, x + 15, y + 15], fill=(50, 50, 50, 255))
                except Exception:
                    pass
                return img
            except Exception:
                return Image.new('RGBA', (QRConfig.QR_SIZE, QRConfig.QR_SIZE), (255, 255, 255, 255))

    @staticmethod
    def generate_qr_image(data, logo_path=None, color=(0, 0, 0)):
        """生成QR码图像 - 优化版本，增强错误处理和稳定性"""
        qr = None  # 初始化qr变量
        qr_img = None  # 初始化qr_img变量
        
        # 数据验证
        if not data:
            print("QR数据为空")
            return QRCodeGenerator._create_placeholder_image()
            
        try:
            # 导入PIL图像工厂以确保可用性
            from qrcode.image.pil import PilImage

            print(f"开始创建QRCode对象，数据长度: {len(data)}")
            qr = qrcode.QRCode(
                version=QRConfig.QR_VERSION,
                error_correction=qrcode.ERROR_CORRECT_H,
                box_size=10,
                border=QRConfig.QR_BORDER,
                image_factory=PilImage  # 显式设置image_factory
            )
            print("QRCode对象创建成功")

            print(f"添加数据到QR码: {data[:50]}...")  # 只显示前50个字符
            qr.add_data(data)
            print("调用qr.make(fit=True)...")
            qr.make(fit=True)
            print(f"QR码制作完成，模块数量: {qr.modules_count if hasattr(qr, 'modules_count') else '未知'}")

            # 创建带颜色的QR码图像 - 使用更稳定的方式
            try:
                print("开始生成QR码图像...")

                # 处理颜色参数 - 更健壮的颜色转换
                try:
                    if isinstance(color, tuple) and len(color) == 3:
                        # 确保颜色值在有效范围内
                        r = max(0, min(255, int(color[0])))
                        g = max(0, min(255, int(color[1])))
                        b = max(0, min(255, int(color[2])))
                        color_hex = "#{:02x}{:02x}{:02x}".format(r, g, b)
                        print(f"使用颜色: {color_hex}")
                    elif isinstance(color, str):
                        color_hex = color
                        print(f"使用字符串颜色: {color_hex}")
                    else:
                        color_hex = "black"
                        print("使用默认黑色")
                except Exception as color_error:
                    print(f"颜色转换失败: {color_error}，使用默认黑色")
                    color_hex = "black"

                print(f"QR对象状态 - version: {qr.version}, modules_count: {getattr(qr, 'modules_count', 'N/A')}")
                print(f"QR对象类型: {type(qr)}")
                print(f"QR对象是否有make_image方法: {hasattr(qr, 'make_image')}")

                # 检查qr对象的modules属性
                if hasattr(qr, 'modules') and qr.modules:
                    print(f"QR modules存在，长度: {len(qr.modules)}")
                else:
                    print("QR modules不存在或为空")

                # 尝试使用更稳定的图像生成方法
                qr_img = QRCodeGenerator._create_qr_image_stable(qr, color_hex)
                
                # 确保图像是正确的尺寸和模式
                if qr_img:
                    print(f"QR图像生成成功，类型: {type(qr_img)}")
                    # 确保图像对象有效
                    if hasattr(qr_img, 'convert'):
                        qr_img = qr_img.convert('RGBA')
                        print(f"转换为RGBA后: {qr_img.size}")
                    else:
                        # 如果转换失败，创建新的图像
                        print("图像转换失败，创建新图像")
                        if hasattr(qr_img, 'copy'):
                            qr_img = qr_img.copy()
                        
                    # 调整图像尺寸到标准大小
                    target_size = (QRConfig.QR_SIZE, QRConfig.QR_SIZE)
                    if hasattr(qr_img, 'size') and qr_img.size != target_size:
                        print(f"调整图像尺寸从 {qr_img.size} 到 {target_size}")
                        if hasattr(qr_img, 'resize'):
                            qr_img = qr_img.resize(target_size, Image.Resampling.LANCZOS)
                            print(f"调整后尺寸: {qr_img.size}")
                        else:
                            # 如果调整大小失败，创建占位符图像
                            print("图像调整大小失败，创建占位符图像")
                            qr_img = QRCodeGenerator._create_placeholder_image()
                    else:
                        print("图像尺寸已符合要求")
                else:
                    print("QR图像生成失败")
                        
            except Exception as pil_error:
                print(f"PilImage创建失败: {pil_error}")
                import traceback
                traceback.print_exc()
                # 创建占位符图像作为后备
                qr_img = QRCodeGenerator._create_placeholder_image()

        except Exception as e:
            print(f"QR码生成完全失败: {e}")
            import traceback
            traceback.print_exc()
            print(f"数据长度: {len(data) if data else 'None'}")
            # 检查qr是否已定义
            if qr is not None:
                print(f"QR对象最终状态: {getattr(qr, 'version', 'None')}")
            else:
                print("qr对象未创建")
            # 创建一个简单的占位符图像
            qr_img = QRCodeGenerator._create_placeholder_image()

        # 如果提供了logo，则添加到QR码中心
        if logo_path and os.path.exists(logo_path) and qr_img:
            try:
                print(f"开始处理logo: {logo_path}")

                # 打开logo图像
                logo = Image.open(logo_path).convert('RGBA')
                print(f"logo图像加载成功: {type(logo)}")

                # 调整logo大小
                logo_size = int(QRConfig.QR_SIZE * QRConfig.QR_LOGO_SIZE_RATIO)
                print(f"调整logo大小: {logo_size}x{logo_size}")
                logo = logo.resize((logo_size, logo_size), Image.Resampling.LANCZOS)

                # 计算logo位置（居中）
                logo_pos = ((QRConfig.QR_SIZE - logo_size) // 2, (QRConfig.QR_SIZE - logo_size) // 2)

                # 创建一个新图像用于合成
                print("开始合成logo和QR码...")
                combined = Image.new('RGBA', qr_img.size, (255, 255, 255, 0))
                combined.paste(qr_img, (0, 0))
                combined.paste(logo, logo_pos, logo)
                print("logo合成成功")

                return combined
            except Exception as e:
                print(f"添加Logo失败: {e}")
                import traceback
                traceback.print_exc()

        print(f"返回QR图像: {qr_img is not None}")
        # 确保总是返回一个有效的图像
        if qr_img is None:
            print("QR图像为None，返回占位符图像")
            qr_img = QRCodeGenerator._create_placeholder_image()
            if qr_img is None:
                qr_img = Image.new('RGBA', (QRConfig.QR_SIZE, QRConfig.QR_SIZE), (255, 255, 255, 255))
        return qr_img

    @staticmethod
    def _create_qr_image_stable(qr, color_hex):
        """使用更稳定的方法创建QR图像"""
        try:
            # 首先尝试标准方法（简化调用）
            print("尝试标准qr.make_image方法...")
            
            # 检查必要的PIL组件是否可用
            if Image is None or ImageDraw is None:
                raise ImportError("PIL组件不可用")
                
            # 直接使用手动绘制方法，避免依赖qr.make_image中的复杂逻辑
            print("跳过qr.make_image，直接使用手动绘制方法...")
            return QRCodeGenerator._create_qr_image_fallback(qr, color_hex)
            
        except Exception as make_image_error:
            print(f"qr.make_image()失败: {make_image_error}")
            import traceback
            traceback.print_exc()
            # 如果标准方法失败，使用备用方法
            return QRCodeGenerator._create_qr_image_fallback(qr, color_hex)

    @staticmethod
    def _create_qr_image_fallback(qr, color_hex):
        """备用QR图像创建方法"""
        try:
            print("使用备用方法创建QR图像...")
            
            # 检查必要的PIL组件是否可用
            if Image is None:
                print("PIL Image模块不可用")
                raise ImportError("PIL Image模块不可用")
                
            # 直接使用手动绘制QR码方法
            try:
                # 转换颜色格式
                fill_color = "black"
                try:
                    if color_hex.startswith('#') and len(color_hex) == 7:
                        # 将十六进制颜色转换为RGB元组
                        r = int(color_hex[1:3], 16)
                        g = int(color_hex[3:5], 16)
                        b = int(color_hex[5:7], 16)
                        fill_color = (r, g, b)
                except ValueError as color_error:
                    print(f"颜色转换失败: {color_error}")
                    pass
                    
                # 使用更可靠的QR码绘制方法
                # 确保fill_color是字符串类型
                if isinstance(fill_color, tuple):
                    # 将RGB元组转换为十六进制字符串
                    fill_color = "#{:02x}{:02x}{:02x}".format(fill_color[0], fill_color[1], fill_color[2])
                qr_img = QRCodeGenerator.manual_draw_qr(qr, fill_color=fill_color, back_color="white")
                if qr_img is not None:
                    print(f"手动绘制QR码成功: {qr_img.size if qr_img else 'None'}")
                    return qr_img
                else:
                    print("手动绘制QR码返回None")
            except Exception as manual_draw_error:
                print(f"手动绘制QR码失败: {manual_draw_error}")
                import traceback
                traceback.print_exc()
                
            # 如果手动绘制失败，创建基础图像
            try:
                img_width = max(100, getattr(qr, 'modules_count', 21) * 10 + QRConfig.QR_BORDER * 20)
                img_height = max(100, getattr(qr, 'modules_count', 21) * 10 + QRConfig.QR_BORDER * 20)
                img = Image.new('RGB', (img_width, img_height), "white")
                print(f"创建基础图像: {img.size}")
                
                # 尝试在基础图像上绘制简单的图案
                try:
                    # 检查ImageDraw是否可用
                    if ImageDraw is not None:
                        try:
                            draw = ImageDraw.Draw(img)
                            if draw is not None:
                                # 绘制网格图案表示QR码
                                for i in range(0, min(img_width, img_height), 10):
                                    draw.line([(i, 0), (i, img_height)], fill=(200, 200, 200))
                                    draw.line([(0, i), (img_width, i)], fill=(200, 200, 200))
                                # 绘制一些方块表示QR模块
                                for i in range(5):
                                    for j in range(5):
                                        if (i + j) % 2 == 0:
                                            x, y = i * 20, j * 20
                                            draw.rectangle([x, y, x + 15, y + 15], fill=(50, 50, 50))
                        except Exception as draw_error:
                            print(f"绘图对象创建失败: {draw_error}")
                            # 即使绘图失败也返回基础图像
                except Exception as pattern_error:
                    print(f"绘制图案失败: {pattern_error}")
                    
                return img
            except Exception as img_error:
                print(f"创建基础图像失败: {img_error}")
                import traceback
                traceback.print_exc()
                
        except Exception as fallback_error:
            print(f"备用方法也失败: {fallback_error}")
            import traceback
            traceback.print_exc()
            
        # 最后的回退方案：创建占位符图像
        try:
            qr_img = QRCodeGenerator._create_placeholder_image()
            if qr_img is None:
                # 确保总是返回一个图像
                qr_img = Image.new('RGBA', (QRConfig.QR_SIZE, QRConfig.QR_SIZE), (255, 255, 255, 255))
            print(f"创建占位符图像: {type(qr_img)}")
            return qr_img
        except Exception as final_error:
            print(f"最终回退方案也失败: {final_error}")
            # 确保总是返回一个图像
            return Image.new('RGBA', (QRConfig.QR_SIZE, QRConfig.QR_SIZE), (255, 255, 255, 255))

    @staticmethod
    def get_qr_texture(qr_image):
        """将PIL图像转换为Kivy纹理"""
        if qr_image is None:
            print("QR图像为None，无法创建纹理")
            return None

        try:
            # 检查图像对象是否有效
            if not hasattr(qr_image, 'save') or not callable(getattr(qr_image, 'save')):
                print("QR图像对象无效，无法保存")
                return None
                
            # 保存图像到内存缓冲区
            buf = BytesIO()
            qr_image.save(buf, format='PNG')
            buf.seek(0)

            # 转换为Kivy纹理
            data = buf.read()
            buf.close()

            # 使用CoreImage创建纹理
            if CoreImage is None:
                print("CoreImage模块不可用")
                return None
                
            core_img = CoreImage(BytesIO(data), ext='png')
            
            # 检查纹理是否有效
            if not hasattr(core_img, 'texture'):
                print("无法从CoreImage获取纹理")
                return None
                
            texture = core_img.texture

            return texture
        except Exception as e:
            print(f"创建QR纹理失败: {e}")
            import traceback
            traceback.print_exc()
            # 尝试创建一个简单的纹理作为后备
            try:
                # 创建一个简单的白色纹理
                from kivy.graphics.texture import Texture
                texture = Texture.create(size=(QRConfig.QR_SIZE, QRConfig.QR_SIZE), colorfmt='rgba')
                # 填充白色数据
                data = b'\xff\xff\xff\xff' * (QRConfig.QR_SIZE * QRConfig.QR_SIZE)
                texture.blit_buffer(data, colorfmt='rgba', bufferfmt='ubyte')
                return texture
            except Exception as fallback_error:
                print(f"创建后备纹理失败: {fallback_error}")
                return None

    @staticmethod
    def save_qr_image(qr_image, filename=None):
        """保存QR码图像到文件"""
        if filename is None:
            filename = f"qrcode_{int(time.time())}.png"

        # 确保目录存在
        file_path = os.path.join(TEMP_DIR, filename)
        qr_image.save(file_path)

        return file_path

# 二维码扫描器
class QRCodeScanner:
    def __init__(self, callback=None):
        """初始化扫描器"""
        try:
            import cv2
            self.cv2 = cv2
        except ImportError:
            self.cv2 = None
            print("未安装 OpenCV，二维码扫描功能不可用")
        self.camera = None
        self.callback = callback
        self.scanning = False
        self.last_scan_time = 0
        self.scan_interval = 0.5  # 扫描间隔（秒）
        self.scan_thread = None

    def start_scanning(self, camera_index=0):
        """开始扫描二维码"""
        if self.scanning:
            return False
        if not self.cv2:
            print("未安装 OpenCV，无法启动扫描")
            return False
        try:
            self.camera = self.cv2.VideoCapture(camera_index)
            if not self.camera or not self.camera.isOpened():
                print("无法打开摄像头")
                return False
            self.scanning = True
            self.scan_thread = threading.Thread(target=self._scan_loop)
            self.scan_thread.daemon = True
            self.scan_thread.start()
            return True
        except Exception as e:
            print(f"启动扫描失败: {e}")
            return False

    def stop_scanning(self):
        """停止扫描"""
        self.scanning = False
        if self.scan_thread:
            self.scan_thread.join(timeout=1.0)
        if self.camera and hasattr(self.camera, 'release'):
            self.camera.release()
            self.camera = None

    def _scan_loop(self):
        """扫描循环"""
        while self.scanning:
            current_time = time.time()
            if current_time - self.last_scan_time < self.scan_interval:
                time.sleep(0.1)
                continue
            if self.camera and hasattr(self.camera, 'read'):
                ret, frame = self.camera.read()
                if not ret:
                    continue
                qr_data = self._decode_frame(frame)
                if qr_data:
                    self.last_scan_time = current_time
                    callback_func = self.callback
                    if callback_func is not None:
                        def schedule_callback(dt, data=qr_data):
                            if callback_func is not None:
                                callback_func(data)
                        Clock.schedule_once(schedule_callback, 0)

    def _decode_frame(self, frame):
        """解码帧中的二维码"""
        if not self.cv2:
            print("未安装 OpenCV，无法解码帧")
            return None
        gray = self.cv2.cvtColor(frame, self.cv2.COLOR_BGR2GRAY)
        decoded_objects = pyzbar.decode(gray)
        for obj in decoded_objects:
            qr_text = obj.data.decode('utf-8')
            if qr_text.startswith(QRConfig.APP_PREFIX):
                encrypted_data = qr_text[len(QRConfig.APP_PREFIX):]
                data = QRCodeCrypto.decrypt_data(encrypted_data)
                if data:
                    if data.get("expiry", 0) > time.time():
                        return data
                    else:
                        print("二维码已过期")
        return None

    @staticmethod
    def scan_from_image(image_path):
        """从图像文件扫描二维码"""
        try:
            try:
                import cv2
            except ImportError:
                print("未安装 OpenCV，无法扫描图像文件")
                return {"error": "未安装 OpenCV，无法扫描二维码"}
            # 读取图像
            if isinstance(image_path, str):
                image = cv2.imread(image_path)
            else:
                image = cv2.cvtColor(np.array(image_path), cv2.COLOR_RGB2BGR)
            if image is None:
                return {"error": "无法读取图像文件"}
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            decoded_objects = pyzbar.decode(gray)
            for obj in decoded_objects:
                qr_text = obj.data.decode('utf-8')
                if qr_text.startswith(QRConfig.APP_PREFIX):
                    encrypted_data = qr_text[len(QRConfig.APP_PREFIX):]
                    data = QRCodeCrypto.decrypt_data(encrypted_data)
                    if data:
                        if data.get("expiry", 0) > time.time():
                            return data
                        else:
                            return {"error": "二维码已过期"}
            return {"error": "未找到有效的二维码"}
        except Exception as e:
            print(f"扫描图像失败: {e}")
            return {"error": f"扫描图像失败: {str(e)}"}

# 动态二维码管理
class DynamicQRCode:
    def __init__(self, user_id, update_interval=30):
        """初始化动态二维码管理器"""
        self.user_id = user_id
        self.update_interval = update_interval  # 更新间隔（秒）
        self.qr_image = None
        self.qr_texture = None
        self.update_event = None
        self.user_data = None
        self.health_data = None
        self.logo_path = None
        self.callback = None
        self._updating = False  # 防止重复更新的标志

    def start(self, user_data, health_data=None, logo_path=None, callback=None):
        """开始动态二维码更新"""
        print(f"DynamicQRCode.start 被调用，user_data: {user_data}, health_data: {health_data}")
        self.user_data = user_data
        self.health_data = health_data
        self.logo_path = logo_path
        self.callback = callback

        # 停止之前的更新
        self.stop()

        # 初始生成二维码 - 直接在主线程中执行
        Clock.schedule_once(self._safe_update_qr, 0)

        # 设置定时更新
        self.update_event = Clock.schedule_interval(
            self._safe_update_qr,
            self.update_interval
        )

    def stop(self):
        """停止动态二维码更新"""
        print("DynamicQRCode.stop 被调用")
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None

    def _safe_update_qr(self, dt=0):
        """安全的二维码更新方法 - 防止重复调用"""
        print(f"DynamicQRCode._safe_update_qr 被调用，当前更新状态: {self._updating}")
        if self._updating:
            print("二维码正在更新中，跳过本次调用")
            return

        self._updating = True
        try:
            self._update_qr()
        except Exception as e:
            print(f"二维码更新过程中发生异常: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self._updating = False

    def _update_qr(self):
        """更新二维码 - 确保在主线程中执行"""
        try:
            print(f"开始更新二维码，当前线程: {threading.current_thread().name}")
            print(f"用户数据: {self.user_data}")
            print(f"健康数据: {self.health_data}")
            print(f"Logo路径: {self.logo_path}")

            # 初始化图像和纹理
            self.qr_image = None
            self.qr_texture = None
            
            # 数据验证
            if not self.user_data:
                print("用户数据为空，无法生成二维码")
                self._notify_callback(None)
                return
                
            if self.health_data:
                # 确定健康状态
                health_status = self._calculate_health_status(self.health_data)
                print(f"计算出的健康状态: {health_status}")

                # 生成带健康状态的二维码
                print("开始生成健康数据QR码...")
                try:
                    self.qr_image = QRCodeGenerator.create_health_data_qr(
                        self.health_data,
                        expiry=self.update_interval + 10,  # 有效期略长于更新间隔
                        logo_path=self.logo_path,
                        health_status=health_status
                    )
                    print(f"健康数据QR码生成结果: {self.qr_image is not None}")
                except Exception as gen_error:
                    print(f"健康数据QR码生成异常: {gen_error}")
                    import traceback
                    traceback.print_exc()
                    # 尝试生成基本二维码作为后备
                    try:
                        if self.user_data:
                            self.qr_image = QRCodeGenerator.create_user_info_qr(
                                self.user_data,
                                expiry=self.update_interval + 10,
                                logo_path=self.logo_path
                            )
                    except Exception as fallback_error:
                        print(f"后备二维码生成也失败: {fallback_error}")
            else:
                # 生成用户信息二维码
                print("开始生成用户信息QR码...")
                try:
                    self.qr_image = QRCodeGenerator.create_user_info_qr(
                        self.user_data,
                        expiry=self.update_interval + 10,
                        logo_path=self.logo_path
                    )
                    print(f"用户信息QR码生成结果: {self.qr_image is not None}")
                except Exception as gen_error:
                    print(f"用户信息QR码生成异常: {gen_error}")
                    import traceback
                    traceback.print_exc()

            # 更新纹理
            print("开始更新纹理...")
            if self.qr_image is not None:
                try:
                    self.qr_texture = QRCodeGenerator.get_qr_texture(self.qr_image)
                    print("QR图像和纹理生成成功")
                except Exception as texture_error:
                    print(f"纹理生成失败: {texture_error}")
                    import traceback
                    traceback.print_exc()
                    self.qr_texture = None
            else:
                print("QR图像生成失败，无法创建纹理")
                self.qr_texture = None

            # 调用回调函数更新UI
            self._notify_callback(self.qr_texture)

        except Exception as e:
            print(f"二维码更新失败: {e}")
            import traceback
            traceback.print_exc()
            # 即使失败也要通知UI
            self._notify_callback(None)
            
    def _notify_callback(self, texture):
        """通知回调函数"""
        try:
            print(f"准备调用回调函数，回调函数: {self.callback}, 是否可调用: {callable(self.callback) if self.callback else False}")
            if self.callback and callable(self.callback):
                def safe_callback(dt):
                    try:
                        # 调用回调函数
                        print(f"调用回调函数，传递纹理: {texture}")
                        if self.callback:
                            self.callback(texture)
                        print("二维码回调函数调用成功")
                    except Exception as e:
                        print(f"二维码回调函数调用失败: {e}")
                        import traceback
                        traceback.print_exc()

                # 使用Clock.schedule_once确保回调在主线程中执行
                Clock.schedule_once(safe_callback, 0)
            else:
                print("回调函数不可用或不可调用")
        except Exception as e:
            print(f"通知回调函数失败: {e}")
            import traceback
            traceback.print_exc()

    def _calculate_health_status(self, health_data):
        """根据健康数据计算健康状态"""
        # 这里是简化的健康状态计算，实际应用应根据医学标准判断
        danger_count = 0
        warning_count = 0

        # 检查血压
        if 'blood_pressure' in health_data:
            systolic = health_data['blood_pressure'].get('systolic', 120)
            diastolic = health_data['blood_pressure'].get('diastolic', 80)

            if systolic > 180 or diastolic > 110:
                danger_count += 1
            elif systolic > 140 or diastolic > 90:
                warning_count += 1

        # 检查血糖
        if 'blood_glucose' in health_data:
            glucose = health_data['blood_glucose'].get('value', 5.5)

            if glucose > 11.1:
                danger_count += 1
            elif glucose > 7.0:
                warning_count += 1

        # 根据危险和警告计数确定状态
        if danger_count > 0:
            return "danger"
        elif warning_count > 0:
            return "warning"
        else:
            return "normal"

    def get_current_texture(self):
        """获取当前二维码纹理"""
        if not self.qr_texture:
            self._update_qr()
        return self.qr_texture

    def save_current_image(self, filename=None):
        """保存当前二维码图像"""
        if not self.qr_image:
            self._update_qr()

        return QRCodeGenerator.save_qr_image(self.qr_image, filename)

# 便捷函数
def generate_user_qrcode(user_data, logo_path=None):
    """生成用户信息二维码"""
    return QRCodeGenerator.create_user_info_qr(user_data, logo_path=logo_path)

def generate_health_qrcode(user_data, health_data, logo_path=None):
    """生成健康信息二维码"""
    return QRCodeGenerator.create_health_data_qr(health_data, logo_path=logo_path)

def generate_member_invitation_qrcode(user_id, user_name, logo_path=None):
    """生成家庭成员邀请二维码"""
    return QRCodeGenerator.create_add_member_qr(user_id, user_name, logo_path=logo_path)

def scan_qrcode_from_image(image_path):
    """从图像文件扫描二维码"""
    return QRCodeScanner.scan_from_image(image_path)