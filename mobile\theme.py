# theme.py - KivyMD 2.0.1 dev0 Compatible Theme Configuration
from kivy.utils import get_color_from_hex
from kivy.metrics import dp, sp
from kivymd.theming import ThemeManager

# Material Design 3 Color System Implementation
class MaterialDesign3Colors:
    """Material Design 3 Color System
    
    Implements the Material Design 3 color system with proper token-based approach.
    This follows the KivyMD 2.0.1 dev0 specifications for theming.
    """
    
    # Primary Color Palette
    PRIMARY = {
        'primary': get_color_from_hex('#2196F3'),
        'on_primary': get_color_from_hex('#FFFFFF'),
        'primary_container': get_color_from_hex('#E3F2FD'),
        'on_primary_container': get_color_from_hex('#0D47A1'),
    }
    
    # Secondary Color Palette
    SECONDARY = {
        'secondary': get_color_from_hex('#625B71'),
        'on_secondary': get_color_from_hex('#FFFFFF'),
        'secondary_container': get_color_from_hex('#E8DEF8'),
        'on_secondary_container': get_color_from_hex('#1D192B'),
    }
    
    # Tertiary Color Palette
    TERTIARY = {
        'tertiary': get_color_from_hex('#7D5260'),
        'on_tertiary': get_color_from_hex('#FFFFFF'),
        'tertiary_container': get_color_from_hex('#FFD8E4'),
        'on_tertiary_container': get_color_from_hex('#31111D'),
    }
    
    # Error Color Palette
    ERROR = {
        'error': get_color_from_hex('#BA1A1A'),
        'on_error': get_color_from_hex('#FFFFFF'),
        'error_container': get_color_from_hex('#FFDAD6'),
        'on_error_container': get_color_from_hex('#410002'),
    }
    
    # Surface Color Palette (Light Theme)
    SURFACE_LIGHT = {
        'surface': get_color_from_hex('#FFFBFE'),
        'on_surface': get_color_from_hex('#1C1B1F'),
        'surface_variant': get_color_from_hex('#E7E0EC'),
        'on_surface_variant': get_color_from_hex('#49454F'),
        'surface_container_highest': get_color_from_hex('#E6E1E5'),
        'surface_container_high': get_color_from_hex('#ECE6F0'),
        'surface_container': get_color_from_hex('#F3EDF7'),
        'surface_container_low': get_color_from_hex('#F7F2FA'),
        'surface_container_lowest': get_color_from_hex('#FFFFFF'),
        'surface_dim': get_color_from_hex('#DDD8DD'),
        'surface_bright': get_color_from_hex('#FFFBFE'),
    }
    
    # Surface Color Palette (Dark Theme)
    SURFACE_DARK = {
        'surface': get_color_from_hex('#1C1B1F'),
        'on_surface': get_color_from_hex('#E6E1E5'),
        'surface_variant': get_color_from_hex('#49454F'),
        'on_surface_variant': get_color_from_hex('#CAC4D0'),
        'surface_container_highest': get_color_from_hex('#36343B'),
        'surface_container_high': get_color_from_hex('#2B2930'),
        'surface_container': get_color_from_hex('#211F26'),
        'surface_container_low': get_color_from_hex('#1C1B1F'),
        'surface_container_lowest': get_color_from_hex('#0F0D13'),
        'surface_dim': get_color_from_hex('#1C1B1F'),
        'surface_bright': get_color_from_hex('#413E4A'),
    }
    
    # Outline Colors
    OUTLINE = {
        'outline': get_color_from_hex('#79747E'),
        'outline_variant': get_color_from_hex('#CAC4D0'),
    }
    
    # Shadow and Scrim
    SHADOW_SCRIM = {
        'shadow': get_color_from_hex('#000000'),
        'scrim': get_color_from_hex('#000000'),
    }
    
    # Inverse Colors
    INVERSE = {
        'inverse_surface': get_color_from_hex('#E6E1E5'),
        'inverse_on_surface': get_color_from_hex('#313033'),
        'inverse_primary': get_color_from_hex('#D0BCFF'),
    }

# KivyMD 2.0.1 Compatible Theme Class
class HealthAppTheme:
    """Health App Theme Configuration for KivyMD 2.0.1 dev0
    
    This class provides a complete theme configuration that follows
    KivyMD 2.0.1 dev0 specifications and Material Design 3 guidelines.
    """
    
    def __init__(self, theme_style="Light"):
        self.theme_style = theme_style
        self._setup_colors()
        # Add DIMENSIONS from MaterialDesign3Components
        self.DIMENSIONS = MaterialDesign3Components.DIMENSIONS
    
    def _setup_colors(self):
        """Setup color scheme based on theme style"""
        if self.theme_style == "Light":
            self.colors = {
                **MaterialDesign3Colors.PRIMARY,
                **MaterialDesign3Colors.SECONDARY,
                **MaterialDesign3Colors.TERTIARY,
                **MaterialDesign3Colors.ERROR,
                **MaterialDesign3Colors.SURFACE_LIGHT,
                **MaterialDesign3Colors.OUTLINE,
                **MaterialDesign3Colors.SHADOW_SCRIM,
                **MaterialDesign3Colors.INVERSE,
            }
            # Add surfaceContainerColor for compatibility
            self.surfaceContainerColor = self.colors['surface_container']
            # Add surface_container_low for compatibility
            self.surface_container_low = self.colors.get('surface_container_low', self.colors['surface_container'])
        else:  # Dark theme
            self.colors = {
                **MaterialDesign3Colors.PRIMARY,
                **MaterialDesign3Colors.SECONDARY,
                **MaterialDesign3Colors.TERTIARY,
                **MaterialDesign3Colors.ERROR,
                **MaterialDesign3Colors.SURFACE_DARK,
                **MaterialDesign3Colors.OUTLINE,
                **MaterialDesign3Colors.SHADOW_SCRIM,
                **MaterialDesign3Colors.INVERSE,
            }
            # Add surfaceContainerColor for compatibility
            self.surfaceContainerColor = self.colors['surface_container']
            # Add surface_container_low for compatibility
            self.surface_container_low = self.colors.get('surface_container_low', self.colors['surface_container'])
    
    # Health-specific color extensions
    HEALTH_COLORS = {
        'health_green': get_color_from_hex('#4CAF50'),
        'health_blue': get_color_from_hex('#03A9F4'),
        'health_orange': get_color_from_hex('#FF9800'),
        'health_purple': get_color_from_hex('#9C27B0'),
        'health_red': get_color_from_hex('#F44336'),
        'health_yellow': get_color_from_hex('#FFC107'),
    }
    
    # Medical record category colors
    MEDICAL_COLORS = {
        'hospital_color': get_color_from_hex('#4CAF50'),
        'outpatient_color': get_color_from_hex('#2196F3'),
        'lab_color': get_color_from_hex('#FF9800'),
        'tech_color': get_color_from_hex('#9C27B0'),
    }
    
    def get_color(self, color_name):
        """Get color by name"""
        return self.colors.get(color_name, get_color_from_hex('#000000'))
    
    def get_health_color(self, color_name):
        """Get health-specific color by name"""
        return self.HEALTH_COLORS.get(color_name, get_color_from_hex('#000000'))
    
    def get_medical_color(self, color_name):
        """Get medical category color by name"""
        return self.MEDICAL_COLORS.get(color_name, get_color_from_hex('#000000'))

# Typography System for KivyMD 2.0.1
class MaterialDesign3Typography:
    """Material Design 3 Typography System
    
    Implements the Material Design 3 typography scale with proper
    font sizes, weights, and line heights for KivyMD 2.0.1 dev0.
    """
    
    # Display styles
    DISPLAY_LARGE = {
        'font_size': sp(57),
        'line_height': 1.12,
        'font_weight': '400',
        'letter_spacing': -0.25,
    }
    
    DISPLAY_MEDIUM = {
        'font_size': sp(45),
        'line_height': 1.16,
        'font_weight': '400',
        'letter_spacing': 0,
    }
    
    DISPLAY_SMALL = {
        'font_size': sp(36),
        'line_height': 1.22,
        'font_weight': '400',
        'letter_spacing': 0,
    }
    
    # Headline styles
    HEADLINE_LARGE = {
        'font_size': sp(32),
        'line_height': 1.25,
        'font_weight': '400',
        'letter_spacing': 0,
    }
    
    HEADLINE_MEDIUM = {
        'font_size': sp(28),
        'line_height': 1.29,
        'font_weight': '400',
        'letter_spacing': 0,
    }
    
    HEADLINE_SMALL = {
        'font_size': sp(24),
        'line_height': 1.33,
        'font_weight': '400',
        'letter_spacing': 0,
    }
    
    # Title styles
    TITLE_LARGE = {
        'font_size': sp(22),
        'line_height': 1.27,
        'font_weight': '400',
        'letter_spacing': 0,
    }
    
    TITLE_MEDIUM = {
        'font_size': sp(16),
        'line_height': 1.5,
        'font_weight': '500',
        'letter_spacing': 0.15,
    }
    
    TITLE_SMALL = {
        'font_size': sp(14),
        'line_height': 1.43,
        'font_weight': '500',
        'letter_spacing': 0.1,
    }
    
    # Label styles
    LABEL_LARGE = {
        'font_size': sp(14),
        'line_height': 1.43,
        'font_weight': '500',
        'letter_spacing': 0.1,
    }
    
    LABEL_MEDIUM = {
        'font_size': sp(12),
        'line_height': 1.33,
        'font_weight': '500',
        'letter_spacing': 0.5,
    }
    
    LABEL_SMALL = {
        'font_size': sp(11),
        'line_height': 1.45,
        'font_weight': '500',
        'letter_spacing': 0.5,
    }
    
    # Body styles
    BODY_LARGE = {
        'font_size': sp(16),
        'line_height': 1.5,
        'font_weight': '400',
        'letter_spacing': 0.5,
    }
    
    BODY_MEDIUM = {
        'font_size': sp(14),
        'line_height': 1.43,
        'font_weight': '400',
        'letter_spacing': 0.25,
    }
    
    BODY_SMALL = {
        'font_size': sp(12),
        'line_height': 1.33,
        'font_weight': '400',
        'letter_spacing': 0.4,
    }

# Component Specifications for KivyMD 2.0.1
class MaterialDesign3Components:
    """Material Design 3 Component Specifications
    
    Defines standard dimensions, elevations, and other properties
    for Material Design 3 components in KivyMD 2.0.1 dev0.
    """
    
    # Elevation levels
    ELEVATION = {
        'level0': 0,
        'level1': dp(1),
        'level2': dp(3),
        'level3': dp(6),
        'level4': dp(8),
        'level5': dp(12),
    }
    
    # Corner radius
    CORNER_RADIUS = {
        'none': 0,
        'extra_small': dp(4),
        'small': dp(8),
        'medium': dp(12),
        'large': dp(16),
        'extra_large': dp(28),
        'full': dp(1000),  # Fully rounded
    }
    
    # Standard component dimensions
    DIMENSIONS = {
    # 全局文本框高度（KivyMD 2.0.1 dev0 规范值）
    'text_field_height': dp(56),                     # 单行标准高度（符合Material Design 3规范）
    'text_field_dense_height': dp(48),               # 紧凑型
    'text_field_large_height': dp(64),               # 大尺寸
    # 对话框专用高度（遵循KivyMD 2.0.1 dev0规范）
    'text_field_dialog_height': dp(56),              # 对话框单行文本框标准高度
    'text_field_multiline_height': dp(80),           # 多行常用高度（增加以适应内容）
    'text_field_multiline_dialog_height': dp(80),   # 对话框多行文本框高度

    # 药物对话框高度配置（KivyMD 2.0.1 dev0规范优化）
    'med_dialog_compact_height': dp(650),            # 对话框整体高度（标准尺寸）
    'med_dialog_scroll_height': dp(380),             # 对话框滚动区基准高度
    'med_dialog_expanded_height': dp(750),           # 对话框扩展高度（用于复杂表单，进一步增大）

    # 按钮和容器高度配置
    'button_height': dp(52),                         # 标准按钮容器高度
    'button_row_height': dp(32),                     # 卡片中按钮行高度
    'add_row_height': dp(44),                        # 添加按钮行高度

    # 下拉菜单配置
    'dropdown_menu_height': dp(200),                 # 下拉菜单最大高度

    # 确认对话框配置
    'confirm_label_height': dp(60),                  # 确认标签高度
    'confirm_dialog_container_height': dp(200),      # 确认对话框容器高度
    'confirm_dialog_height': dp(250),                # 确认对话框总高度

    # 对话框布局配置
    'dialog_title_height': dp(56),                   # 对话框标题高度
    'dialog_padding': dp(20),                        # 对话框内边距
    'dialog_spacing': dp(20),                        # 对话框元素间距
    'button_spacing': dp(12),                        # 按钮间距
    'field_spacing': dp(4),                          # 字段间小间距
    'field_spacing_medium': dp(8),                   # 字段间中等间距
    'field_spacing_large': dp(12),                   # 字段间大间距
    'box_padding': dp(16),                           # 容器内边距
    
    # 图标尺寸配置
    'icon_size_small': dp(20),                       # 小图标尺寸
    'icon_size_medium': dp(24),                      # 中等图标尺寸
    'icon_size_large': dp(32),                       # 大图标尺寸
    
    # 字体大小配置（KivyMD 2.0.1 dev0规范）
    'font_size_xs': dp(12),                          # 超小字体尺寸
    'font_size_small': dp(14),                       # 小字体尺寸
    'font_size_medium': dp(16),                      # 中等字体尺寸
    'font_size_large': dp(18),                       # 大字体尺寸
    'font_size_xl': dp(20),                          # 超大字体尺寸
    
    # 下拉菜单扩展配置
    'dropdown_menu_width': dp(240),                  # 下拉菜单宽度

    # 其余原有键保持不变，可在此继续添加 ...
}
    
    # Spacing system
    SPACING = {
        'none': 0,
        'extra_small': dp(4),
        'small': dp(8),
        'medium': dp(16),
        'large': dp(24),
        'extra_large': dp(32),
    }
    
    # Icon sizes
    ICON_SIZES = {
        'small': dp(16),
        'medium': dp(24),
        'large': dp(32),
        'extra_large': dp(48),
    }
    
    # Dialog positioning
    DIALOG_POSITIONS = {
        'center_y': 0.5,  # 默认垂直居中
        'center_x': 0.5,  # 默认水平居中
        'medication_dialog_center_y': 0.4,  # 药物管理对话框垂直位置
        'form_dialog_center_y': 0.45,  # 表单对话框垂直位置
        'reminder_dialog_center_y': 0.45,  # 提醒对话框垂直位置
        'confirmation_dialog_center_y': 0.5,  # 确认对话框垂直位置
    }

# KivyMD 2.0.1 Theme Manager Integration
class HealthAppThemeManager:
    """Health App Theme Manager for KivyMD 2.0.1 dev0
    
    Integrates with KivyMD's ThemeManager to provide proper
    theme configuration and management.
    """
    
    def __init__(self, app):
        self.app = app
        self.theme_manager = app.theme_cls
        self.health_theme = HealthAppTheme()
        self._configure_theme()
    
    def _configure_theme(self):
        """Configure KivyMD theme with health app specifications"""
        # Set primary palette
        self.theme_manager.primary_palette = "Purple"
        self.theme_manager.primary_hue = "500"
        
        # Set accent palette
        self.theme_manager.accent_palette = "Pink"
        self.theme_manager.accent_hue = "A200"
        
        # Set theme style
        self.theme_manager.theme_style = "Light"
        
        # Set Material Design version
        self.theme_manager.material_style = "M3"
        
        # Configure custom colors
        self._setup_custom_colors()
    
    def _setup_custom_colors(self):
        """Setup custom colors for health app"""
        # Add health-specific colors to theme
        for color_name, color_value in self.health_theme.HEALTH_COLORS.items():
            setattr(self.theme_manager, color_name, color_value)
        
        # Add medical category colors
        for color_name, color_value in self.health_theme.MEDICAL_COLORS.items():
            setattr(self.theme_manager, color_name, color_value)
    
    def switch_theme(self):
        """Switch between light and dark themes"""
        if self.theme_manager.theme_style == "Light":
            self.theme_manager.theme_style = "Dark"
            self.health_theme = HealthAppTheme("Dark")
        else:
            self.theme_manager.theme_style = "Light"
            self.health_theme = HealthAppTheme("Light")
        
        self._setup_custom_colors()
    
    def get_color(self, color_name):
        """Get color from theme"""
        # Try to get from KivyMD theme first
        if hasattr(self.theme_manager, color_name):
            return getattr(self.theme_manager, color_name)
        
        # Fall back to health theme
        return self.health_theme.get_color(color_name)
    
    def get_health_color(self, color_name):
        """Get health-specific color"""
        return self.health_theme.get_health_color(color_name)
    
    def get_medical_color(self, color_name):
        """Get medical category color"""
        return self.health_theme.get_medical_color(color_name)

# Backward Compatibility Layer
class AppTheme:
    """Backward compatibility layer for existing code
    
    This class provides backward compatibility with the old theme system
    while using the new KivyMD 2.0.1 dev0 compliant theme underneath.
    """
    
    def __init__(self):
        self._health_theme = HealthAppTheme()
        self._setup_legacy_colors()
        # Add DIMENSIONS for backward compatibility
        self.DIMENSIONS = self._health_theme.DIMENSIONS
    
    def _setup_legacy_colors(self):
        """Setup legacy color attributes for backward compatibility"""
        # Primary colors
        self.PRIMARY_COLOR = self._health_theme.get_color('primary')
        self.PRIMARY_DARK = get_color_from_hex('#1976D2')
        self.PRIMARY_LIGHT = self._health_theme.get_color('primary_container')
        self.PRIMARY_MEDIUM = get_color_from_hex('#64B5F6')
        
        # Accent colors
        self.ACCENT_COLOR = self._health_theme.get_color('secondary')
        self.ACCENT_DARK = get_color_from_hex('#C2185B')
        self.ACCENT_LIGHT = get_color_from_hex('#F8BBD0')
        
        # Background colors - restored to original colors
        self.BACKGROUND_COLOR = get_color_from_hex('#FAFAFA')  # 浅灰色背景
        self.CARD_BACKGROUND = get_color_from_hex('#FFFFFF')   # 白色卡片背景
        self.SURFACE_COLOR = get_color_from_hex('#FFFFFF')     # 白色表面
        self.SURFACE_CONTAINER_COLOR = get_color_from_hex('#F3EDF7')  # 表面容器颜色
        
        # Text colors
        self.TEXT_PRIMARY = self._health_theme.get_color('on_surface')
        self.TEXT_SECONDARY = self._health_theme.get_color('on_surface_variant')
        self.TEXT_LIGHT = self._health_theme.get_color('on_primary')
        self.TEXT_ON_PRIMARY = self._health_theme.get_color('on_primary')
        self.ON_SURFACE = self._health_theme.get_color('on_surface')
        
        # Status colors
        self.SUCCESS_COLOR = self._health_theme.get_health_color('health_green')
        self.WARNING_COLOR = self._health_theme.get_health_color('health_yellow')
        self.ERROR_COLOR = self._health_theme.get_color('error')
        self.INFO_COLOR = self._health_theme.get_health_color('health_blue')
        
        # Health colors
        self.HEALTH_GREEN = self._health_theme.get_health_color('health_green')
        self.HEALTH_BLUE = self._health_theme.get_health_color('health_blue')
        self.HEALTH_ORANGE = self._health_theme.get_health_color('health_orange')
        self.HEALTH_PURPLE = self._health_theme.get_health_color('health_purple')
        self.HEALTH_RED = self._health_theme.get_health_color('health_red')
        self.HEALTH_DATA_COLOR = self._health_theme.get_health_color('health_green')
        self.HEALTH_RISK_COLOR = self._health_theme.get_health_color('health_orange')
        self.MEDICAL_SERVICE_COLOR = self._health_theme.get_health_color('health_blue')
        
        # Medical colors
        self.HOSPITAL_COLOR = self._health_theme.get_medical_color('hospital_color')
        self.OUTPATIENT_COLOR = self._health_theme.get_medical_color('outpatient_color')
        self.LAB_COLOR = self._health_theme.get_medical_color('lab_color')
        self.TECH_COLOR = self._health_theme.get_medical_color('tech_color')
        
        # Medical category colors (additional aliases)
        self.INPATIENT_COLOR = self._health_theme.get_health_color('health_red')
        self.LAB_REPORT_COLOR = self._health_theme.get_health_color('health_green')
        self.IMAGING_COLOR = self._health_theme.get_health_color('health_blue')
        self.MEDICATION_COLOR = self._health_theme.get_health_color('health_purple')
        self.OTHER_RECORD_COLOR = get_color_from_hex('#607D8B')
        
        # Additional colors for backward compatibility
        self.BORDER_COLOR = get_color_from_hex('#E0E0E0')  # 浅灰色边框
        self.DIVIDER_COLOR = get_color_from_hex('#EEEEEE')  # 分隔线颜色
        self.INPUT_BACKGROUND = get_color_from_hex('#F8F8F8')  # 输入框背景色
        self.ELEVATED_SURFACE = get_color_from_hex('#FAFAFA')  # 浮起表面颜色
        self.SHADOW_COLOR = get_color_from_hex('#000000')  # 阴影颜色
        self.TEXT_HINT = get_color_from_hex('#9E9E9E')  # 提示文本颜色
        
        # Backward compatibility attributes for medication_management_screen.py
        self.BACKGROUND_COLOR = get_color_from_hex('#F5F5F5')  # 浅灰色背景
        self.PRIMARY_COLOR = get_color_from_hex('#2196F3')  # 主色调蓝色
        self.PRIMARY_DARK = get_color_from_hex('#1976D2')  # 深蓝色
        self.PRIMARY_LIGHT = get_color_from_hex('#E3F2FD')  # 超浅蓝色
        self.ACCENT_COLOR = get_color_from_hex('#FF4081')  # 强调色粉色
        self.CARD_BACKGROUND = get_color_from_hex('#FFFFFF')  # 白色卡片背景
        self.SURFACE_COLOR = get_color_from_hex('#FFFFFF')  # 表面颜色
        self.TEXT_PRIMARY = get_color_from_hex('#212121')  # 深灰色主文本
        self.TEXT_SECONDARY = get_color_from_hex('#757575')  # 中灰色次要文本
        self.TEXT_LIGHT = get_color_from_hex('#FFFFFF')  # 白色文本
        self.SUCCESS_COLOR = get_color_from_hex('#4CAF50')  # 成功绿色
        self.WARNING_COLOR = get_color_from_hex('#FFC107')  # 警告黄色
        self.ERROR_COLOR = get_color_from_hex('#F44336')  # 错误红色
        self.INFO_COLOR = get_color_from_hex('#2196F3')  # 信息蓝色
        
        # KivyMD 2.0.1 dev0 compatibility attributes
        self.primaryColor = self.PRIMARY_COLOR  # 兼容KivyMD 2.0.1 dev0的primaryColor属性
        self.onPrimaryColor = self.TEXT_LIGHT  # 主色上的文本颜色
        self.surfaceColor = self.SURFACE_COLOR  # 表面颜色
        self.onSurfaceColor = self.TEXT_PRIMARY  # 表面上的文本颜色
        self.errorColor = self.ERROR_COLOR  # 错误颜色
        self.onErrorColor = self.TEXT_LIGHT  # 错误颜色上的文本颜色

# Application Metrics (unchanged for compatibility)
class AppMetrics:
    """Application metrics and dimensions"""
    
    # Font sizes
    FONT_SIZE_XL = dp(24)
    FONT_SIZE_LARGE = dp(20)
    FONT_SIZE_MEDIUM = dp(18)
    FONT_SIZE_NORMAL = dp(16)
    FONT_SIZE_SMALL = dp(14)
    FONT_SIZE_XS = dp(12)
    
    # Spacing
    SPACING_XL = dp(32)
    SPACING_LARGE = dp(24)
    SPACING_MEDIUM = dp(16)
    SPACING_NORMAL = dp(12)
    SPACING_SMALL = dp(8)
    SPACING_XS = dp(4)
    
    # Padding
    PADDING_XL = dp(24)
    PADDING_LARGE = dp(20)
    PADDING_MEDIUM = dp(16)
    PADDING_NORMAL = dp(12)
    PADDING_SMALL = dp(8)
    PADDING_XS = dp(4)
    
    # Component heights
    HEIGHT_XL = dp(64)
    HEIGHT_LARGE = dp(56)
    HEIGHT_MEDIUM = dp(48)
    HEIGHT_NORMAL = dp(40)
    HEIGHT_SMALL = dp(32)
    HEIGHT_XS = dp(24)
    
    # Button dimensions
    BUTTON_HEIGHT_PRIMARY = dp(48)
    BUTTON_HEIGHT_SECONDARY = dp(40)
    BUTTON_WIDTH = dp(200)
    
    # Input dimensions
    INPUT_HEIGHT = dp(48)
    
    # Icon sizes
    ICON_SIZE_LARGE = dp(32)
    ICON_SIZE_MEDIUM = dp(24)
    ICON_SIZE_SMALL = dp(18)
    
    # Card properties
    CARD_ELEVATION = dp(2)
    CARD_CORNER_RADIUS = dp(16)
    BUTTON_CORNER_RADIUS = dp(8)
    CORNER_RADIUS = dp(8)
    
    # Navigation
    NAVBAR_HEIGHT = dp(56)
    STATUSBAR_HEIGHT = dp(24)
    APPBAR_HEIGHT = dp(56)
    
    # Lists
    LIST_ITEM_HEIGHT = dp(56)
    GRID_SPACING = dp(16)
    
    # Screen dimensions
    SCREEN_WIDTH = 392
    SCREEN_HEIGHT = 850
    
    # Breakpoints
    BREAKPOINT_SMALL = dp(600)
    BREAKPOINT_MEDIUM = dp(905)
    BREAKPOINT_LARGE = dp(1240)
    
    # Forms
    FORM_FIELD_SPACING = dp(16)
    FORM_GROUP_SPACING = dp(24)
    CHECKBOX_SIZE = dp(20)
    
    # Animation
    ANIMATION_DURATION_SHORT = 0.2
    ANIMATION_DURATION_MEDIUM = 0.3
    ANIMATION_DURATION_LONG = 0.5
    
    # Module cards
    MODULE_CARD_WIDTH = dp(160)
    MODULE_CARD_HEIGHT = dp(120)
    HEALTH_CARD_HEIGHT = dp(80)
    
    @staticmethod
    def get_responsive_width(screen_width, percent=0.9):
        return screen_width * percent
    
    @staticmethod
    def get_responsive_padding(screen_width):
        if screen_width <= AppMetrics.BREAKPOINT_SMALL:
            return dp(16)
        elif screen_width <= AppMetrics.BREAKPOINT_MEDIUM:
            return dp(24)
        else:
            return dp(32)

# Theme Manager for easy access
class ThemeManager:
    """Simplified theme manager for the application"""
    
    @staticmethod
    def get_theme(theme_name='light'):
        """Get theme instance"""
        if theme_name.lower() == 'dark':
            return HealthAppTheme('Dark')
        else:
            return HealthAppTheme('Light')
    
    @staticmethod
    def get_app_theme():
        """Get backward compatible app theme"""
        return AppTheme()

# Font Manager for backward compatibility
class FontManager:
    """字体管理器，提供统一的字体文件和名称管理
    
    这个类负责管理应用中使用的所有字体文件和字体名称，
    提供统一的字体获取接口，确保字体配置的一致性。
    """
    
    # 默认字体配置
    DEFAULT_FONT = "NotoSans"
    DEFAULT_FONT_FILE = "NotoSans-Regular.ttf"
    
    # 字体文件映射
    FONT_FILES = {
        "regular": "NotoSans-Regular.ttf",
        "medium": "NotoSans-Medium.ttf",
        "light": "NotoSans-Light.ttf",
        "semibold": "NotoSans-SemiBold.ttf",
        "bold": "NotoSans-Bold.ttf",
        "black": "NotoSans-Black.ttf",
        "extrabold": "NotoSans-ExtraBold.ttf",
        "extralight": "NotoSans-ExtraLight.ttf",
        "thin": "NotoSans-Thin.ttf"
    }
    
    # 字体名称映射
    FONT_NAMES = {
        "regular": "NotoSans",
        "medium": "NotoSans-Medium",
        "light": "NotoSans-Light",
        "semibold": "NotoSans-SemiBold",
        "bold": "NotoSans-Bold",
        "black": "NotoSans-Black",
        "extrabold": "NotoSans-ExtraBold",
        "extralight": "NotoSans-ExtraLight",
        "thin": "NotoSans-Thin"
    }
    
    @staticmethod
    def get_font_path(weight="regular"):
        """获取指定字重的字体文件路径
        
        Args:
            weight: 字体字重
            
        Returns:
            字体文件路径
        """
        try:
            from kivy.app import App  # type: ignore
            from os.path import join
            app = App.get_running_app()
            if app and hasattr(app, 'resource_path'):
                return join(app.resource_path, "assets", "fonts", FontManager.FONT_FILES.get(weight, FontManager.DEFAULT_FONT_FILE))
        except ImportError:
            pass
        # 如果无法获取App实例，使用默认路径
        from os.path import join, dirname, abspath
        import os
        base_path = dirname(abspath(__file__))
        return join(base_path, "assets", "fonts", FontManager.FONT_FILES.get(weight, FontManager.DEFAULT_FONT_FILE))
    
    @staticmethod
    def get_font_name(weight="regular"):
        """获取指定字重的字体名称
        
        Args:
            weight: 字体字重
            
        Returns:
            字体名称
        """
        return FontManager.FONT_NAMES.get(weight, FontManager.DEFAULT_FONT)

# Base Font Configuration for backward compatibility
class BaseFontConfig:
    """基础字体配置类
    
    这个类定义了应用中所有文本元素的基础字体配置，包括字体大小、行高和字重。
    作为FontStyles和KivyMDFontStyles的基础，确保整个应用的字体配置一致。
    """
    
    # 标题字体大小和行高 - 恢复到原始大小
    TITLE_LARGE_SIZE = dp(20)
    TITLE_LARGE_LINE_HEIGHT = 1.4
    
    TITLE_MEDIUM_SIZE = dp(16)
    TITLE_MEDIUM_LINE_HEIGHT = 1.3
    
    TITLE_SMALL_SIZE = dp(14)
    TITLE_SMALL_LINE_HEIGHT = 1.2
    
    # 副标题字体大小和行高 - 恢复到原始大小
    SUBTITLE_LARGE_SIZE = dp(14)
    SUBTITLE_LARGE_LINE_HEIGHT = 1.3
    
    SUBTITLE_MEDIUM_SIZE = dp(12)
    SUBTITLE_MEDIUM_LINE_HEIGHT = 1.2
    
    SUBTITLE_SMALL_SIZE = dp(10)
    SUBTITLE_SMALL_LINE_HEIGHT = 1.1
    
    # 正文字体大小和行高 - 恢复到原始大小
    BODY_LARGE_SIZE = dp(14)
    BODY_LARGE_LINE_HEIGHT = 1.3
    
    BODY_MEDIUM_SIZE = dp(12)
    BODY_MEDIUM_LINE_HEIGHT = 1.2
    
    BODY_SMALL_SIZE = dp(10)
    BODY_SMALL_LINE_HEIGHT = 1.1
    
    # 按钮字体大小和行高 - 恢复到原始大小
    BUTTON_LARGE_SIZE = dp(14)
    BUTTON_LARGE_LINE_HEIGHT = 1.3
    
    BUTTON_MEDIUM_SIZE = dp(12)
    BUTTON_MEDIUM_LINE_HEIGHT = 1.2
    
    BUTTON_SMALL_SIZE = dp(10)
    BUTTON_SMALL_LINE_HEIGHT = 1.1
    
    # 标签字体大小和行高 - 恢复到原始大小
    LABEL_LARGE_SIZE = dp(12)
    LABEL_LARGE_LINE_HEIGHT = 1.2
    
    LABEL_MEDIUM_SIZE = dp(10)
    LABEL_MEDIUM_LINE_HEIGHT = 1.1
    
    LABEL_SMALL_SIZE = dp(8)
    LABEL_SMALL_LINE_HEIGHT = 1.0
    
    # 提示文本字体大小和行高 - 恢复到原始大小
    HINT_TEXT_SIZE = dp(10)
    HINT_TEXT_LINE_HEIGHT = 1.1
    
    # 错误文本字体大小和行高 - 恢复到原始大小
    ERROR_TEXT_SIZE = dp(10)
    ERROR_TEXT_LINE_HEIGHT = 1.1
    
    # 标题字体大小和行高 (Headline) - restored to original sizes
    HEADLINE_LARGE_SIZE = dp(24)
    HEADLINE_LARGE_LINE_HEIGHT = 1.2
    
    HEADLINE_MEDIUM_SIZE = dp(20)
    HEADLINE_MEDIUM_LINE_HEIGHT = 1.3
    
    HEADLINE_SMALL_SIZE = dp(18)
    HEADLINE_SMALL_LINE_HEIGHT = 1.4
    
    # 显示字体大小和行高 (Display) - restored to original sizes
    DISPLAY_LARGE_SIZE = dp(32)
    DISPLAY_LARGE_LINE_HEIGHT = 1.1
    
    DISPLAY_MEDIUM_SIZE = dp(28)
    DISPLAY_MEDIUM_LINE_HEIGHT = 1.2
    
    DISPLAY_SMALL_SIZE = dp(24)
    DISPLAY_SMALL_LINE_HEIGHT = 1.3
    ERROR_TEXT_LINE_HEIGHT = 1.3
    
    # 链接文本字体大小和行高 - restored to original sizes
    LINK_TEXT_SIZE = dp(12)
    LINK_TEXT_LINE_HEIGHT = 1.3

# Font Styles for backward compatibility
class FontStyles:
    """Kivy字体样式定义，提供统一的Kivy文本元素样式设置
    
    这个类定义了应用中所有常规Kivy文本元素的标准字体样式，如Label, Button等。
    样式格式适用于标准Kivy组件，通过普通字典格式提供样式配置。
    主要用于非KivyMD组件的样式配置。
    """
    
    # 标题样式
    TITLE_LARGE = {
        'font_size': BaseFontConfig.TITLE_LARGE_SIZE,
        'font_name': FontManager.get_font_name('medium'),
        'color': (0, 0, 0, 1),  # 黑色
        'bold': True
    }
    
    TITLE_MEDIUM = {
        'font_size': BaseFontConfig.TITLE_MEDIUM_SIZE,
        'font_name': FontManager.get_font_name('medium'),
        'color': (0, 0, 0, 1),  # 黑色
        'bold': True
    }
    
    TITLE_SMALL = {
        'font_size': BaseFontConfig.TITLE_SMALL_SIZE,
        'font_name': FontManager.get_font_name('medium'),
        'color': (0, 0, 0, 1),  # 黑色
        'bold': True
    }
    
    # 正文样式
    BODY_LARGE = {
        'font_size': BaseFontConfig.BODY_LARGE_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0, 0, 0, 1),  # 黑色
        'bold': False
    }
    
    BODY_MEDIUM = {
        'font_size': BaseFontConfig.BODY_MEDIUM_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0, 0, 0, 1),  # 黑色
        'bold': False
    }
    
    BODY_SMALL = {
        'font_size': BaseFontConfig.BODY_SMALL_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0, 0, 0, 1),  # 黑色
        'bold': False
    }
    
    # 按钮样式
    BUTTON_LARGE = {
        'font_size': BaseFontConfig.BUTTON_LARGE_SIZE,
        'font_name': FontManager.get_font_name('medium'),
        'color': (0.7, 0.5, 0.6, 1),  # 紫色
        'bold': False
    }
    
    BUTTON_MEDIUM = {
        'font_size': BaseFontConfig.BUTTON_MEDIUM_SIZE,
        'font_name': FontManager.get_font_name('medium'),
        'color': (1, 1, 1, 1),  # 白色
        'bold': False
    }
    
    BUTTON_SMALL = {
        'font_size': BaseFontConfig.BUTTON_SMALL_SIZE,
        'font_name': FontManager.get_font_name('medium'),
        'color': (1, 1, 1, 1),  # 白色
        'bold': False
    }
    
    # 标签样式
    LABEL_LARGE = {
        'font_size': BaseFontConfig.LABEL_LARGE_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0, 0, 0, 0.87),  # 黑色，轻微透明
        'bold': False
    }
    
    LABEL_MEDIUM = {
        'font_size': BaseFontConfig.LABEL_MEDIUM_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0, 0, 0, 0.87),  # 黑色，轻微透明
        'bold': False
    }
    
    LABEL_SMALL = {
        'font_size': BaseFontConfig.LABEL_SMALL_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0, 0, 0, 0.87),  # 黑色，轻微透明
        'bold': False
    }
    
    # 提示文本样式
    HINT_TEXT = {
        'font_size': BaseFontConfig.HINT_TEXT_SIZE,
        'font_name': FontManager.get_font_name('light'),
        'color': (0, 0, 0, 0.6),  # 黑色，更透明
        'bold': False
    }
    
    # 错误文本样式
    ERROR_TEXT = {
        'font_size': BaseFontConfig.ERROR_TEXT_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0.96, 0.26, 0.21, 1),  # 红色
        'bold': False
    }
    
    # 链接文本样式
    LINK_TEXT = {
        'font_size': BaseFontConfig.LINK_TEXT_SIZE,
        'font_name': FontManager.get_font_name('regular'),
        'color': (0.1, 0.46, 0.82, 1),  # 蓝色
        'bold': False,
        'underline': True
    }
    
    # Logo样式
    LOGO_TITLE = {
        'font_size': sp(32),  # 使用 sp 单位
        'font_name': FontManager.get_font_name('bold'),
        'bold': True,
        'color': (0.13, 0.46, 0.82, 1)  # 蓝色
    }
    
    # Logo副标题样式
    LOGO_SUBTITLE = {
        'font_size': sp(14),  # 使用 sp 单位
        'font_name': FontManager.get_font_name('regular'),
        'bold': False,
        'color': (0.4, 0.4, 0.4, 1)  # 灰色
    }

# Export commonly used classes for easy import
class KivyMDFontStyles:
    """KivyMD字体样式定义，提供统一的KivyMD字体样式设置
    
    这个类定义了应用中所有KivyMD文本元素的标准字体样式，包括Body、Label、Headline、Display、Title等。
    样式格式特别适配KivyMD的要求，通过特定的键名和格式提供样式配置。
    主要用于KivyMD组件的样式配置，如MDLabel、MDButton等。
    """
    
    @staticmethod
    def get_font_styles(sp_func):
        """获取KivyMD字体样式配置
        
        Args:
            sp_func: sp函数，用于设置字体大小
            
        Returns:
            KivyMD字体样式配置字典
        """
        return {
            "Icon": {
                "large": {
                    "font-name": "Icons",
                    "font-size": sp_func(24),
                    "line-height": 1.2
                },
                "medium": {
                    "font-name": "Icons",
                    "font-size": sp_func(20),
                    "line-height": 1.2
                },
                "small": {
                    "font-name": "Icons",
                    "font-size": sp_func(16),
                    "line-height": 1.2
                }
            },
            "Body": {
                "large": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.BODY_LARGE_SIZE),
                    "line-height": BaseFontConfig.BODY_LARGE_LINE_HEIGHT
                },
                "medium": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.BODY_MEDIUM_SIZE),
                    "line-height": BaseFontConfig.BODY_MEDIUM_LINE_HEIGHT
                },
                "small": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.BODY_SMALL_SIZE),
                    "line-height": BaseFontConfig.BODY_SMALL_LINE_HEIGHT
                },
            },
            
            "Label": {
                "large": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.LABEL_LARGE_SIZE),
                    "line-height": BaseFontConfig.LABEL_LARGE_LINE_HEIGHT
                },
                "medium": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.LABEL_MEDIUM_SIZE),
                    "line-height": BaseFontConfig.LABEL_MEDIUM_LINE_HEIGHT
                },
                "small": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.LABEL_SMALL_SIZE),
                    "line-height": BaseFontConfig.LABEL_SMALL_LINE_HEIGHT
                },
            },
            
            "Headline": {
                "large": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.HEADLINE_LARGE_SIZE),
                    "line-height": BaseFontConfig.HEADLINE_LARGE_LINE_HEIGHT
                },
                "medium": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.HEADLINE_MEDIUM_SIZE),
                    "line-height": BaseFontConfig.HEADLINE_MEDIUM_LINE_HEIGHT
                },
                "small": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.HEADLINE_SMALL_SIZE),
                    "line-height": BaseFontConfig.HEADLINE_SMALL_LINE_HEIGHT
                },
            },
            
            "Display": {
                "large": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.DISPLAY_LARGE_SIZE),
                    "line-height": BaseFontConfig.DISPLAY_LARGE_LINE_HEIGHT
                },
                "medium": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.DISPLAY_MEDIUM_SIZE),
                    "line-height": BaseFontConfig.DISPLAY_MEDIUM_LINE_HEIGHT
                },
                "small": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.DISPLAY_SMALL_SIZE),
                    "line-height": BaseFontConfig.DISPLAY_SMALL_LINE_HEIGHT
                },
            },
            
            "Title": {
                "large": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.TITLE_LARGE_SIZE),
                    "line-height": BaseFontConfig.TITLE_LARGE_LINE_HEIGHT
                },
                "medium": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.TITLE_MEDIUM_SIZE),
                    "line-height": BaseFontConfig.TITLE_MEDIUM_LINE_HEIGHT
                },
                "small": {
                    "font-name": "NotoSans", 
                    "font-size": sp_func(BaseFontConfig.TITLE_SMALL_SIZE),
                    "line-height": BaseFontConfig.TITLE_SMALL_LINE_HEIGHT
                },
            },
   
        }

# Create global instance for backward compatibility
AppThemeInstance = AppTheme()

# For backward compatibility, AppTheme is both a class and an instance
# This is a workaround for Pyright type checking
AppTheme = AppThemeInstance  # type: ignore
AppThemeType = AppThemeInstance  # type: ignore

__all__ = [
    'MaterialDesign3Colors',
    'HealthAppTheme',
    'MaterialDesign3Typography',
    'MaterialDesign3Components',
    'HealthAppThemeManager',
    'AppTheme',
    'AppThemeInstance',
    'AppThemeType',
    'AppMetrics',
    'FontManager',
    'BaseFontConfig',
    'FontStyles',
    'KivyMDFontStyles',
    'ThemeManager',
]