"""用户管理模块
该模块提供用户账户管理功能，包括用户切换、用户信息管理等。
所有用户数据统一存储在user_data.json文件中。
"""

import os
import json
from datetime import datetime, timedelta
import time
from typing import Dict, Optional

# 导入存储工具
from .storage import UserStorage

# 用户数据文件路径
MODULE_DIR = os.path.dirname(os.path.abspath(__file__))
APP_DIR = os.path.dirname(MODULE_DIR)
USER_DATA_FILE = os.path.join(APP_DIR, "user_data.json")

class UserAccount:
    """用户账户类，表示一个用户账户的数据结构"""

    def __init__(self, user_id=None, username=None, password=None, phone=None, full_name=None, role=None, id_number=None, gender=None, avatar=None, last_login=None, roles=None, custom_id=None):
        """初始化用户账户

        Args:
            user_id (str, optional): 用户ID（已弃用，仅用于兼容旧代码）
            username (str): 用户名
            password (str, optional): 密码
            phone (str, optional): 手机号
            full_name (str, optional): 真实姓名（替代旧的real_name）
            role (str, optional): 用户角色（替代旧的identity）
            id_number (str, optional): 身份证号
            gender (str, optional): 性别
            avatar (str, optional): 用户头像URL
            last_login (str, optional): 最后登录时间
            roles (list, optional): 用户角色列表
            custom_id (str, optional): 后端生成的带前缀的格式化ID（用户唯一标识）
        """
        # user_id 已弃用，仅用于兼容旧代码
        self.user_id = user_id
        self.username = username
        self.password = password
        self.phone = phone
        self.full_name = full_name or username  # 使用full_name替代real_name
        self.role = role or '个人用户'  # 使用role替代identity
        self.id_number = id_number
        self.gender = gender
        self.avatar = avatar
        self.last_login = last_login or datetime.now().isoformat()
        self.roles = roles or []
        self.custom_id = custom_id  # 后端生成的带前缀的格式化ID
        
        # 兼容性属性（已弃用）
        self.real_name = self.full_name  # 兼容旧代码
        self.identity = self.role  # 兼容旧代码
        
        # 新增属性
        self.email = None
        self.created_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()
        self.is_active = True
        self.preferences = {}
        self.sync_status = 'pending'  # pending, synced, failed
        self.last_sync: Optional[str] = None  # 明确指定类型

        # 健康相关信息
        self.allergies = ''
        self.medical_history = ''
        self.current_medications = ''
        self.emergency_contact = ''
        self.emergency_phone = ''
        self.birth_date = ''
        self.id_card = ''

    def to_dict(self):
        """将用户账户转换为字典格式"""
        return {
            'user_id': self.user_id,  # 保留用于兼容
            'username': self.username,
            'password': self.password,
            'phone': self.phone,
            'full_name': self.full_name,
            'role': self.role,
            'id_number': self.id_number,
            'gender': self.gender,
            'avatar': self.avatar,
            'last_login': self.last_login,
            'roles': self.roles,
            'custom_id': self.custom_id,
            'email': getattr(self, 'email', None),
            'created_at': getattr(self, 'created_at', datetime.now().isoformat()),
            'updated_at': getattr(self, 'updated_at', datetime.now().isoformat()),
            'is_active': getattr(self, 'is_active', True),
            'preferences': getattr(self, 'preferences', {}),
            'sync_status': getattr(self, 'sync_status', 'pending'),
            'last_sync': getattr(self, 'last_sync', None),
            # 健康相关字段
            'allergies': getattr(self, 'allergies', ''),
            'medical_history': getattr(self, 'medical_history', ''),
            'current_medications': getattr(self, 'current_medications', ''),
            'emergency_contact': getattr(self, 'emergency_contact', ''),
            'emergency_phone': getattr(self, 'emergency_phone', ''),
            'birth_date': getattr(self, 'birth_date', ''),
            'id_card': getattr(self, 'id_card', ''),
            # 兼容性字段
            'real_name': self.full_name,
            'identity': self.role
        }

    @classmethod
    def from_dict(cls, data):
        """从字典创建用户账户对象"""
        account = cls(
            user_id=data.get('user_id'),
            username=data.get('username'),
            password=data.get('password'),
            phone=data.get('phone'),
            full_name=data.get('full_name') or data.get('real_name'),  # 兼容旧字段
            role=data.get('role') or data.get('identity', '个人用户'),  # 兼容旧字段
            id_number=data.get('id_number'),
            gender=data.get('gender'),
            avatar=data.get('avatar'),
            last_login=data.get('last_login'),
            roles=data.get('roles', []),
            custom_id=data.get('custom_id')
        )
        
        # 设置额外属性
        account.email = data.get('email')
        account.created_at = data.get('created_at', datetime.now().isoformat())
        account.updated_at = data.get('updated_at', datetime.now().isoformat())
        account.is_active = data.get('is_active', True)
        account.preferences = data.get('preferences', {})
        account.sync_status = data.get('sync_status', 'pending')
        account.last_sync = data.get('last_sync')
        
        # 添加健康相关字段支持
        account.allergies = data.get('allergies', '')
        account.medical_history = data.get('medical_history', '')
        account.current_medications = data.get('current_medications', '')
        account.emergency_contact = data.get('emergency_contact', '')
        account.emergency_phone = data.get('emergency_phone', '')
        account.birth_date = data.get('birth_date', '')
        account.id_card = data.get('id_card', '')
        
        return account

    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()

    def get_display_name(self):
        """获取显示名称"""
        return self.full_name or self.username

    def has_role(self, role_name):
        """检查用户是否具有指定角色"""
        return role_name in self.roles or self.role == role_name

    def add_role(self, role_name):
        """添加角色"""
        if role_name not in self.roles:
            self.roles.append(role_name)
            self.updated_at = datetime.now().isoformat()

    def remove_role(self, role_name):
        """移除角色"""
        if role_name in self.roles:
            self.roles.remove(role_name)
            self.updated_at = datetime.now().isoformat()

    def set_preference(self, key, value):
        """设置用户偏好"""
        self.preferences[key] = value
        self.updated_at = datetime.now().isoformat()

    def get_preference(self, key, default=None):
        """获取用户偏好"""
        return self.preferences.get(key, default)


class UserManager:
    """用户管理器类，负责管理所有用户账户"""

    def __init__(self):
        """初始化用户管理器"""
        self.accounts = []  # 存储所有用户账户
        self.current_user = None  # 当前登录的用户
        self.storage = UserStorage()  # 存储管理器
        self.load_accounts()  # 加载用户数据

    def load_accounts(self):
        """从文件加载用户账户数据"""
        try:
            if os.path.exists(USER_DATA_FILE):
                with open(USER_DATA_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # 加载用户账户
                accounts_data = data.get('accounts', [])
                self.accounts = [UserAccount.from_dict(account_data) for account_data in accounts_data]
                
                # 加载当前用户
                current_user_data = data.get('current_user')
                if current_user_data:
                    self.current_user = UserAccount.from_dict(current_user_data)
                    
                # print(f"已加载 {len(self.accounts)} 个用户账户")  # 调试日志已注释
            else:
                # print("用户数据文件不存在，创建新的用户管理器")  # 调试日志已注释
                self.accounts = []
                self.current_user = None
                
        except Exception as e:
            # print(f"加载用户数据失败: {e}")  # 调试日志已注释
            self.accounts = []
            self.current_user = None

    def save_accounts(self):
        """保存用户账户数据到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(USER_DATA_FILE), exist_ok=True)
            
            data = {
                'accounts': [account.to_dict() for account in self.accounts],
                'current_user': self.current_user.to_dict() if self.current_user else None,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(USER_DATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            # print(f"已保存 {len(self.accounts)} 个用户账户")  # 调试日志已注释
            return True
            
        except Exception as e:
            # print(f"保存用户数据失败: {e}")  # 调试日志已注释
            return False

    def create_account(self, username, password=None, full_name=None, **kwargs):
        """创建新用户账户（别名方法）
        
        Args:
            username (str): 用户名
            password (str, optional): 密码
            full_name (str, optional): 真实姓名
            **kwargs: 其他参数
            
        Returns:
            UserAccount: 新创建的用户账户对象，如果创建失败返回None
        """
        return self.add_account(username, password, full_name=full_name, **kwargs)

    def add_account(self, username, password=None, phone=None, full_name=None, role=None, 
                   id_number=None, gender=None, avatar=None, custom_id=None, **kwargs):
        """添加新用户账户
        
        Args:
            username (str): 用户名
            password (str, optional): 密码
            phone (str, optional): 手机号
            full_name (str, optional): 真实姓名
            role (str, optional): 用户角色
            id_number (str, optional): 身份证号
            gender (str, optional): 性别
            avatar (str, optional): 用户头像URL
            custom_id (str, optional): 后端生成的带前缀的格式化ID
            **kwargs: 其他参数
            
        Returns:
            UserAccount: 新创建的用户账户对象，如果创建失败返回None
        """
        try:
            # 检查用户名是否已存在
            if self.get_account_by_username(username):
                # print(f"用户名 {username} 已存在")  # 调试日志已注释
                return None
                
            # 检查custom_id是否已存在
            if custom_id and self.get_account_by_custom_id(custom_id):
                # print(f"Custom ID {custom_id} 已存在")  # 调试日志已注释
                return None
            
            # 生成临时user_id（用于兼容）
            temp_user_id = f"TEMP_{int(time.time())}"
            
            # 创建新用户账户
            new_account = UserAccount(
                user_id=temp_user_id,  # 临时ID，用于兼容
                username=username,
                password=password,
                phone=phone,
                full_name=full_name,
                role=role,
                id_number=id_number,
                gender=gender,
                avatar=avatar,
                custom_id=custom_id
            )
            
            # 设置额外属性
            for key, value in kwargs.items():
                if hasattr(new_account, key):
                    setattr(new_account, key, value)
            
            # 添加到账户列表
            self.accounts.append(new_account)
            
            # 如果没有当前用户，设置为当前用户
            if not self.current_user:
                self.current_user = new_account
            
            # 保存数据
            self.save_accounts()
            
            # print(f"成功添加用户: {username} (Custom ID: {custom_id})")  # 调试日志已注释
            return new_account
            
        except Exception as e:
            # print(f"添加用户账户失败: {e}")  # 调试日志已注释
            return None

    def get_account_by_username(self, username):
        """根据用户名获取用户账户"""
        for account in self.accounts:
            if account.username == username:
                return account
        return None

    def get_account_by_custom_id(self, custom_id):
        """根据custom_id获取用户账户"""
        if not custom_id:
            return None
        for account in self.accounts:
            if hasattr(account, 'custom_id') and account.custom_id == custom_id:
                return account
        return None

    def get_account_by_id(self, user_id):
        """根据user_id获取用户账户（已弃用，仅用于兼容）"""
        for account in self.accounts:
            if account.user_id == user_id:
                return account
        return None

    def get_user_by_custom_id(self, custom_id):
        """根据custom_id获取用户（别名方法）"""
        return self.get_account_by_custom_id(custom_id)

    def get_all_accounts(self):
        """获取所有用户账户"""
        return self.accounts.copy()

    def get_current_user(self):
        """获取当前登录的用户"""
        return self.current_user

    def get_current_user_id(self):
        """获取当前用户的ID（优先返回custom_id）"""
        if self.current_user:
            # 优先返回custom_id
            if hasattr(self.current_user, 'custom_id') and self.current_user.custom_id:
                return self.current_user.custom_id
            # 兼容旧代码，返回user_id
            return self.current_user.user_id
        return None

    def get_current_user_custom_id(self):
        """获取当前用户的custom_id"""
        if self.current_user and hasattr(self.current_user, 'custom_id'):
            return self.current_user.custom_id
        return None

    def set_current_user(self, user_account, force_save=False):
        """设置当前登录的用户
        
        Args:
            user_account (UserAccount): 要设置为当前用户的账户
            force_save (bool): 是否强制保存到文件
        """
        if user_account:
            self.current_user = user_account
            user_account.update_last_login()
            
            if force_save:
                self.save_accounts()
            
            # print(f"当前用户已设置为: {user_account.username} (Custom ID: {getattr(user_account, 'custom_id', 'None')})")  # 调试日志已注释
        else:
            self.current_user = None
            if force_save:
                self.save_accounts()
            # print("当前用户已清除")  # 调试日志已注释

    def switch_user(self, identifier, force_save=False):
        """切换用户
        
        Args:
            identifier (str): 用户标识符（可以是username、user_id或custom_id）
            force_save (bool): 是否强制保存到文件
            
        Returns:
            UserAccount: 切换后的用户账户，如果切换失败返回None
        """
        # 首先尝试按custom_id查找
        user = self.get_account_by_custom_id(identifier)
        
        # 如果没找到，尝试按username查找
        if not user:
            user = self.get_account_by_username(identifier)
        
        # 如果还没找到，尝试按user_id查找（兼容旧代码）
        if not user:
            user = self.get_account_by_id(identifier)
        
        if user:
            self.set_current_user(user, force_save)
            return user
        else:
            # print(f"未找到用户: {identifier}")  # 调试日志已注释
            return None

    def authenticate(self, username, password):
        """用户认证
        
        Args:
            username (str): 用户名
            password (str): 密码
            
        Returns:
            UserAccount: 认证成功返回用户账户，失败返回None
        """
        user = self.get_account_by_username(username)
        if user and user.password == password:
            self.set_current_user(user, force_save=True)
            return user
        return None

    def remove_account(self, identifier):
        """删除用户账户
        
        Args:
            identifier (str): 用户标识符（可以是username、user_id或custom_id）
            
        Returns:
            bool: 删除成功返回True，失败返回False
        """
        # 查找要删除的用户
        user_to_remove = None
        
        # 首先尝试按custom_id查找
        user_to_remove = self.get_account_by_custom_id(identifier)
        
        # 如果没找到，尝试按username查找
        if not user_to_remove:
            user_to_remove = self.get_account_by_username(identifier)
        
        # 如果还没找到，尝试按user_id查找（兼容旧代码）
        if not user_to_remove:
            user_to_remove = self.get_account_by_id(identifier)
        
        if user_to_remove:
            # 如果要删除的是当前用户，清除当前用户
            if self.current_user == user_to_remove:
                self.current_user = None
            
            # 从账户列表中移除
            self.accounts.remove(user_to_remove)
            
            # 保存更改
            self.save_accounts()
            
            # print(f"已删除用户: {user_to_remove.username}")  # 调试日志已注释
            return True
        else:
            # print(f"未找到要删除的用户: {identifier}")  # 调试日志已注释
            return False

    def delete_account(self, identifier):
        """删除用户账户（别名方法）"""
        return self.remove_account(identifier)

    def verify_password(self, username, password):
        """验证用户密码
        
        Args:
            username (str): 用户名
            password (str): 密码
            
        Returns:
            bool: 验证成功返回True，失败返回False
        """
        user = self.get_account_by_username(username)
        if user and user.password:
            # 导入密码验证函数
            try:
                from .security_config import verify_password
                return verify_password(password, user.password)
            except ImportError:
                # 如果无法导入安全配置，使用简单的字符串比较（仅用于测试）
                return password == user.password
        return False

    def change_password(self, username, old_password, new_password):
        """修改用户密码
        
        Args:
            username (str): 用户名
            old_password (str): 旧密码
            new_password (str): 新密码
            
        Returns:
            bool: 修改成功返回True，失败返回False
        """
        # 验证旧密码
        if not self.verify_password(username, old_password):
            return False
            
        # 查找用户
        user = self.get_account_by_username(username)
        if not user:
            return False
            
        # 更新密码
        user.password = new_password
        user.updated_at = datetime.now().isoformat()
        
        # 保存更改
        self.save_accounts()
        return True

    def update_account(self, identifier, **kwargs):
        """更新用户账户信息
        
        Args:
            identifier (str): 用户标识符（可以是username、user_id或custom_id）
            **kwargs: 要更新的字段
            
        Returns:
            bool: 更新成功返回True，失败返回False
        """
        # 查找要更新的用户
        user_to_update = None
        
        # 首先尝试按custom_id查找
        user_to_update = self.get_account_by_custom_id(identifier)
        
        # 如果没找到，尝试按username查找
        if not user_to_update:
            user_to_update = self.get_account_by_username(identifier)
        
        # 如果还没找到，尝试按user_id查找（兼容旧代码）
        if not user_to_update:
            user_to_update = self.get_account_by_id(identifier)
        
        if user_to_update:
            # 更新字段
            for key, value in kwargs.items():
                if hasattr(user_to_update, key):
                    setattr(user_to_update, key, value)
            
            # 更新时间戳
            user_to_update.updated_at = datetime.now().isoformat()
            
            # 保存更改
            self.save_accounts()
            
            # print(f"已更新用户: {user_to_update.username}")  # 调试日志已注释
            return True
        else:
            # print(f"未找到要更新的用户: {identifier}")  # 调试日志已注释
            return False

    def get_user_info(self):
        """获取当前用户信息（字典格式）"""
        if self.current_user:
            return self.current_user.to_dict()
        return None

    def get_available_identities(self, user_account):
        """获取用户可用的身份列表（兼容旧代码）"""
        if user_account:
            # 确保返回一个列表
            roles = getattr(user_account, "roles", [])
            role = getattr(user_account, "role", "个人用户")
            return roles or [role]
        return ["个人用户"]

    def save_current_user(self, force_save=False):
        """保存当前用户状态"""
        if force_save:
            self.save_accounts()

    def logout(self):
        """用户登出"""
        if self.current_user:
            # print(f"用户 {self.current_user.username} 已登出")  # 调试日志已注释
            self.current_user = None
            self.save_accounts()

    def is_logged_in(self):
        """检查是否有用户登录"""
        return self.current_user is not None

    def get_user_count(self):
        """获取用户总数"""
        return len(self.accounts)

    def search_users(self, query):
        """搜索用户
        
        Args:
            query (str): 搜索关键词
            
        Returns:
            list: 匹配的用户账户列表
        """
        results = []
        query_lower = query.lower()
        
        for account in self.accounts:
            if (account.username and query_lower in account.username.lower() or 
                (account.full_name and query_lower in account.full_name.lower()) or
                (account.phone and query_lower in account.phone) or
                (hasattr(account, 'custom_id') and account.custom_id and query_lower in account.custom_id.lower())):
                results.append(account)
        
        return results

    def get_users_by_role(self, role):
        """根据角色获取用户列表
        
        Args:
            role (str): 角色名称
            
        Returns:
            list: 具有指定角色的用户账户列表
        """
        results = []
        for account in self.accounts:
            if account.has_role(role):
                results.append(account)
        return results

    def export_users(self, file_path=None):
        """导出用户数据
        
        Args:
            file_path (str, optional): 导出文件路径，如果不指定则使用默认路径
            
        Returns:
            bool: 导出成功返回True，失败返回False
        """
        try:
            if not file_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_path = f"user_export_{timestamp}.json"
            
            export_data = {
                'export_time': datetime.now().isoformat(),
                'user_count': len(self.accounts),
                'accounts': [account.to_dict() for account in self.accounts]
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            # print(f"用户数据已导出到: {file_path}")  # 调试日志已注释
            return True
            
        except Exception as e:
            # print(f"导出用户数据失败: {e}")  # 调试日志已注释
            return False

    def import_users(self, file_path):
        """导入用户数据
        
        Args:
            file_path (str): 导入文件路径
            
        Returns:
            bool: 导入成功返回True，失败返回False
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            accounts_data = import_data.get('accounts', [])
            imported_count = 0
            
            for account_data in accounts_data:
                username = account_data.get('username')
                if username and not self.get_account_by_username(username):
                    account = UserAccount.from_dict(account_data)
                    self.accounts.append(account)
                    imported_count += 1
            
            if imported_count > 0:
                self.save_accounts()
                # print(f"成功导入 {imported_count} 个用户账户")  // 调试日志已注释
            else:
                print("没有新用户需要导入")
            
            return True
            
        except Exception as e:
            # print(f"导入用户数据失败: {e}")  // 调试日志已注释
            return False

    def cleanup_inactive_users(self, days=30):
        """清理不活跃用户
        
        Args:
            days (int): 不活跃天数阈值
            
        Returns:
            int: 清理的用户数量
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            users_to_remove = []
            
            for account in self.accounts:
                if account.last_login:
                    last_login = datetime.fromisoformat(account.last_login)
                    if last_login < cutoff_date and not account.is_active:
                        users_to_remove.append(account)
            
            for user in users_to_remove:
                self.accounts.remove(user)
            
            if users_to_remove:
                self.save_accounts()
                # print(f"已清理 {len(users_to_remove)} 个不活跃用户")  // 调试日志已注释
            
            return len(users_to_remove)
            
        except Exception as e:
            # print(f"清理不活跃用户失败: {e}")  // 调试日志已注释
            return 0

    def sync_user_to_cloud(self, username):
        """将用户同步到云端（占位方法）
        
        Args:
            username (str): 要同步的用户名
            
        Returns:
            bool: 同步成功返回True，失败返回False
        """
        try:
            user = self.get_account_by_username(username)
            if not user:
                # print(f"未找到用户: {username}")  # 调试日志已注释
                return False
            
            # 这里应该实现实际的云端同步逻辑
            # 目前只是更新同步状态
            user.sync_status = 'synced'
            user.last_sync = datetime.now().isoformat()
            
            self.save_accounts()
            # print(f"用户 {username} 已同步到云端")  # 调试日志已注释
            return True
            
        except Exception as e:
            # print(f"同步用户到云端失败: {e}")  # 调试日志已注释
            return False

    def add_user_to_sync_queue(self, username, user_data=None):
        """将用户添加到同步队列
        
        Args:
            username (str): 用户名
            user_data (dict, optional): 用户数据
            
        Returns:
            bool: 添加成功返回True，失败返回False
        """
        try:
            account = self.get_account_by_username(username)
            if not account:
                # print(f"未找到用户: {username}")  // 调试日志已注释
                return False
            
            # 更新用户数据
            if user_data:
                for key, value in user_data.items():
                    if hasattr(account, key):
                        setattr(account, key, value)
            
            # 设置同步状态
            account.sync_status = 'pending'
            account.updated_at = datetime.now().isoformat()
            
            # 如果提供了email，保存到account对象
            if user_data and 'email' in user_data:
                account.email = user_data.get('email')
            
            # 保存更改
            self.save_accounts()
            
            # print(f"用户{username}已添加到同步队列")  // 调试日志已注释
            return True
            
        except Exception as e:
            # print(f"添加用户到同步队列失败: {e}")  // 调试日志已注释
            return False


# 单例模式
_user_manager_instance = None

def get_user_manager():
    """获取用户管理器实例（单例模式）"""
    global _user_manager_instance
    if _user_manager_instance is None:
        _user_manager_instance = UserManager()
    return _user_manager_instance

# 兼容性函数
def get_current_user_id():
    """获取当前用户ID（兼容性函数）"""
    manager = get_user_manager()
    return manager.get_current_user_id()

# 测试代码
if __name__ == "__main__":
    # 获取用户管理器
    manager = get_user_manager()
    
    # 获取所有用户
    accounts = manager.get_all_accounts()
    for account in accounts:
        print(f"用户ID: {account.user_id}, 用户名: {account.username}, 角色: {account.role}, custom_id: {account.custom_id if hasattr(account, 'custom_id') else 'None'}")
    
    # 获取当前用户
    current = manager.get_current_user()
    if current:
        print(f"当前用户: {current.username} ({current.role}), custom_id: {current.custom_id if hasattr(current, 'custom_id') else 'None'}")