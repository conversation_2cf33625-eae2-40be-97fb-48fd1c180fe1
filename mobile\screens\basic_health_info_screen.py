"""基本健康信息屏幕模块

提供用户全面健康信息的录入、编辑和管理功能，包括：
- 基本信息：身高、体重、BMI等
- 生活方式：饮食、运动、睡眠、烟酒习惯（多选形式）
- 医疗历史：慢性病史、手术史、住院史、传染病史（列表管理）
- 家族遗传病史：直系亲属重大疾病史（列表管理）
- 过敏记录：药物/食物/环境过敏源（列表管理）
- 预防接种：疫苗接种记录（列表管理）
"""

import typing
from kivy.logger import Logger as KivyLogger
from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty, ListProperty, DictProperty
from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
import os
import json
import logging
from datetime import datetime
from utils.database import get_db_manager

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogContentContainer, MDDialogButtonContainer
from kivymd.uix.divider import MDDivider
from kivy.uix.widget import Widget
from kivymd.uix.textfield import MDTextField, MDTextFieldHintText, MDTextFieldHelperText
from kivymd.uix.selectioncontrol import MDCheckbox
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.list import MDList, MDListItem
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogContentContainer, MDDialogButtonContainer
from kivymd.uix.divider import MDDivider
from widgets.logo import HealthLogo
from widgets.common_kv_components import load_common_kv_components

# 导入主题和字体样式
try:
    from mobile.theme import AppTheme, AppMetrics, FontStyles
except ImportError:
    # 如果无法从mobile包导入，则尝试直接导入
    try:
        from theme import AppTheme, AppMetrics, FontStyles
    except ImportError:
        # 如果两个都失败，抛出异常而不是创建模拟类
        raise ImportError("无法导入主题配置，请检查theme.py文件是否存在且可访问")

# 设置日志
logger = logging.getLogger(__name__)

# 定义KV语言字符串
KV = '''
<HealthCategoryCard>:
    # 分类标题栏
    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(40)
        spacing: dp(12)

        MDIcon:
            icon: root.icon
            theme_icon_color: "Custom"
            icon_color: root.icon_color
            font_size: dp(24)
            size_hint_x: None
            width: dp(24)
            pos_hint: {"center_y": 0.5}

        MDLabel:
            text: root.title
            font_style: "Body"
            role: "large"
            bold: True
            theme_text_color: "Primary"
            pos_hint: {"center_y": 0.5}

        Widget:
            size_hint_x: 0.1

        MDIconButton:
            icon: "chevron-down" if root.expanded else "chevron-right"
            font_size: dp(20)
            theme_icon_color: "Custom"
            icon_color: app.theme.TEXT_SECONDARY
            size_hint_x: None
            width: dp(40)
            on_release: root.toggle_expand()

    # 分隔线
    MDDivider:
        height: dp(1)
        color: app.theme.DIVIDER_COLOR

    # 内容区域
    MDBoxLayout:
        id: content_layout
        orientation: 'vertical'
        size_hint_y: None
        height: self.minimum_height if root.expanded else 0
        opacity: 1 if root.expanded else 0
        spacing: dp(8)

<MultiSelectItem>:
    orientation: 'horizontal'
    size_hint_y: None
    height: dp(48)
    spacing: dp(12)
    padding: [dp(8), dp(4), dp(8), dp(4)]

    MDCheckbox:
        id: checkbox
        size_hint_x: None
        width: dp(32)
        active: root.selected
        on_active: root.on_checkbox_active(self.active)

    MDLabel:
        text: root.text
        font_style: "Body"
        role: "medium"
        theme_text_color: "Primary"
        pos_hint: {"center_y": 0.5}

<ListManagementItem>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    md_bg_color: app.theme.SURFACE_COLOR
    radius: [dp(8)]
    padding: [dp(12), dp(8), dp(12), dp(8)]
    spacing: dp(8)

    MDBoxLayout:
        orientation: 'horizontal'
        size_hint_y: None
        height: dp(32)
        spacing: dp(8)

        MDLabel:
            text: root.title
            font_style: "Body"
            role: "medium"
            bold: True
            theme_text_color: "Primary"
            size_hint_x: 0.8
            pos_hint: {"center_y": 0.5}

        MDIconButton:
            icon: "pencil"
            font_size: dp(18)
            theme_icon_color: "Custom"
            icon_color: app.theme.PRIMARY_COLOR
            size_hint_x: None
            width: dp(32)
            on_release: root.on_edit()

        MDIconButton:
            icon: "delete"
            font_size: dp(18)
            theme_icon_color: "Custom"
            icon_color: app.theme.ERROR_COLOR
            size_hint_x: None
            width: dp(32)
            on_release: root.on_delete()

    MDLabel:
        text: root.content
        font_style: "Label"
        theme_text_color: "Secondary"
        size_hint_y: None
        height: self.texture_size[1]
        text_size: self.width, None

<HealthInfoItem>:
    orientation: 'horizontal'
    size_hint_y: None
    height: dp(48)
    spacing: dp(12)
    padding: [dp(8), dp(4), dp(8), dp(4)]

    MDLabel:
        text: root.label
        font_style: "Body"
        role: "medium"
        theme_text_color: "Primary"
        size_hint_x: 0.4
        pos_hint: {"center_y": 0.5}

    MDLabel:
        text: root.value if root.value else "未填写"
        font_style: "Body"
        role: "medium"
        theme_text_color: "Custom"
        text_color: app.theme.PRIMARY_COLOR if root.value else app.theme.TEXT_SECONDARY
        size_hint_x: 0.5
        pos_hint: {"center_y": 0.5}

    MDIconButton:
        icon: "pencil"
        font_size: dp(18)
        theme_icon_color: "Custom"
        icon_color: app.theme.PRIMARY_COLOR
        size_hint_x: None
        width: dp(40)
        on_release: root.on_edit()

<BasicHealthInfoScreen>:
    md_bg_color: app.theme.BACKGROUND_COLOR

    MDBoxLayout:
        orientation: "vertical"
        spacing: dp(8)

        # 顶部栏
        MDBoxLayout:
            id: top_bar
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(56)
            md_bg_color: app.theme.PRIMARY_COLOR
            padding: [dp(8), dp(0), dp(8), dp(0)]

            MDIconButton:
                icon: "arrow-left"
                font_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                pos_hint: {"center_y": 0.5}
                on_release: root.go_back()

            MDLabel:
                text: "基本健康信息"
                font_style: "Body"
                role: "large"
                bold: True
                theme_text_color: "Custom"
                text_color: app.theme.TEXT_LIGHT
                size_hint_x: 0.7
                pos_hint: {"center_y": 0.5}
                halign: "center"

            MDIconButton:
                icon: "content-save"
                font_size: dp(24)
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_LIGHT
                pos_hint: {"center_y": 0.5}
                on_release: root.save_all_data()

        # Logo已由BaseScreen统一管理，无需在KV中定义

        # 主内容区
        MDScrollView:
            id: scroll_view
            do_scroll_x: False
            do_scroll_y: True

            MDBoxLayout:
                id: main_layout
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(16), dp(8), dp(16), dp(16)]
                spacing: dp(16)
'''

# 只加载一次KV，确保ids绑定唯一
Builder.load_string(KV)

class MultiSelectItem(MDBoxLayout):
    """多选项组件"""
    text = StringProperty("")
    selected = BooleanProperty(False)
    callback = ObjectProperty(None)

    def on_checkbox_active(self, active):
        """复选框状态改变"""
        self.selected = active
        if self.callback:
            self.callback(self.text, active)

class ListManagementItem(MDCard):
    """列表管理项组件"""
    title = StringProperty("")
    content = StringProperty("")
    item_data = DictProperty({})
    edit_callback = ObjectProperty(None)
    delete_callback = ObjectProperty(None)

    def on_edit(self):
        """编辑项目"""
        if self.edit_callback:
            self.edit_callback(self.item_data)

    def on_delete(self):
        """删除项目"""
        if self.delete_callback:
            self.delete_callback(self.item_data)

class HealthCategoryCard(MDCard):
    """健康信息分类卡片组件"""
    title = StringProperty("")
    icon = StringProperty("")
    icon_color = ListProperty([1, 1, 1, 1])
    expanded = BooleanProperty(True)
    category_data = DictProperty({})
    screen_ref = ObjectProperty(None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.load_items()

    def toggle_expand(self):
        """切换展开/收起状态"""
        self.expanded = not self.expanded

    def load_items(self):
        """加载分类下的信息项"""
        try:
            content_layout = self.ids.content_layout
            content_layout.clear_widgets()

            # 检查category_data是否为字典类型
            if not isinstance(self.category_data, dict):
                logger.warning(f"category_data不是字典类型: {type(self.category_data)} - {self.category_data}")
                return

            if not self.category_data or 'items' not in self.category_data:
                return

            category_key = self.category_data.get('key', '')

            # 根据不同类型加载不同的UI
            if category_key == 'lifestyle':
                self.load_lifestyle_items(content_layout)
            elif category_key in ['medical_history', 'family_history', 'allergies', 'vaccinations']:
                self.load_list_management_items(content_layout)
            else:
                self.load_normal_items(content_layout)
                
        except Exception as e:
            logger.error(f"加载分类项时出错: {e}")
            # 添加错误提示
            try:
                content_layout = self.ids.content_layout
                content_layout.clear_widgets()
                error_label = MDLabel(
                    text=f"加载数据时出错: {str(e)}",
                    theme_text_color="Error",
                    size_hint_y=None,
                    height=dp(40)
                )
                content_layout.add_widget(error_label)
            except Exception as inner_e:
                logger.error(f"添加错误提示时出错: {inner_e}")

    def load_normal_items(self, content_layout):
        """加载普通信息项"""
        try:
            items = self.category_data.get('items', {})
            
            # 确保items是字典类型
            if not isinstance(items, dict):
                logger.warning(f"items不是字典类型: {type(items)} - {items}")
                return
                
            for item_key, item_data in items.items():
                # 确保item_data是字典类型
                if not isinstance(item_data, dict):
                    logger.warning(f"跳过非字典类型的项目数据: {item_key} - {type(item_data)}")
                    continue
                    
                item = HealthInfoItem(
                    label=item_data.get('label', ''),
                    value=item_data.get('value', ''),
                    item_key=item_key,
                    category_key=self.category_data.get('key', ''),
                    item_data=item_data
                )
                content_layout.add_widget(item)
                
        except Exception as e:
            logger.error(f"加载普通信息项时出错: {e}")
            # 添加错误提示
            from kivymd.uix.label import MDLabel
            error_label = MDLabel(
                text=f"加载数据时出错: {str(e)}",
                theme_text_color="Custom",
                text_color=[1, 0, 0, 1],
                size_hint_y=None,
                height="48dp"
            )
            content_layout.add_widget(error_label)

    def load_lifestyle_items(self, content_layout):
        """加载生活方式多选项"""
        lifestyle_sections = {
            'diet': {
                'title': '饮食习惯（可多选）',
                'options': ['肉食为主', '素食为主', '荤素均衡', '喜腌制食品', '喜食粥粉面等易消化食物', '喜热粥热茶', '喜食辛辣', '高盐饮食']
            },
            'exercise': {
                'title': '运动情况',
                'fields': {
                    'exercise_type': {'label': '运动方式', 'options': ['跑步', '游泳', '健身', '瑜伽', '太极', '广场舞', '散步', '骑行', '球类运动', '其他']},
                    'exercise_amount': {'label': '运动量', 'options': ['轻度', '中度', '重度']},
                    'exercise_frequency': {'label': '运动频率', 'options': ['每天', '每周5-6次', '每周3-4次', '每周1-2次', '偶尔', '从不']}
                }
            },
            'sleep': {
                'title': '睡眠情况（可多选）',
                'options': ['主观感觉良好', '主观感觉一般', '主观感觉较差', '入眠困难', '易醒', '早醒', '多梦', '打鼾']
            },
            'smoking': {
                'title': '吸烟情况',
                'fields': {
                    'smoking_years': {'label': '吸烟时长（年）', 'type': 'number'},
                    'smoking_amount': {'label': '吸烟量（包/天）', 'type': 'number'},
                    'quit_smoking': {'label': '有无戒烟', 'options': ['从未吸烟', '已戒烟', '正在戒烟', '未戒烟']},
                    'quit_time': {'label': '戒烟时间', 'type': 'text'}
                }
            },
            'drinking': {
                'title': '饮酒情况',
                'fields': {
                    'drinking_amount': {'label': '饮酒量', 'options': ['从不饮酒', '少量（<25g/天）', '中量（25-50g/天）', '大量（>50g/天）']},
                    'drinking_frequency': {'label': '饮酒频率', 'options': ['从不', '偶尔', '每周1-2次', '每周3-4次', '每天']},
                    'quit_drinking': {'label': '戒酒情况', 'options': ['从未饮酒', '已戒酒', '正在戒酒', '未戒酒']}
                }
            },
            'bowel': {
                'title': '大便情况（可多选）',
                'options': ['正常', '便秘', '腹泻', '便血', '大便不成形', '排便困难', '排便疼痛']
            },
            'urination': {
                'title': '小便情况（可多选）',
                'options': ['正常', '尿频', '尿急', '尿痛', '排尿困难', '血尿', '夜尿增多（>2次/夜）', '尿失禁']
            },
            'work_stress': {
                'title': '工作压力',
                'options': ['很低', '较低', '中等', '较高', '很高']
            }
        }

        for section_key, section_data in lifestyle_sections.items():
            # 添加小节标题
            title_label = MDLabel(
                text=section_data['title'],
                font_style="Body",
                role="medium",
                bold=True,
                theme_text_color="Primary",
                size_hint_y=None,
                height=dp(32)
            )
            content_layout.add_widget(title_label)

            if 'options' in section_data:
                # 多选项
                for option in section_data['options']:
                    selected = option in self.get_lifestyle_selections(section_key)
                    item = MultiSelectItem(
                        text=option,
                        selected=selected,
                        callback=lambda text, active, sk=section_key: self.on_lifestyle_selection(sk, text, active)
                    )
                    content_layout.add_widget(item)
            elif 'fields' in section_data:
                # 字段项
                for field_key, field_data in section_data['fields'].items():
                    current_value = self.get_lifestyle_field_value(section_key, field_key)
                    item_data = {'type': 'select' if 'options' in field_data else field_data.get('type', 'text')}
                    if 'options' in field_data:
                        item_data['options'] = field_data['options']
                    item = HealthInfoItem(
                        label=field_data['label'],
                        value=current_value,
                        item_key=f"{section_key}_{field_key}",
                        category_key='lifestyle',
                        item_data=item_data
                    )
                    content_layout.add_widget(item)

            # 添加分隔线
            content_layout.add_widget(MDBoxLayout(size_hint_y=None, height=dp(8)))

    def load_list_management_items(self, content_layout):
        """加载列表管理项"""
        try:
            category_key = self.category_data.get('key', '')
            items = self.get_list_items(category_key)

            # 添加新增按钮
            add_button = MDButton(
                MDButtonText(text=f"添加{self.category_data['title']}"),
                style="outlined",
                size_hint_y=None,
                height=dp(40),
                on_release=lambda x: self.add_list_item(category_key)
            )
            content_layout.add_widget(add_button)

            # 添加现有项目
            for item_data in items:
                # 确保item_data是字典类型
                if not isinstance(item_data, dict):
                    logger.warning(f"跳过非字典类型的项目数据: {type(item_data)} - {item_data}")
                    continue
                    
                list_item = ListManagementItem(
                    title=item_data.get('title', ''),
                    content=item_data.get('content', ''),
                    item_data=item_data,
                    edit_callback=lambda data, ck=category_key: self.edit_list_item(ck, data),
                    delete_callback=lambda data, ck=category_key: self.delete_list_item(ck, data)
                )
                content_layout.add_widget(list_item)
                
        except Exception as e:
            logger.error(f"加载列表管理项时出错: {e}")
            # 添加错误提示
            error_label = MDLabel(
                text=f"加载数据时出错: {str(e)}",
                theme_text_color="Error",
                size_hint_y=None,
                height=dp(40)
            )
            content_layout.add_widget(error_label)

    def get_lifestyle_selections(self, section_key):
        """获取生活方式选择"""
        if not self.screen_ref:
            return []
        return self.screen_ref.lifestyle_selections.get(section_key, [])

    def get_lifestyle_field_value(self, section_key, field_key):
        """获取生活方式字段值"""
        if not self.screen_ref:
            return ''
        return self.screen_ref.lifestyle_fields.get(f"{section_key}_{field_key}", '')

    def on_lifestyle_selection(self, section_key, text, active):
        """生活方式选择改变"""
        if not self.screen_ref:
            return
        self.screen_ref.update_lifestyle_selection(section_key, text, active)

    def get_list_items(self, category_key):
        """获取列表项"""
        if not self.screen_ref:
            return []
        return self.screen_ref.list_data.get(category_key, [])

    def add_list_item(self, category_key):
        """添加列表项"""
        if self.screen_ref:
            self.screen_ref.add_list_item(category_key)

    def edit_list_item(self, category_key, item_data):
        """编辑列表项"""
        if self.screen_ref:
            self.screen_ref.edit_list_item(category_key, item_data)

    def delete_list_item(self, category_key, item_data):
        """删除列表项"""
        if self.screen_ref:
            self.screen_ref.delete_list_item(category_key, item_data)

class HealthInfoItem(MDBoxLayout):
    """健康信息项组件"""
    label = StringProperty("")
    value = StringProperty("")
    item_key = StringProperty("")
    category_key = StringProperty("")
    item_data = DictProperty({})

    def on_edit(self):
        """编辑信息项"""
        try:
            root_window = self.get_root_window()
            if root_window and hasattr(root_window, 'children') and root_window.children:
                screen = root_window.children[0].current_screen
                if hasattr(screen, 'edit_health_item'):
                    screen.edit_health_item(self.category_key, self.item_key, self.label, self.value, self.item_data)
        except Exception as e:
            logger.error(f"编辑信息项时出错: {e}")

class BasicHealthInfoScreen(BaseScreen):
    """基本健康信息屏幕"""

    def __init__(self, **kwargs):
        # 设置导航栏属性
        kwargs['screen_title'] = '基本健康信息'
        kwargs['show_top_bar'] = True
        kwargs['top_bar_action_icon'] = 'content-save'
        super().__init__(**kwargs)
        # 加载通用KV组件
        load_common_kv_components()
        self.app = MDApp.get_running_app()
        self.health_data = {}
        self.lifestyle_selections = {}  # 生活方式多选数据
        self.lifestyle_fields = {}      # 生活方式字段数据
        self.list_data = {}             # 列表管理数据
        self.init_health_data_structure()

    def init_ui(self, dt=0):
        """初始化UI"""
        # 首先调用父类的init_ui方法，确保BaseScreen的Logo设置被执行
        super().init_ui(dt)
        
        self.load_data()
        self.refresh_ui()
        return True

    def init_health_data_structure(self):
        """初始化健康数据结构"""
        self.health_data = {
            'basic_info': {
                'key': 'basic_info',
                'title': '基本信息',
                'icon': 'account-circle',
                'color': [0.2, 0.6, 1.0, 1.0],  # 蓝色
                'items': {
                    'height': {'label': '身高', 'value': '', 'unit': 'cm', 'type': 'number'},
                    'weight': {'label': '体重', 'value': '', 'unit': 'kg', 'type': 'number'},
                    'bmi': {'label': 'BMI指数', 'value': '', 'unit': '', 'type': 'calculated'},
                    'blood_type': {'label': '血型', 'value': '', 'unit': '', 'type': 'select', 'options': ['A型', 'B型', 'AB型', 'O型']},
                    'rh_factor': {'label': 'RH因子', 'value': '', 'unit': '', 'type': 'select', 'options': ['阳性', '阴性']}
                }
            },
            'lifestyle': {
                'key': 'lifestyle',
                'title': '生活方式',
                'icon': 'heart-pulse',
                'color': [1.0, 0.4, 0.4, 1.0],  # 红色
                'items': {}  # 生活方式使用特殊处理
            },
            'medical_history': {
                'key': 'medical_history',
                'title': '医疗史',
                'icon': 'medical-bag',
                'color': [0.4, 0.8, 0.4, 1.0],  # 绿色
                'items': {}  # 使用列表管理
            },
            'family_history': {
                'key': 'family_history',
                'title': '家族遗传史',
                'icon': 'account-group',
                'color': [0.8, 0.4, 0.8, 1.0],  # 紫色
                'items': {}  # 使用列表管理
            },
            'allergies': {
                'key': 'allergies',
                'title': '过敏记录',
                'icon': 'alert-circle',
                'color': [1.0, 0.6, 0.2, 1.0],  # 橙色
                'items': {}  # 使用列表管理
            },
            'vaccinations': {
                'key': 'vaccinations',
                'title': '预防接种记录',
                'icon': 'needle',
                'color': [0.6, 0.8, 1.0, 1.0],  # 浅蓝色
                'items': {}  # 使用列表管理
            }
        }

        # 初始化列表数据结构
        self.list_data = {
            'medical_history': [],
            'family_history': [],
            'allergies': [],
            'vaccinations': []
        }

        # 初始化生活方式数据
        self.lifestyle_selections = {
            'diet': [],
            'sleep': [],
            'bowel': [],
            'urination': [],
            'work_stress': []
        }

        self.lifestyle_fields = {}

    def on_enter(self, *args):
        """进入屏幕时的处理"""
        try:
            # 初始化UI
            self.init_ui()
        except Exception as e:
            logger.error(f"进入基本健康信息屏幕时出错: {e}")

    def go_back(self):
        """返回上一页"""
        try:
            app = MDApp.get_running_app()
            if app and hasattr(app, 'root') and app.root:
                app.root.transition.direction = 'right'
                app.root.current = 'health_data_management_screen'
        except Exception as e:
            logger.error(f"返回上一页时出错: {e}")

    def save_all_data(self):
        """保存所有健康数据"""
        try:
            # 计算BMI
            self.calculate_bmi()

            # 保存数据到JSON文件
            all_data = {
                'health_data': self.health_data,
                'lifestyle_selections': self.lifestyle_selections,
                'lifestyle_fields': self.lifestyle_fields,
                'list_data': self.list_data
            }
            with open('health_data.json', 'w', encoding='utf-8') as f:
                json.dump(all_data, f, ensure_ascii=False, indent=4)

            # 统计保存的数据项
            saved_count = sum(1 for cat in self.health_data.values() for item in cat.get('items', {}).values() if item.get('value'))
            saved_count += sum(len(selections) for selections in self.lifestyle_selections.values())
            saved_count += sum(1 for value in self.lifestyle_fields.values() if value)
            saved_count += sum(len(items) for items in self.list_data.values())

            self.show_message(f"已保存 {saved_count} 项健康信息" if saved_count > 0 else "请填写健康信息后再保存")

        except Exception as e:
            logger.error(f"保存健康数据时出错: {e}")
            self.show_message("保存失败")

    def calculate_bmi(self):
        """计算BMI指数"""
        try:
            height_str = self.health_data['basic_info']['items']['height']['value']
            weight_str = self.health_data['basic_info']['items']['weight']['value']

            if height_str and weight_str:
                height = float(height_str) / 100  # 转换为米
                weight = float(weight_str)
                bmi = weight / (height * height)
                bmi_status = "偏瘦" if bmi < 18.5 else "正常" if bmi < 24 else "超重" if bmi < 28 else "肥胖"
                self.health_data['basic_info']['items']['bmi']['value'] = f"{bmi:.1f} ({bmi_status})"

        except ValueError as e:
            logger.debug(f"BMI计算失败: {e}")

    def edit_health_item(self, category_key, item_key, label, value, item_data):
        """编辑健康信息项"""
        try:
            self.show_item_dialog(category_key, item_key, label, value, item_data)
        except Exception as e:
            logger.error(f"编辑健康信息项时出错: {e}")

    def show_item_dialog(self, category_key, item_key, label, value, item_data):
        """显示项目编辑对话框"""
        try:
            # 创建内容容器
            content_container = MDBoxLayout(
                orientation="vertical",
                spacing=dp(12),
                size_hint_y=None,
                height=dp(200),
                adaptive_height=True
            )

            # 根据数据类型创建不同的输入控件
            input_type = item_data.get('type')
            if input_type == 'select' and 'options' in item_data:
                # 下拉选择 (简化，使用文本字段)
                self.input_field = MDTextField(
                    MDTextFieldHintText(text=f"选择{label}"),
                    MDTextFieldHelperText(text=f"选项: {', '.join(item_data['options'])}"),
                    text=value,
                    size_hint_y=None,
                    height=dp(56)
                )
            elif input_type == 'number':
                # 数字输入
                self.input_field = MDTextField(
                    MDTextFieldHintText(text=f"输入{label}"),
                    text=value,
                    input_filter='float',
                    size_hint_y=None,
                    height=dp(56)
                )
            else:
                # 普通文本
                self.input_field = MDTextField(
                    MDTextFieldHintText(text=f"输入{label}"),
                    text=value,
                    size_hint_y=None,
                    height=dp(56)
                )

            content_container.add_widget(self.input_field)

            if item_data.get('unit'):
                content_container.add_widget(MDLabel(
                    text=f"单位: {item_data['unit']}", 
                    size_hint_y=None, 
                    height=dp(24),
                    theme_text_color="Secondary"
                ))

            # 创建符合KivyMD 2.0.1规范的对话框
            self.dialog = MDDialog(
                # 对话框属性设置
                size_hint=(0.9, None),
                height=dp(320),
                auto_dismiss=False,
                radius=[dp(16), dp(16), dp(16), dp(16)]
            )
            
            # 添加标题
            self.dialog.add_widget(
                MDDialogHeadlineText(
                    text=f"编辑{label}",
                    halign="center"
                )
            )
            
            # 添加内容容器
            self.dialog.add_widget(
                MDDialogContentContainer(
                    content_container,
                    orientation="vertical",
                    spacing=dp(8)
                )
            )
            
            # 添加按钮容器
            self.dialog.add_widget(
                MDDialogButtonContainer(
                    Widget(),  # 占位符，用于右对齐按钮
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: self.dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="保存"),
                        style="text",
                        on_release=lambda x: self.save_item(category_key, item_key)
                    ),
                    spacing=dp(8)
                )
            )

            self.dialog.open()

        except Exception as e:
            logger.error(f"显示项目对话框时出错: {e}")
            self.show_message("显示对话框失败")

    def save_item(self, category_key, item_key):
        """保存项目数据"""
        try:
            value = self.input_field.text.strip()

            if category_key == 'lifestyle':
                self.lifestyle_fields[item_key] = value
            else:
                self.health_data[category_key]['items'][item_key]['value'] = value

            # 如果是身高或体重，重新计算BMI
            if category_key == 'basic_info' and item_key in ['height', 'weight']:
                self.calculate_bmi()

            # 刷新UI
            self.refresh_ui()

            # 关闭对话框
            self.dialog.dismiss()

            # 显示成功消息
            self.show_message("保存成功")

        except Exception as e:
            logger.error(f"保存项目数据时出错: {e}")
            self.show_message("保存失败")

    def update_lifestyle_selection(self, section_key, text, active):
        """更新生活方式选择"""
        if section_key not in self.lifestyle_selections:
            self.lifestyle_selections[section_key] = []

        if active:
            if text not in self.lifestyle_selections[section_key]:
                self.lifestyle_selections[section_key].append(text)
        else:
            if text in self.lifestyle_selections[section_key]:
                self.lifestyle_selections[section_key].remove(text)

    def add_list_item(self, category_key):
        """添加列表项"""
        try:
            self.show_list_dialog(category_key)
        except Exception as e:
            logger.error(f"添加列表项时出错: {e}")

    def edit_list_item(self, category_key, item_data):
        """编辑列表项"""
        try:
            self.show_list_dialog(category_key, item_data)
        except Exception as e:
            logger.error(f"编辑列表项时出错: {e}")

    def show_list_dialog(self, category_key, existing_data=None):
        """显示列表编辑对话框"""
        try:
            fields_config = self.get_list_fields_config(category_key)
            title = fields_config['title']
            fields = fields_config['fields']

            content_container = MDDialogContentContainer(
                MDDialogHeadlineText(text=title),
                MDDivider(),
                orientation="vertical",
                spacing=dp(12),
                size_hint_y=None,
                height=dp(100 + len(fields) * 60),
            )

            self.dialog_fields = {}
            for field_key, field_info in fields.items():
                field = MDTextField(
                    MDTextFieldHintText(text=field_info['label']),
                    text=existing_data.get(field_key, '') if existing_data else '',
                    multiline=field_info.get('multiline', False),
                    size_hint_y=None,
                    height=dp(80) if field_info.get('multiline') else dp(56)
                )
                content_container.add_widget(field)
                self.dialog_fields[field_key] = field

            # 创建对话框 - 符合KivyMD 2.0.1规范
            self.dialog = MDDialog(
                # 对话框属性设置
                size_hint=(0.9, None),
                height=dp(520),  # 增加标题和按钮高度
                auto_dismiss=False,
                radius=[dp(16), dp(16), dp(16), dp(16)]
            )
            
            # 添加标题
            self.dialog.add_widget(
                MDDialogHeadlineText(
                    text=fields_config['title'],
                    halign="center"
                )
            )
            
            # 添加内容容器
            self.dialog.add_widget(
                MDDialogContentContainer(
                    MDScrollView(
                        content_container,
                        size_hint_y=None,
                        height=dp(400),
                        do_scroll_x=False,
                        do_scroll_y=True,
                        bar_width=dp(4),
                        scroll_type=['bars', 'content']
                    ),
                    orientation="vertical",
                    spacing=dp(8)
                )
            )
            
            # 添加按钮容器
            self.dialog.add_widget(
                MDDialogButtonContainer(
                    Widget(),  # 占位符，用于右对齐按钮
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: self.dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="保存"),
                        style="text",
                        on_release=lambda x: self.save_list_item(category_key, existing_data, fields_config['constructors'])
                    ),
                    spacing=dp(8)
                )
            )

            self.dialog.open()

        except Exception as e:
            logger.error(f"显示列表对话框时出错: {e}")
            self.show_message("显示对话框失败")

    def get_list_fields_config(self, category_key):
        """获取列表字段配置"""
        configs = {
            'medical_history': {
                'title': '医疗史记录',
                'fields': {
                    'type': {'label': '选择类型'},
                    'disease_name': {'label': '疾病名称'},
                    'control_status': {'label': '控制情况'},
                    'duration': {'label': '病程时长'}
                },
                'constructors': {
                    'title': lambda data: data['type'],
                    'content': lambda data: f"疾病: {data['disease_name']}, 控制情况: {data['control_status']}"
                }
            },
            'family_history': {
                'title': '家族遗传史记录',
                'fields': {
                    'relationship': {'label': '直系亲属关系（父亲/母亲/兄弟/姐妹/祖父/祖母/外祖父/外祖母）'},
                    'disease_type': {'label': '疾病种类'},
                    'outcome': {'label': '转归情况', 'multiline': True}
                },
                'constructors': {
                    'title': lambda data: f"{data['relationship']} - {data['disease_type']}",
                    'content': lambda data: f"关系: {data['relationship']}, 疾病: {data['disease_type']}, 转归: {data['outcome']}"
                }
            },
            'allergies': {
                'title': '过敏记录',
                'fields': {
                    'allergy_type': {'label': '过敏种类（药物/食物/环境等）'},
                    'symptoms': {'label': '过敏症状', 'multiline': True}
                },
                'constructors': {
                    'title': lambda data: data['allergy_type'],
                    'content': lambda data: f"过敏种类: {data['allergy_type']}, 症状: {data['symptoms']}"
                }
            },
            'vaccinations': {
                'title': '预防接种记录',
                'fields': {
                    'vaccine_type': {'label': '接种疫苗种类'},
                    'date': {'label': '接种时间（YYYY-MM-DD）'},
                    'location': {'label': '接种地点'}
                },
                'constructors': {
                    'title': lambda data: f"{data['vaccine_type']} ({data['date']})",
                    'content': lambda data: f"疫苗: {data['vaccine_type']}, 时间: {data['date']}, 地点: {data['location']}"
                }
            }
        }
        return configs.get(category_key, {})

    def save_list_item(self, category_key, existing_data, constructors):
        """保存列表项目"""
        try:
            item_data = {'id': existing_data.get('id') if existing_data else len(self.list_data[category_key])}
            for field_key, field in self.dialog_fields.items():
                item_data[field_key] = field.text.strip()
            item_data['title'] = constructors['title'](item_data)
            item_data['content'] = constructors['content'](item_data)

            if existing_data:
                # 更新现有项目
                for i, item in enumerate(self.list_data[category_key]):
                    if item['id'] == existing_data['id']:
                        self.list_data[category_key][i] = item_data
                        break
            else:
                # 添加新项目
                self.list_data[category_key].append(item_data)

            # 保存到数据库
            self.save_list_item_to_db(category_key, item_data)
            
            self.dialog.dismiss()
            self.refresh_ui()
            self.show_message("保存成功")

        except Exception as e:
            logger.error(f"保存列表项目时出错: {e}")
            self.show_message("保存失败")

    def delete_list_item(self, category_key, item_data):
        """删除列表项"""
        try:
            self.list_data[category_key] = [item for item in self.list_data[category_key] if item['id'] != item_data['id']]
            # 从数据库删除
            self.delete_list_item_from_db(category_key, item_data)
            self.refresh_ui()
            self.show_message("删除成功")
        except Exception as e:
            logger.error(f"删除列表项时出错: {e}")
    
    def save_list_item_to_db(self, category_key, item_data):
        """保存列表项到数据库"""
        try:
            db_manager = get_db_manager()
            if not db_manager:
                logger.error("无法获取数据库管理器")
                return
            
            # 获取用户ID
            user_data = getattr(self.manager.get_screen('main'), 'user_data', {})
            custom_id = user_data.get('custom_id', 'default_user')
            
            # 将item_data转换为JSON字符串
            item_json = json.dumps(item_data, ensure_ascii=False)
            current_time = datetime.now().isoformat()
            
            # 检查是否已存在
            existing_query = """
                SELECT id FROM basic_health_info_list 
                WHERE custom_id = ? AND category = ? AND json_extract(item_data, '$.id') = ?
            """
            existing_results = db_manager.execute_query(existing_query, (custom_id, category_key, item_data['id']))
            existing = existing_results[0] if existing_results else None
            
            if existing:
                # 更新现有记录
                update_query = """
                    UPDATE basic_health_info_list 
                    SET item_data = ?, updated_at = ?
                    WHERE id = ?
                """
                db_manager.execute_query(update_query, (item_json, current_time, existing[0]))
            else:
                # 插入新记录
                insert_query = """
                    INSERT INTO basic_health_info_list (custom_id, category, item_data, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                """
                db_manager.execute_query(insert_query, (custom_id, category_key, item_json, current_time, current_time))
            
            logger.info(f"成功保存列表项到数据库: {category_key}")
            
        except Exception as e:
            logger.error(f"保存列表项到数据库时出错: {e}")
    
    def delete_list_item_from_db(self, category_key, item_data):
        """从数据库删除列表项"""
        try:
            db_manager = get_db_manager()
            if not db_manager:
                logger.error("无法获取数据库管理器")
                return
            
            # 获取用户ID
            user_data = getattr(self.manager.get_screen('main'), 'user_data', {})
            custom_id = user_data.get('custom_id', 'default_user')
            
            # 删除记录
            delete_query = """
                DELETE FROM basic_health_info_list 
                WHERE custom_id = ? AND category = ? AND json_extract(item_data, '$.id') = ?
            """
            db_manager.execute_query(delete_query, (custom_id, category_key, item_data['id']))
            
            logger.info(f"成功从数据库删除列表项: {category_key}")
            
        except Exception as e:
            logger.error(f"从数据库删除列表项时出错: {e}")
    
    def save_health_item_to_db(self, category_key, item_key, item_value, item_type='text'):
        """保存健康数据项到数据库"""
        try:
            db_manager = get_db_manager()
            if not db_manager:
                logger.error("无法获取数据库管理器")
                return
            
            # 获取用户ID
            user_data = getattr(self.manager.get_screen('main'), 'user_data', {})
            custom_id = user_data.get('custom_id', 'default_user')
            
            current_time = datetime.now().isoformat()
            
            # 检查是否已存在
            existing_query = """
                SELECT id FROM basic_health_info 
                WHERE custom_id = ? AND category = ? AND item_key = ?
            """
            existing_results = db_manager.execute_query(existing_query, (custom_id, category_key, item_key))
            existing = existing_results[0] if existing_results else None
            
            if existing:
                # 更新现有记录
                update_query = """
                    UPDATE basic_health_info 
                    SET item_value = ?, item_type = ?, updated_at = ?
                    WHERE id = ?
                """
                db_manager.execute_query(update_query, (item_value, item_type, current_time, existing[0]))
            else:
                # 插入新记录
                insert_query = """
                    INSERT INTO basic_health_info (custom_id, category, item_key, item_value, item_type, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                db_manager.execute_query(insert_query, (custom_id, category_key, item_key, item_value, item_type, current_time, current_time))
            
            logger.info(f"成功保存健康数据项到数据库: {category_key}.{item_key}")
            
        except Exception as e:
            logger.error(f"保存健康数据项到数据库时出错: {e}")
    
    def load_data_from_db(self):
        """从数据库加载健康数据"""
        try:
            db_manager = get_db_manager()
            if not db_manager:
                logger.error("无法获取数据库管理器")
                return
            
            self._initialize_list_data()
            custom_id = self._get_user_custom_id()
            
            # 分别加载不同类型的数据
            self._load_basic_health_info(db_manager, custom_id)
            self._load_list_data(db_manager, custom_id)
            
            logger.info("成功从数据库加载健康数据")
            
        except Exception as e:
            logger.error(f"从数据库加载健康数据时出错: {e}")
    
    def _initialize_list_data(self):
        """初始化列表数据结构"""
        if not isinstance(self.list_data, dict):
            logger.warning(f"list_data类型错误，重新初始化: {type(self.list_data)}")
            self.list_data = {
                'medical_history': [],
                'family_history': [],
                'allergies': [],
                'vaccinations': []
            }
    
    def _get_user_custom_id(self):
        """获取用户自定义ID"""
        user_data = getattr(self.manager.get_screen('main'), 'user_data', {})
        return user_data.get('custom_id', 'default_user')
    
    def _load_basic_health_info(self, db_manager, custom_id):
        """加载基本健康信息"""
        health_query = """
            SELECT category, item_key, item_value, item_type 
            FROM basic_health_info 
            WHERE custom_id = ?
        """
        health_records = db_manager.fetch_all(health_query, (custom_id,))
        
        for record in health_records:
            try:
                self._process_health_record(record)
            except Exception as e:
                logger.error(f"处理健康数据记录时出错: {e} - 记录: {record}")
    
    def _process_health_record(self, record):
        """处理单个健康数据记录"""
        category, item_key, item_value, item_type = record
        
        # 验证数据类型
        if not isinstance(category, str) or not isinstance(item_key, str):
            logger.warning(f"跳过无效的记录类型: category={type(category)}, item_key={type(item_key)}")
            return
        
        # 根据类别处理数据
        if self._is_basic_health_category(category):
            self._update_basic_health_data(category, item_key, item_value)
        elif self._is_lifestyle_category(category):
            self._update_lifestyle_data(category, item_key, item_value)
        else:
            logger.debug(f"未处理的健康数据类别: {category}")
    
    def _is_basic_health_category(self, category):
        """检查是否为基本健康信息类别"""
        return (category in self.health_data and 
                isinstance(self.health_data[category], dict))
    
    def _is_lifestyle_category(self, category):
        """检查是否为生活方式类别"""
        return category in ['lifestyle_selections', 'lifestyle_fields']
    
    def _update_basic_health_data(self, category, item_key, item_value):
        """更新基本健康数据"""
        category_data = self.health_data[category]
        if ('items' in category_data and 
            isinstance(category_data['items'], dict) and
            item_key in category_data['items']):
            
            if isinstance(category_data['items'][item_key], dict):
                category_data['items'][item_key]['value'] = item_value
            else:
                logger.warning(f"跳过非字典类型的项目数据: {category}.{item_key}")
    
    def _update_lifestyle_data(self, category, item_key, item_value):
        """更新生活方式数据"""
        if category == 'lifestyle_selections':
            if item_key not in self.lifestyle_selections:
                self.lifestyle_selections[item_key] = []
            if item_value and item_value not in self.lifestyle_selections[item_key]:
                self.lifestyle_selections[item_key].append(item_value)
        else:
            self.lifestyle_fields[item_key] = item_value
    
    def _load_list_data(self, db_manager, custom_id):
        """加载列表数据"""
        list_query = """
            SELECT category, item_data 
            FROM basic_health_info_list 
            WHERE custom_id = ?
        """
        list_records = db_manager.fetch_all(list_query, (custom_id,))
        
        for record in list_records:
            try:
                self._process_list_record(record)
            except Exception as e:
                logger.error(f"处理列表数据时出错: {e}")
    
    def _process_list_record(self, record):
        """处理单个列表数据记录"""
        category, item_data_json = record
        
        # 验证类别
        if not isinstance(category, str) or not category:
            logger.warning(f"跳过无效的列表数据类别: {category}")
            return
        
        try:
            item_data = json.loads(item_data_json)
            self._ensure_list_category_exists(category)
            self._add_item_to_list_data(category, item_data)
        except json.JSONDecodeError as e:
            logger.error(f"解析列表数据JSON时出错: {e}")
    
    def _ensure_list_category_exists(self, category):
        """确保列表数据类别存在且为列表类型"""
        if category not in self.list_data:
            self.list_data[category] = []
        elif not isinstance(self.list_data[category], list):
            logger.warning(f"list_data[{category}]不是列表类型，重新初始化: {type(self.list_data[category])}")
            self.list_data[category] = []
    
    def _add_item_to_list_data(self, category, item_data):
        """将项目添加到列表数据中"""
        if isinstance(item_data, list):
            # 如果是列表，扩展到现有列表中
            for item in item_data:
                if isinstance(item, dict):
                    self.list_data[category].append(item)
                else:
                    logger.warning(f"跳过非字典类型的列表项: {type(item)} - {item}")
        elif isinstance(item_data, dict):
            # 如果是字典，直接添加
            self.list_data[category].append(item_data)
        else:
            logger.warning(f"跳过不支持的数据类型: {type(item_data)} - {item_data}")

    def load_data(self):
        """加载健康数据"""
        try:
            if os.path.exists('health_data.json'):
                with open('health_data.json', 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                    self.health_data = loaded_data.get('health_data', self.health_data)
                    self.lifestyle_selections = loaded_data.get('lifestyle_selections', self.lifestyle_selections)
                    self.lifestyle_fields = loaded_data.get('lifestyle_fields', self.lifestyle_fields)
                    self.list_data = loaded_data.get('list_data', self.list_data)
            else:
                # 模拟加载已保存的数据
                sample_data = {
                    'basic_info': {
                        'height': '175',
                        'weight': '70',
                        'blood_type': 'A型',
                        'rh_factor': '阳性'
                    },
                    'lifestyle_selections': {
                        'diet': ['荤素均衡'],
                        'sleep': ['主观感觉良好'],
                        'bowel': ['正常'],
                        'urination': ['正常'],
                        'work_stress': ['中等']
                    },
                    'lifestyle_fields': {
                        'exercise_exercise_type': '跑步',
                        'exercise_exercise_amount': '中度',
                        'exercise_exercise_frequency': '每周3-4次',
                        'smoking_quit_smoking': '从未吸烟',
                        'drinking_drinking_amount': '从不饮酒'
                    }
                }

                # 将示例数据填入数据结构
                for item_key, value in sample_data['basic_info'].items():
                    if item_key in self.health_data['basic_info']['items']:
                        self.health_data['basic_info']['items'][item_key]['value'] = value

                # 加载生活方式数据
                self.lifestyle_selections.update(sample_data['lifestyle_selections'])
                self.lifestyle_fields.update(sample_data['lifestyle_fields'])

            # 计算BMI
            self.calculate_bmi()

        except Exception as e:
            logger.error(f"加载健康数据时出错: {e}")

    def refresh_ui(self):
        """刷新UI"""
        try:
            # 确保main_layout存在
            if not hasattr(self, 'ids') or not isinstance(self.ids, dict):
                self.ids = {}
            
            # 如果main_layout不存在，调用ensure_main_layout创建它
            if 'main_layout' not in self.ids:
                main_layout = self.ensure_main_layout()
            else:
                main_layout = self.ids['main_layout']
                
            # 清空主布局
            if main_layout and hasattr(main_layout, 'clear_widgets'):
                main_layout.clear_widgets()

            # 添加健康分类卡片
            for category_key, category_data in self.health_data.items():
                # 确保category_data是字典类型
                if not isinstance(category_data, dict):
                    logger.warning(f"跳过非字典类型的分类数据: {category_key} - {type(category_data)}")
                    continue
                    
                # 检查必要的字段
                if 'title' not in category_data or 'icon' not in category_data:
                    logger.warning(f"分类数据缺少必要字段: {category_key} - {category_data}")
                    continue
                    
                card = HealthCategoryCard(
                    title=category_data['title'],
                    icon=category_data['icon'],
                    icon_color=category_data['color'],
                    category_data=category_data,
                    screen_ref=self
                )
                if main_layout and hasattr(main_layout, 'add_widget'):
                    main_layout.add_widget(card)

        except Exception as e:
            logger.error(f"刷新UI时出错: {e}")

    def show_message(self, message):
        """显示消息"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8
            )
            snackbar.open()
        except Exception as e:
            logger.error(f"显示消息时出错: {e}")

# 用于测试的应用程序
if __name__ == '__main__':
    class TestApp(MDApp):
        theme = AppTheme
        metrics = AppMetrics
        font_styles = FontStyles

        def build(self):
            self.user_data = {"username": "测试用户", "user_id": "test123", "gender": "男", "age": 45}
            return BasicHealthInfoScreen()

    TestApp().run()