#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KivyMD内置日期选择器封装
使用KivyMD 2.0.1 dev0的MDModalDatePicker组件
符合Material Design 3规范和theme.py配置
支持中文界面显示，包括月份和星期名称

分析用户提供的脚本：
- MDDatePicker类在KivyMD 2.0.1 dev0中不存在，应使用MDModalDatePicker
- locale参数不被支持，需要通过自定义方式实现中文显示
- 全局字体设置可以确保中文正确渲染
"""

import logging
from datetime import datetime, date
from typing import Callable, Optional
from kivy.clock import Clock
from kivy.lang import Builder
from kivymd.uix.pickers import MDModalDatePicker
from kivymd.uix.label import MDLabel
from kivymd.app import MDApp
from kivy.logger import Logger

# 配置日志
logger = logging.getLogger(__name__)

# 中文月份名称
CHINESE_MONTHS = [
    "一月", "二月", "三月", "四月", "五月", "六月",
    "七月", "八月", "九月", "十月", "十一月", "十二月"
]

# 中文星期名称（从周一到周日）
CHINESE_WEEKDAYS = ["一", "二", "三", "四", "五", "六", "日"]

class ChineseMDModalDatePicker(MDModalDatePicker):
    """
    支持中文显示的KivyMD日期选择器
    
    继承自MDModalDatePicker，通过自定义更新方法实现中文月份和星期显示，
    并使用全局字体设置确保正确渲染中文。
    """
    
    def __init__(self, **kwargs):
        """
        初始化中文日期选择器
        
        Args:
            **kwargs: 传递给父类的参数
        """
        # 设置中文按钮文本
        kwargs.setdefault('text_button_ok', '确定')
        kwargs.setdefault('text_button_cancel', '取消')
        
        super().__init__(**kwargs)
        
        # 绑定打开事件以确保字体和本地化正确应用
        self.bind(on_open=self._on_open_chinese)
        
        Logger.info("ChineseMDModalDatePicker: 中文日期选择器初始化完成")
    
    def _on_open_chinese(self, *args):
        """
        日期选择器打开时确保字体和本地化正确应用
        
        Args:
            *args: 事件参数
        """
        Logger.info("ChineseMDModalDatePicker: 日期选择器已打开，应用全局字体和中文本地化")
        # 延迟执行以确保界面完全加载
        Clock.schedule_once(self._ensure_font_and_localization, 0.1)
    
    def _ensure_font_and_localization(self, dt):
        """
        确保全局字体设置和中文本地化正确应用
        
        Args:
            dt: 时间间隔参数
        """
        try:
            # 获取当前应用实例
            app = MDApp.get_running_app()
            if app and hasattr(app, 'theme_cls'):
                # 确保主题字体设置正确应用
                self._apply_theme_font(self)
                Logger.info("ChineseMDModalDatePicker: 全局字体设置已应用")
            else:
                Logger.warning("ChineseMDModalDatePicker: 无法获取应用主题设置")
            
            # 应用中文本地化
            self.update_week_names()
            self.update_days()
            
        except Exception as e:
            Logger.error(f"ChineseMDModalDatePicker: 应用字体和本地化时出错: {e}")
    
    def _apply_theme_font(self, widget):
        """
        递归应用主题字体到所有子组件
        
        Args:
            widget: 要应用字体的组件
        """
        try:
            # 如果组件有font_name属性，确保使用支持中文的字体
            if hasattr(widget, 'font_name'):
                widget.font_name = 'NotoSans'
            
            # 递归处理子组件
            if hasattr(widget, 'children'):
                for child in widget.children:
                    self._apply_theme_font(child)
                    
        except Exception as e:
            Logger.warning(f"ChineseMDModalDatePicker: 应用字体到组件时出错: {e}")
    
    def update_week_names(self, *args):
        """
        更新星期名称为中文
        """
        try:
            if hasattr(self.ids, 'week_nums'):
                self.ids.week_nums.clear_widgets()
                for day in CHINESE_WEEKDAYS:
                    label = MDLabel(
                        text=day,
                        halign="center",
                        valign="center",
                        theme_text_color="Secondary",
                        font_style="Body",
                        role="small"
                    )
                    self.ids.week_nums.add_widget(label)
                Logger.info("ChineseMDModalDatePicker: 星期名称已更新为中文")
        except Exception as e:
            Logger.error(f"ChineseMDModalDatePicker: 更新星期名称失败: {e}")
    
    def update_days(self, *args):
        """
        更新日期显示，包括月份标题为中文
        
        调用super以保持原功能，然后更新月份标题
        """
        super().update_days(*args)
        try:
            if hasattr(self.ids, 'label_title'):
                self.ids.label_title.text = f"{CHINESE_MONTHS[self.sel_month - 1]} {self.sel_year}"
                Logger.info("ChineseMDModalDatePicker: 月份标题已更新为中文")
        except Exception as e:
            Logger.error(f"ChineseMDModalDatePicker: 更新月份标题失败: {e}")
    
    def on_ok(self, *args):
        """
        重写OK方法，添加日志
        
        Args:
            *args: 参数列表，第一个参数是日期选择器实例
        """
        # 处理参数
        if args and len(args) >= 1:
            instance_date_picker = args[0]
            # 获取选中的日期
            try:
                selected_dates = instance_date_picker.get_date()
                if selected_dates:
                    selected_date = selected_dates[0]  # 获取第一个选中的日期
                    logger.info(f"用户选择日期: {selected_date}")
                else:
                    selected_date = datetime.now().date()
                    logger.warning("无法获取选中的日期，使用默认日期")
            except Exception as e:
                selected_date = datetime.now().date()
                logger.warning(f"获取选中日期时出错，使用默认日期: {e}")
        else:
            # 从当前选择器获取选中的日期
            try:
                if hasattr(self, 'sel_year') and hasattr(self, 'sel_month') and hasattr(self, 'sel_day'):
                    selected_date = datetime(self.sel_year, self.sel_month, self.sel_day).date()
                    logger.info(f"用户选择日期: {selected_date}")
                else:
                    selected_date = datetime.now().date()
                    logger.info("使用当前日期作为默认选择")
            except Exception as e:
                selected_date = datetime.now().date()
                logger.warning(f"获取选中日期时出错，使用默认日期: {e}")
        
        # 调用父类方法
        return super().on_ok(*args)
    
    def on_cancel(self, *args):
        """
        重写取消方法，添加日志
        """
        logger.info("用户取消日期选择")
        return super().on_cancel(*args)

class KivyMDDatePickerManager:
    """
    KivyMD内置日期选择器管理器
    封装MDModalDatePicker的使用，提供统一的接口
    """
    
    def __init__(self):
        """初始化日期选择器管理器"""
        self.current_picker = None
        self.callback = None
        
    def show_date_picker(
        self, 
        callback: Callable[[date], None],
        initial_date: Optional[date] = None,
        title: str = "选择日期",
        min_date: Optional[date] = None,
        max_date: Optional[date] = None
    ) -> None:
        """
        显示KivyMD内置日期选择器
        
        Args:
            callback: 日期选择回调函数，接收选中的日期
            initial_date: 初始日期，默认为今天
            title: 对话框标题（注：MDModalDatePicker不直接支持标题，但保留参数以兼容）
            min_date: 最小可选日期
            max_date: 最大可选日期
        """
        try:
            # 关闭之前的选择器
            if self.current_picker:
                self.current_picker.dismiss()
                
            # 设置回调函数
            self.callback = callback
            
            # 处理初始日期
            if initial_date is None:
                initial_date = datetime.now().date()
            
            # 创建日期选择器
            self.current_picker = ChineseMDModalDatePicker(
                year=initial_date.year,
                month=initial_date.month,
                day=initial_date.day,
                min_date=min_date,
                max_date=max_date
            )
            
            # 绑定事件（使用on_ok代替on_save）
            self.current_picker.bind(
                on_ok=self._on_date_selected,
                on_cancel=self._on_date_cancelled
            )
            
            # 显示选择器
            self.current_picker.open()
            
            logger.info(f"显示KivyMD日期选择器，标题: {title}")
            
        except Exception as e:
            logger.error(f"显示日期选择器失败: {e}")
            # 回退到当前日期
            if callback:
                callback(datetime.now().date())
    
    def _on_date_selected(self, instance_picker, selected_date=None, date_range=None) -> None:
        """
        日期选择回调处理
        
        Args:
            instance_picker: 日期选择器实例
            selected_date: 选中的日期
            date_range: 日期范围（对于单日期模式通常为None）
        """
        try:
            # 如果selected_date为None，尝试从instance_picker获取日期
            if selected_date is None:
                try:
                    selected_dates = instance_picker.get_date()
                    if selected_dates:
                        selected_date = selected_dates[0]  # 获取第一个选中的日期
                    else:
                        selected_date = datetime.now().date()
                        logger.warning("无法从选择器获取日期，使用默认日期")
                except Exception as e:
                    selected_date = datetime.now().date()
                    logger.warning(f"获取选中日期时出错，使用默认日期: {e}")
            
            logger.info(f"用户选择日期: {selected_date}")
            
            # 调用回调函数
            if self.callback:
                self.callback(selected_date)
                
            # 关闭选择器
            instance_picker.dismiss()
            self.current_picker = None
            self.callback = None
            
        except Exception as e:
            logger.error(f"处理日期选择失败: {e}")
    
    def _on_date_cancelled(self, instance_picker) -> None:
        """
        日期选择取消回调处理
        
        Args:
            instance_picker: 日期选择器实例
        """
        try:
            logger.info("用户取消日期选择")
            
            # 关闭选择器
            instance_picker.dismiss()
            self.current_picker = None
            self.callback = None
            
        except Exception as e:
            logger.error(f"处理日期选择取消失败: {e}")
    
    def dismiss(self) -> None:
        """
        手动关闭当前日期选择器
        """
        if self.current_picker:
            self.current_picker.dismiss()
            self.current_picker = None
            self.callback = None

# 全局单例实例
_date_picker_manager = None

def get_date_picker_manager() -> KivyMDDatePickerManager:
    """
    获取日期选择器管理器单例
    
    Returns:
        KivyMDDatePickerManager: 日期选择器管理器实例
    """
    global _date_picker_manager
    if _date_picker_manager is None:
        _date_picker_manager = KivyMDDatePickerManager()
    return _date_picker_manager

def show_kivymd_date_picker(
    callback: Callable[[date], None],
    initial_date: Optional[date] = None,
    title: str = "选择日期",
    min_date: Optional[date] = None,
    max_date: Optional[date] = None
) -> None:
    """
    便捷函数：显示KivyMD内置日期选择器
    
    Args:
        callback: 日期选择回调函数
        initial_date: 初始日期
        title: 对话框标题
        min_date: 最小可选日期
        max_date: 最大可选日期
    """
    manager = get_date_picker_manager()
    manager.show_date_picker(
        callback=callback,
        initial_date=initial_date,
        title=title,
        min_date=min_date,
        max_date=max_date
    )


# ============================================================================
# 简化的示例应用类（基于用户提供的脚本改进）
# ============================================================================

class SimpleDatePickerApp(MDApp):
    """
    简化的日期选择器示例应用
    
    这是基于用户提供脚本的改进版本，使用KivyMD 2.0.1 dev0兼容的组件：
    - 使用MDModalDatePicker替代不存在的MDDatePicker
    - 移除不支持的locale参数
    - 通过中文按钮文本和全局字体设置实现中文支持
    """
    
    def build(self):
        """
        构建应用界面
        
        Returns:
            Widget: 根组件
        """
        # 设置主题
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # KV字符串定义界面
        kv = '''
MDFloatLayout:
    md_bg_color: self.theme_cls.backgroundColor
    
    MDButton:
        style: "filled"
        pos_hint: {"center_x": 0.5, "center_y": 0.5}
        size_hint: None, None
        size: dp(200), dp(50)
        on_release: app.show_date_picker()
        
        MDButtonText:
            text: "选择日期"
            
    MDLabel:
        id: date_label
        text: "请选择日期"
        halign: "center"
        pos_hint: {"center_x": 0.5, "center_y": 0.3}
        font_style: "Title"
        role: "medium"
        theme_text_color: "Primary"
'''
        return Builder.load_string(kv)
    
    def show_date_picker(self):
        """
        显示日期选择器
        
        使用KivyMD 2.0.1 dev0兼容的方式：
        - 使用MDModalDatePicker而非MDDatePicker
        - 设置中文按钮文本
        - 依赖全局字体设置显示中文
        """
        try:
            # 创建日期选择器（KivyMD 2.0.1 dev0兼容方式）
            date_dialog = MDModalDatePicker(
                # 设置中文按钮文本
                text_button_ok="确定",
                text_button_cancel="取消",
                supporting_text="选择日期",
                # 设置日期范围（可选）
                min_date=date(1920, 1, 1),
                max_date=date(2050, 12, 31),
            )
            
            # 绑定选择回调
            date_dialog.bind(on_ok=self.on_save)
            date_dialog.bind(on_cancel=self.on_cancel)
            
            # 打开日期选择器
            date_dialog.open()
            
            print("中文日期选择器已打开")
            
        except Exception as e:
            print(f"打开日期选择器时出错: {e}")
            logger.error(f"日期选择器错误: {e}")
    
    def on_save(self, instance, value, date_range=None):
        """
        日期选择确认回调
        
        Args:
            instance: 日期选择器实例
            value (date): 选择的日期对象
            date_range: 日期范围（范围选择模式时使用）
        """
        try:
            if value:
                # 格式化日期显示
                formatted_date = value.strftime("%Y年%m月%d日")
                self.root.ids.date_label.text = f"选择的日期: {formatted_date}"
                print(f"用户选择的日期: {value} ({formatted_date})")
            
            # 关闭选择器
            instance.dismiss()
            
        except Exception as e:
            print(f"处理日期选择时出错: {e}")
            logger.error(f"日期选择处理错误: {e}")
    
    def on_cancel(self, instance):
        """
        日期选择取消回调
        
        Args:
            instance: 日期选择器实例
        """
        print("用户取消了日期选择")
        instance.dismiss()


# 示例运行函数
def run_simple_date_picker_example():
    """
    运行简化的日期选择器示例
    
    这个函数展示了如何正确使用KivyMD 2.0.1 dev0的日期选择器
    """
    try:
        app = SimpleDatePickerApp()
        app.run()
    except Exception as e:
        print(f"运行示例应用时出错: {e}")
        logger.error(f"示例应用错误: {e}")


if __name__ == "__main__":
    # 如果直接运行此文件，启动示例应用
    run_simple_date_picker_example()