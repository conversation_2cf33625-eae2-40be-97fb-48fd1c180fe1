"""健康日记UI管理器

负责健康日记创建屏幕的UI组件管理和创建
遵循KivyMD 2.0.1 dev0规范和Material Design 3设计原则
支持图表显示功能（折线图与条形图，默认条形图）
保持对dialogs.py、health_chart_utils.py、日期选择器及时间选择器的调用
"""

import logging
from datetime import date
from typing import Dict, List, Optional

from kivy.metrics import dp
from kivy.properties import ObjectProperty
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import MDTextField
from kivymd.uix.scrollview import MDScrollView

# 导入健康图表工具
try:
    from mobile.utils.health_chart_utils import HealthChartView
    HEALTH_CHART_AVAILABLE = True
    logging.info("健康图表工具导入成功")
except ImportError as e:
    logging.warning(f"健康图表工具导入失败: {e}")
    HEALTH_CHART_AVAILABLE = False
    HealthChartView = None

logger = logging.getLogger(__name__)


class HealthDiaryUIManager:
    """健康日记UI管理器
    
    负责管理健康日记创建屏幕的所有UI组件创建和更新
    """
    
    def __init__(self, screen_instance):
        """初始化UI管理器
        
        Args:
            screen_instance: 健康日记创建屏幕实例
        """
        self.screen = screen_instance
        self.widgets = {}  # 存储UI组件引用
        
    def create_base_layout(self) -> bool:
        """创建基础布局框架
        
        Returns:
            bool: 创建成功返回True，否则返回False
        """
        try:
            main_layout = self.screen.ids.get('main_layout')
            if not main_layout:
                raise ValueError("未找到main_layout")
                
            # 检查是否已经创建过内容，避免重复创建
            if self.screen.ids.get('content_scroll'):
                logger.info("基础布局已存在，跳过重复创建")
                return True
                
            # 创建滚动容器
            content_scroll = MDScrollView(
                id='content_scroll',
                do_scroll_x=False,
                do_scroll_y=True,
                bar_width=dp(4)
            )
            
            # 创建根容器
            root_container = MDBoxLayout(
                id='root_container',
                orientation="vertical",
                adaptive_height=True,
                padding=[dp(16), dp(8), dp(16), dp(16)],
                spacing=dp(16)
            )
            
            # 创建模块容器
            modules_container = MDBoxLayout(
                id='modules_container',
                orientation="vertical",
                adaptive_height=True,
                spacing=dp(16)
            )
            
            # 保存引用
            self.widgets['content_scroll'] = content_scroll
            self.widgets['root_container'] = root_container
            self.widgets['modules_container'] = modules_container
            
            # 组装布局
            root_container.add_widget(modules_container)
            content_scroll.add_widget(root_container)
            main_layout.add_widget(content_scroll, index=0)
            
            logger.info("基础布局创建完成")
            return True
        except Exception as e:
            logger.error(f"创建基础布局失败: {e}")
            return False
    
    def create_panels_for_basescreen(self):
        """为BaseScreen创建面板 - 不创建额外的ScrollView"""
        try:
            main_layout = self.screen.ids.get('main_layout')
            if not main_layout:
                logger.error("未找到main_layout")
                return
                
            # 直接在main_layout中创建模块容器，不创建ScrollView
            modules_container = MDBoxLayout(
                id='modules_container',
                orientation="vertical",
                adaptive_height=True,
                spacing=dp(16),
                padding=[dp(16), dp(8), dp(16), dp(16)]
            )
            
            # 保存引用
            self.widgets['modules_container'] = modules_container
            
            # 将模块容器直接添加到main_layout
            main_layout.add_widget(modules_container)
            
            # 创建所有面板
            self._create_all_panels(modules_container)
            
            logger.info("BaseScreen兼容面板创建完成")
        except Exception as e:
            logger.error(f"创建BaseScreen兼容面板失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _create_all_panels(self, modules_container):
        """创建所有面板的内部方法"""
        # 日期选择面板（非折叠）
        date_panel = self._create_date_panel()
        modules_container.add_widget(date_panel)
        self.widgets["date_panel"] = date_panel
        
        # 今日情况面板（非折叠）
        condition_panel = self._create_condition_panel()
        modules_container.add_widget(condition_panel)
        self.widgets["condition_panel"] = condition_panel
        
        # 血压记录面板
        bp_panel = self._create_record_panel("血压", "heart", "blood_pressure_panel")
        modules_container.add_widget(bp_panel)
        self.widgets["blood_pressure_panel"] = bp_panel
        
        # 血糖记录面板
        bs_panel = self._create_record_panel("血糖", "water", "blood_sugar_panel")
        modules_container.add_widget(bs_panel)
        self.widgets["blood_sugar_panel"] = bs_panel
        
        # 体重记录面板
        weight_panel = self._create_weight_panel()
        modules_container.add_widget(weight_panel)
        self.widgets["weight_panel"] = weight_panel
        
        # 运动记录面板
        exercise_panel = self._create_text_panel("运动", "run", "exercise_panel", "运动类型和时长")
        modules_container.add_widget(exercise_panel)
        self.widgets["exercise_panel"] = exercise_panel
        
        # 睡眠记录面板
        sleep_panel = self._create_text_panel("睡眠", "sleep", "sleep_panel", "睡眠情况")
        modules_container.add_widget(sleep_panel)
        self.widgets["sleep_panel"] = sleep_panel

    def create_panels(self):
        """创建所有面板 - 原有方法保持不变"""
        try:
            # 检查面板是否已经创建，避免重复创建
            if 'modules_container' not in self.widgets:
                logger.warning("模块容器未找到，无法创建面板")
                return
                
            modules_container = self.widgets['modules_container']
            self._create_all_panels(modules_container)
            
            logger.info("所有面板创建完成")
            logger.debug(f"Final widgets dict keys: {list(self.widgets.keys())}")
        except Exception as e:
            logger.error(f"创建面板失败: {e}")
    
    def _create_input_field(self, hint_text: str, input_filter: Optional[str] = None, 
                           size_hint_x: Optional[float] = None, font_size: float = dp(14)) -> MDTextField:
        """创建输入字段的通用方法
        
        Args:
            hint_text: 提示文本
            input_filter: 输入过滤器
            size_hint_x: 宽度比例
            font_size: 字体大小
            
        Returns:
            MDTextField: 输入字段组件
        """
        from kivymd.uix.textfield import MDTextFieldHintText
        
        field = MDTextField(
            MDTextFieldHintText(
                text=hint_text,
                font_size=font_size
            ),
            mode="outlined",
            size_hint_x=size_hint_x,
            size_hint_y=None,
            height=dp(40),  # 统一高度
            font_size=font_size
        )
        
        if input_filter:
            field.input_filter = input_filter
            
        return field
    
    def _create_weight_panel(self) -> MDCard:
        """创建体重记录面板
        
        Returns:
            MDCard: 体重记录面板
        """
        # 从屏幕模块中获取DiaryPanelCard类
        try:
            # 尝试从屏幕实例的模块中获取DiaryPanelCard类
            import sys
            screen_module = sys.modules[self.screen.__class__.__module__]
            DiaryPanelCard = getattr(screen_module, 'DiaryPanelCard')
        except (AttributeError, KeyError):
            # 如果无法从模块获取，则使用屏幕实例的类属性
            DiaryPanelCard = self.screen.__class__.DiaryPanelCard
        
        panel = DiaryPanelCard(
            title="体重",
            icon="scale-bathroom",
            panel_id="weight_panel",
            screen_ref=self.screen,
            expanded=self.screen._panel_states.get("weight", False)
        )
        content = panel.ids.content_layout
        
        # 创建顶部功能按钮区域
        top_bar = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(40),
            spacing=dp(4)
        )
        
        top_bar.add_widget(MDBoxLayout())  # 弹性空间
        
        content.add_widget(top_bar)
        
        # 创建录入区域
        record_container = MDBoxLayout(
            orientation="horizontal",
            adaptive_height=True,
            spacing=dp(4),
            size_hint_y=None,
            height=dp(40)
        )
        
        input_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(4),
            size_hint_x=1.0
        )
        
        weight_field = self._create_input_field(
            "体重 (kg)",
            input_filter="float",
            size_hint_x=0.4,
            font_size=dp(14)
        )
        
        # 创建一个标签来显示体重状态
        status_label = MDLabel(
            text="状态",
            theme_text_color="Primary",
            font_size=dp(14),
            halign="left",
            valign="center"
        )
        
        # 创建一个容器来容纳标签和下拉箭头
        status_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(4),
            size_hint_x=0.3
        )
        
        status_container.add_widget(status_label)
        
        # 添加下拉箭头图标
        from kivymd.uix.label import MDIcon
        dropdown_icon = MDIcon(
            icon="menu-down",
            theme_text_color="Secondary",
            font_size=dp(16)
        )
        status_container.add_widget(dropdown_icon)
        
        # 保存引用
        self.widgets["weight_field"] = weight_field
        self.widgets["weight_status_label"] = status_label
        self.widgets["weight_status_container"] = status_container
        
        # 绑定点击事件到容器
        status_container.bind(on_touch_down=self._on_weight_status_touch)
        
        input_container.add_widget(weight_field)
        input_container.add_widget(status_container)
        
        record_container.add_widget(input_container)
        content.add_widget(record_container)
        
        return panel
    
    def _on_weight_status_touch(self, instance, touch):
        """处理体重状态容器的触摸事件"""
        if instance.collide_point(*touch.pos):
            # 显示体重状态选择菜单
            self.screen.show_weight_status_menu(instance)
            return True
        return False
    
    def _create_text_panel(self, title: str, icon: str, pid: str, hint: str) -> MDCard:
        """创建文本输入面板（运动/睡眠）
        
        Args:
            title: 面板标题
            icon: 图标名称
            pid: 面板ID
            hint: 提示文本
            
        Returns:
            MDCard: 文本输入面板
        """
        # 从屏幕模块中获取DiaryPanelCard类
        try:
            # 尝试从屏幕实例的模块中获取DiaryPanelCard类
            import sys
            screen_module = sys.modules[self.screen.__class__.__module__]
            DiaryPanelCard = getattr(screen_module, 'DiaryPanelCard')
        except (AttributeError, KeyError):
            # 如果无法从模块获取，则使用屏幕实例的类属性
            DiaryPanelCard = self.screen.__class__.DiaryPanelCard
        
        panel = DiaryPanelCard(
            title=title,
            icon=icon,
            panel_id=pid,
            screen_ref=self.screen,
            expanded=self.screen._panel_states.get(pid.replace("_panel", ""), False)
        )
        content = panel.ids.content_layout
        
        # 创建顶部功能按钮区域
        top_bar = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(40),
            spacing=dp(4)
        )
        
        # 添加弹性空间将按钮推到右侧
        top_bar.add_widget(MDBoxLayout())
        
        content.add_widget(top_bar)
        
        # 文本输入区域
        field = MDTextField(
            mode="outlined",
            multiline=True,
            size_hint_y=None,
            height=dp(100),
            max_height=dp(200),
            font_size=dp(14)
        )
        
        # 添加提示文本
        from kivymd.uix.textfield import MDTextFieldHintText
        field.add_widget(MDTextFieldHintText(text=hint, font_size=dp(14)))
        
        # 绑定事件使文本框随内容增加而向下扩展
        def update_height(instance, value):
            # 基于文本行数计算高度
            lines = len(instance.text.split('\n'))
            instance.height = max(dp(100), lines * dp(20) + dp(20))
        
        field.bind(text=update_height)
        
        content.add_widget(field)
        self.widgets[f"{pid}_field"] = field
        return panel
    
    def _create_condition_panel(self) -> MDBoxLayout:
        """创建今日情况面板（非折叠面板）
        
        Returns:
            MDBoxLayout: 今日情况面板
        """
        # 创建一个普通的MDBoxLayout作为非折叠面板
        panel = MDBoxLayout(
            orientation="vertical",
            adaptive_height=True,
            spacing=dp(12),
            padding=[dp(16), dp(12), dp(16), dp(12)]
        )
        
        # 文本输入区域
        field = MDTextField(
            mode="outlined",
            multiline=True,
            size_hint_y=None,
            height=dp(120),
            max_height=dp(300),
            font_size=dp(14)
        )
        
        # 添加提示文本
        from kivymd.uix.textfield import MDTextFieldHintText
        field.add_widget(MDTextFieldHintText(text="描述身体状况", font_size=dp(14)))
        
        # 绑定事件使文本框随内容增加而向下扩展
        def update_height(instance, value):
            # 基于文本行数计算高度
            lines = len(instance.text.split('\n'))
            instance.height = max(dp(120), lines * dp(20) + dp(20))
        
        field.bind(text=update_height)
        
        panel.add_widget(field)
        self.widgets["condition_field"] = field
        return panel
    
    def _create_date_panel(self) -> MDBoxLayout:
        """创建日期选择面板（非折叠面板）
        
        Returns:
            MDBoxLayout: 日期选择面板
        """
        # 创建一个普通的MDBoxLayout作为非折叠面板
        panel = MDBoxLayout(
            orientation="vertical",
            adaptive_height=True,
            spacing=dp(12),
            padding=[dp(16), dp(12), dp(16), dp(12)]
        )
        
        # 删除标题栏和分隔线，直接添加内容区域
        content = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(40),
            spacing=dp(12)
        )
        
        date_field = MDTextField(
            text=self.screen.selected_date,
            readonly=True,
            mode="outlined",
            size_hint_x=0.7,
            height=dp(40),
            size_hint_y=None,
            font_size=dp(14)
        )
        
        # 添加提示文本
        from kivymd.uix.textfield import MDTextFieldHintText
        date_field.add_widget(MDTextFieldHintText(text="选择日期", font_size=dp(14)))
        
        from kivymd.uix.button import MDIconButton
        date_button = MDIconButton(
            icon="calendar",
            size_hint_x=0.3,
            on_release=lambda x: self.screen.show_date_picker(),
            radius=[dp(8)],
            size_hint_y=None,
            height=dp(48)
        )
        content.add_widget(date_field)
        content.add_widget(date_button)
        panel.add_widget(content)
        self.widgets["date_field"] = date_field
        self.widgets["date_picker_btn"] = date_button  # 保存日期选择按钮的引用
        
        # 添加调试信息
        logger.debug(f"Date field created and stored in widgets dict. Keys: {list(self.widgets.keys())}")
        
        return panel
    
    def _create_panel_header(self, title: str, pid: str) -> MDBoxLayout:
        """创建面板头部（通用的顶部功能按钮区域）
        
        Args:
            title: 面板标题
            pid: 面板ID
            
        Returns:
            MDBoxLayout: 顶部功能按钮区域布局
        """
        # 创建顶部功能按钮区域
        top_bar = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(40),
            spacing=dp(6)
        )
        
        # 添加弹性空间将按钮推到右侧
        top_bar.add_widget(MDBoxLayout())
        
        # 添加记录按钮
        add_btn = MDButton(
            style="tonal",
            size_hint_y=None,
            height=dp(40),
            width=dp(100),
            size_hint_x=None
        )
        
        # 根据面板类型设置不同的添加函数
        if pid == "blood_pressure_panel":
            add_btn.bind(on_release=lambda x: self.screen.add_blood_pressure_record())
        elif pid == "blood_sugar_panel":
            add_btn.bind(on_release=lambda x: self.screen.add_blood_sugar_record())
        
        add_btn.add_widget(MDButtonText(
            text=f"添加{title}",
            font_size=dp(14)
        ))
        top_bar.add_widget(add_btn)
        
        # 保存按钮引用
        self.widgets[f"{pid}_add_btn"] = add_btn
        
        return top_bar
    
    def _create_record_panel(self, title: str, icon: str, pid: str) -> MDCard:
        """创建记录面板（血压/血糖）
        
        Args:
            title: 面板标题
            icon: 图标名称
            pid: 面板ID
            
        Returns:
            MDCard: 记录面板
        """
        # 从屏幕模块中获取DiaryPanelCard类
        try:
            # 尝试从屏幕实例的模块中获取DiaryPanelCard类
            import sys
            screen_module = sys.modules[self.screen.__class__.__module__]
            DiaryPanelCard = getattr(screen_module, 'DiaryPanelCard')
        except (AttributeError, KeyError):
            # 如果无法从模块获取，则使用屏幕实例的类属性
            DiaryPanelCard = self.screen.__class__.DiaryPanelCard
        
        panel = DiaryPanelCard(
            title=title,
            icon=icon,
            panel_id=pid,
            screen_ref=self.screen,
            expanded=self.screen._panel_states.get(pid.replace("_panel", ""), False)
        )
        content = panel.ids.content_layout
        
        # 如果是血压面板或血糖面板，先添加图表容器到顶部
        if pid in ["blood_pressure_panel", "blood_sugar_panel"]:
            # 添加图表容器到面板顶部
            chart_container = MDBoxLayout(
                orientation="vertical",
                size_hint_y=None,
                height=dp(300),
                spacing=dp(4),
                padding=[dp(8), dp(8), dp(8), dp(8)]
            )
            chart_container.bind(minimum_height=chart_container.setter('height'))
            content.add_widget(chart_container)
            # 直接保存图表容器引用
            self.widgets[f"{pid}_chart"] = chart_container
            logger.info(f"{title}图表容器已创建并保存")
            
            # 创建顶部功能按钮区域
            top_bar = self._create_panel_header(title, pid)
            content.add_widget(top_bar)
        else:
            # 非血压/血糖面板的正常处理
            # 创建顶部功能按钮区域
            top_bar = self._create_panel_header(title, pid)
            content.add_widget(top_bar)
            
            # 添加记录显示区域
            records_container = MDBoxLayout(
                orientation="vertical",
                size_hint_y=None,
                spacing=dp(4)
            )
            records_container.bind(minimum_height=records_container.setter('height'))
            content.add_widget(records_container)
            self.widgets[f"{pid}_records"] = records_container
        
        self.widgets[f"{pid}_container"] = content
        return panel
    
    def update_blood_pressure_chart(self):
        """更新血压图表显示"""
        try:
            self._update_chart_generic(
                "blood_pressure_panel",
                self.screen.blood_pressure_records,
                "血压趋势图",
                [("收缩压", "systolic"), ("舒张压", "diastolic"), ("心率", "heart_rate")]
            )
        except Exception as e:
            logger.error(f"更新血压图表失败: {e}")
    
    def update_blood_sugar_chart(self):
        """更新血糖图表显示"""
        try:
            self._update_chart_generic(
                "blood_sugar_panel",
                self.screen.blood_sugar_records,
                "血糖趋势图",
                [("血糖值", "value")]
            )
        except Exception as e:
            logger.error(f"更新血糖图表失败: {e}")
    
    def _find_or_create_chart_container(self, panel_key: str, chart_title: str):
        """查找或创建图表容器
        
        Args:
            panel_key: 面板键名
            chart_title: 图表标题
            
        Returns:
            图表容器对象，如果无法找到或创建则返回None
        """
        # 获取图表容器
        chart_container_key = f"{panel_key}_chart"
        chart_container = self.widgets.get(chart_container_key)
        
        # 如果图表容器不存在，尝试从界面中查找
        if not chart_container:
            logger.debug(f"未找到{chart_title}图表容器，尝试从界面中查找")
            # 获取面板内容容器
            panel_container_key = f"{panel_key}_container"
            panel_container = self.widgets.get(panel_container_key)
            
            # 如果在widgets中找不到，尝试从界面中查找面板容器
            if not panel_container:
                # 查找面板组件
                panel_widget = self.widgets.get(panel_key.replace("_chart", "_panel"))
                if panel_widget and hasattr(panel_widget, 'ids') and 'content_layout' in panel_widget.ids:
                    panel_container = panel_widget.ids.content_layout
                    # 保存引用
                    self.widgets[panel_container_key] = panel_container
            
            if panel_container:
                # 查找图表容器子组件
                for child in panel_container.children:
                    if hasattr(child, 'id') and child.id == chart_container_key:
                        chart_container = child
                        # 保存引用以避免下次查找
                        self.widgets[chart_container_key] = chart_container
                        logger.debug(f"从界面中找到并保存{chart_title}图表容器引用")
                        break
        
        # 如果仍未找到图表容器，创建新的
        if not chart_container:
            logger.debug(f"仍未找到{chart_title}图表容器，创建新的图表容器")
            # 获取面板内容容器
            panel_container_key = f"{panel_key}_container"
            panel_container = self.widgets.get(panel_container_key)
            
            # 如果在widgets中找不到，尝试从界面中查找面板容器
            if not panel_container:
                # 查找面板组件
                panel_widget = self.widgets.get(panel_key.replace("_chart", "_panel"))
                if panel_widget and hasattr(panel_widget, 'ids') and 'content_layout' in panel_widget.ids:
                    panel_container = panel_widget.ids.content_layout
                    # 保存引用
                    self.widgets[panel_container_key] = panel_container
            
            if panel_container:
                # 创建新的图表容器
                chart_container = MDBoxLayout(
                    orientation="vertical",
                    size_hint_y=None,
                    height=dp(300),
                    spacing=dp(4),
                    padding=[dp(8), dp(8), dp(8), dp(8)],
                    id=chart_container_key
                )
                chart_container.bind(minimum_height=chart_container.setter('height'))
                
                # 添加到面板内容的顶部
                panel_container.add_widget(chart_container, len(panel_container.children))
                
                # 保存引用
                self.widgets[chart_container_key] = chart_container
                logger.info(f"创建并保存{chart_title}图表容器引用")
            else:
                logger.debug(f"无法找到{panel_key}面板容器，无法创建图表容器")
                return None
        
        return chart_container
    
    def _update_chart_generic(self, panel_key: str, records: List[Dict], chart_title: str, 
                             y_fields: List[tuple]):
        """通用图表更新函数，减少重复代码"""
        try:
            # 获取图表容器
            chart_container = self._find_or_create_chart_container(panel_key, chart_title)
            
            # 如果无法找到或创建图表容器，直接返回
            if not chart_container:
                return

            # 清空现有图表
            chart_container.clear_widgets()

            # 使用健康图表工具显示图表
            if HEALTH_CHART_AVAILABLE and HealthChartView is not None:
                # 创建图表数据
                times = [record.get('time', '--:--') for record in records]
                
                # 根据不同的面板类型创建不同的数据结构
                chart_data = {
                    'title': chart_title,
                    'x_data': times,
                }
                
                if panel_key == "blood_pressure_panel":
                    # 血压数据
                    systolic_values = [int(record.get('systolic', 0)) for record in records]
                    diastolic_values = [int(record.get('diastolic', 0)) for record in records]
                    heart_rate_values = [int(record.get('heart_rate', 0)) for record in records]
                    
                    chart_data['y_data'] = {
                        '收缩压': systolic_values,
                        '舒张压': diastolic_values,
                        '心率': heart_rate_values
                    }
                    chart_data['unit'] = ''
                else:
                    # 血糖数据
                    sugar_values = [float(record.get('value', 0)) for record in records]
                    chart_data['y_data'] = {
                        '血糖值': sugar_values
                    }
                    chart_data['unit'] = 'mmol/L'

                # 检查是否已经存在图表视图，避免重复创建
                existing_chart_view = None
                for child in chart_container.children:
                    if HealthChartView is not None and isinstance(child, HealthChartView):
                        existing_chart_view = child
                        break
                
                chart_view = None
                if existing_chart_view:
                    # 如果已存在图表视图，直接更新数据
                    existing_chart_view.update_chart(chart_data)
                    chart_view = existing_chart_view
                else:
                    # 创建新的图表视图
                    if HealthChartView is not None:
                        chart_view = HealthChartView()
                        chart_view.chart_type = "bar"
                        if hasattr(chart_view, 'toggle_button') and chart_view.toggle_button:
                            chart_view.toggle_button.children[0].text = "切换为折线图"
                        
                        # 更新图表数据
                        chart_view.update_chart(chart_data)
                        chart_container.add_widget(chart_view)
                
                if chart_view is not None:
                    chart_view.height = int(dp(300))
                    chart_view.size_hint_y = None
                    chart_container.height = dp(350)
            else:
                # 使用简化版图表显示
                self._show_simple_chart_generic(chart_container, panel_key, records, chart_title, y_fields)
        except Exception as e:
            logger.error(f"更新{chart_title}图表失败: {e}")
    
    def _show_simple_chart_generic(self, chart_container, panel_key: str, records: List[Dict], 
                                  chart_title: str, y_fields: List[tuple]):
        """通用简化版图表显示函数"""
        try:
            # 清空现有内容
            chart_container.clear_widgets()
            
            # 根据面板类型确定最小记录数要求
            min_records = 2 if panel_key == "blood_pressure_panel" else 1
            
            if len(records) < min_records:
                info_container = MDBoxLayout(
                    orientation="vertical",
                    padding=[dp(10), dp(10), dp(10), dp(10)],
                    spacing=dp(5)
                )

                info_label = MDLabel(
                    text=chart_title,
                    theme_text_color="Primary",
                    halign="center",
                    font_style="Title",
                    font_size=dp(14),
                    size_hint_y=None,
                    height=dp(24)
                )

                hint_label = MDLabel(
                    text=f"需要至少{min_records}条记录才能显示趋势图",
                    theme_text_color="Secondary",
                    halign="center",
                    font_style="Body",
                    font_size=dp(14)
                )

                info_container.add_widget(info_label)
                info_container.add_widget(hint_label)
                chart_container.add_widget(info_container)
                chart_container.height = dp(100)
            else:
                # 创建图表主容器
                main_chart_layout = MDBoxLayout(
                    orientation="vertical",
                    padding=[dp(8), dp(8), dp(8), dp(8)],
                    spacing=dp(6)
                )

                # 创建图表标题
                chart_title_widget = MDLabel(
                    text=chart_title,
                    theme_text_color="Primary",
                    halign="center",
                    font_style="Title",
                    font_size=dp(14),
                    size_hint_y=None,
                    height=dp(24)
                )
                main_chart_layout.add_widget(chart_title_widget)

                # 创建图例区域
                legend_container = MDBoxLayout(
                    orientation="horizontal",
                    size_hint_y=None,
                    height=dp(24),
                    spacing=dp(15),
                    padding=[dp(5), dp(0), dp(5), dp(0)]
                )

                # 为每个字段创建图例
                colors = [
                    [1, 0, 0, 1],    # 红色
                    [0, 0, 1, 1],    # 蓝色
                    [0, 0.4, 0, 1],  # 深绿色
                    [1, 0.6, 0, 1],  # 橙色
                ]
                
                # 单位映射
                unit_mapping = {
                    "收缩压": "mmHg",
                    "舒张压": "mmHg",
                    "心率": "次/分",
                    "血糖值": "mmol/L"
                }
                
                for i, (display_name, _) in enumerate(y_fields):
                    legend_box = MDBoxLayout(
                        orientation="horizontal",
                        size_hint_x=None,
                        width=dp(80)
                    )
                    color_box = MDBoxLayout(
                        size_hint_x=None,
                        width=dp(16),
                        height=dp(16),
                        md_bg_color=colors[i % len(colors)]
                    )
                    # 使用预定义的单位映射
                    unit = unit_mapping.get(display_name, "")
                    label_text = f"{display_name} ({unit})" if unit else display_name
                    label = MDLabel(
                        text=label_text,
                        theme_text_color="Primary",
                        font_size=dp(14),
                        halign="left"
                    )
                    legend_box.add_widget(color_box)
                    legend_box.add_widget(label)
                    legend_container.add_widget(legend_box)

                legend_container.add_widget(MDBoxLayout())  # 弹性空间

                main_chart_layout.add_widget(legend_container)

                chart_container.add_widget(main_chart_layout)
                chart_container.height = dp(100)
        except Exception as e:
            logger.error(f"显示简化版{chart_title}图表失败: {e}")
    
    def load_condition_data(self, content_data: dict):
        """加载身体状况数据"""
        try:
            condition = content_data.get('condition', '')
            if 'condition_field' in self.widgets:
                self.widgets['condition_field'].text = condition
                # 在只读模式下，确保字段保持只读状态
                if getattr(self.screen, '_readonly_mode', False):
                    if hasattr(self.widgets['condition_field'], 'readonly'):
                        self.widgets['condition_field'].readonly = True
                    if hasattr(self.widgets['condition_field'], 'disabled'):
                        self.widgets['condition_field'].disabled = True
        except Exception as e:
            logger.error(f"加载身体状况数据失败: {e}")
    
    def load_weight_data(self, content_data: dict):
        """加载体重数据"""
        try:
            weight = content_data.get('weight', '')
            weight_status = content_data.get('weight_status', '')
            
            if 'weight_field' in self.widgets:
                self.widgets['weight_field'].text = weight
                # 在只读模式下，确保字段保持只读状态
                if getattr(self.screen, '_readonly_mode', False):
                    if hasattr(self.widgets['weight_field'], 'readonly'):
                        self.widgets['weight_field'].readonly = True
                    if hasattr(self.widgets['weight_field'], 'disabled'):
                        self.widgets['weight_field'].disabled = True
            
            if 'weight_status_label' in self.widgets:
                self.widgets['weight_status_label'].text = weight_status if weight_status else "状态"
        except Exception as e:
            logger.error(f"加载体重数据失败: {e}")
    
    def load_blood_pressure_data(self, content_data: dict):
        """加载血压数据"""
        try:
            # 清空现有记录
            self.screen.blood_pressure_records = []
            
            blood_pressure_data = content_data.get('blood_pressure', [])
            if blood_pressure_data:
                # 加载血压记录
                for record in blood_pressure_data:
                    if isinstance(record, dict) and 'systolic' in record and 'diastolic' in record:
                        self.screen.blood_pressure_records.append(record)
                
                # 根据时间排序所有记录
                self.screen.blood_pressure_records.sort(key=lambda x: x.get('time', ''))
            
            # 更新血压图表显示（无论是否有数据都需要更新）
            self.update_blood_pressure_chart()
        except Exception as e:
            logger.error(f"加载血压数据失败: {e}")
    
    def load_blood_sugar_data(self, content_data: dict):
        """加载血糖数据"""
        try:
            # 清空现有记录
            self.screen.blood_sugar_records = []
            
            blood_sugar_data = content_data.get('blood_sugar', [])
            if blood_sugar_data:
                # 加载血糖记录
                for record in blood_sugar_data:
                    if isinstance(record, dict) and 'value' in record and 'time' in record:
                        self.screen.blood_sugar_records.append(record)
                
                # 根据时间排序所有记录
                self.screen.blood_sugar_records.sort(key=lambda x: x.get('time', ''))
            
            # 更新血糖图表显示（无论是否有数据都需要更新）
            self.update_blood_sugar_chart()
        except Exception as e:
            logger.error(f"加载血糖数据失败: {e}")
    
    def load_exercise_data(self, content_data: dict):
        """加载运动数据"""
        try:
            exercise = content_data.get('exercise', '')
            if 'exercise_field' in self.widgets:
                self.widgets['exercise_field'].text = exercise
                # 在只读模式下，确保字段保持只读状态
                if getattr(self.screen, '_readonly_mode', False):
                    if hasattr(self.widgets['exercise_field'], 'readonly'):
                        self.widgets['exercise_field'].readonly = True
                    if hasattr(self.widgets['exercise_field'], 'disabled'):
                        self.widgets['exercise_field'].disabled = True
        except Exception as e:
            logger.error(f"加载运动数据失败: {e}")
    
    def load_sleep_data(self, content_data: dict):
        """加载睡眠数据"""
        try:
            sleep = content_data.get('sleep', '')
            if 'sleep_field' in self.widgets:
                self.widgets['sleep_field'].text = sleep
                # 在只读模式下，确保字段保持只读状态
                if getattr(self.screen, '_readonly_mode', False):
                    if hasattr(self.widgets['sleep_field'], 'readonly'):
                        self.widgets['sleep_field'].readonly = True
                    if hasattr(self.widgets['sleep_field'], 'disabled'):
                        self.widgets['sleep_field'].disabled = True
        except Exception as e:
            logger.error(f"加载睡眠数据失败: {e}")
    
    def clear_all_data(self, clear_records=True):
        """清空所有输入数据，用于新建日记
        
        Args:
            clear_records: 是否清空血压和血糖记录数据
        """
        try:
            logger.info("开始清空所有数据")
            logger.debug(f"Widgets dict keys before clearing: {list(self.widgets.keys())}")
            
            # 只有在非编辑模式下才重置日期为今天
            # 在编辑模式下保留原有日期
            if not getattr(self.screen, '_is_edit_mode', False):
                self.screen.selected_date = date.today().strftime("%Y-%m-%d")
                if 'date_field' in self.widgets:
                    self.widgets['date_field'].text = self.screen.selected_date
                else:
                    logger.warning(f"date_field not found in widgets dict during clear_all_data. Available keys: {list(self.widgets.keys())}")
            else:
                # 在编辑模式下，保持原有的日期设置
                logger.debug("编辑模式下保留原有日期设置")
                if 'date_field' in self.widgets:
                    self.widgets['date_field'].text = self.screen.selected_date
                else:
                    logger.warning(f"date_field not found in widgets dict during clear_all_data. Available keys: {list(self.widgets.keys())}")
            
            # 清空文本输入框
            text_fields = ['condition_field', 'weight_field', 'exercise_field', 'sleep_field']
            for field_id in text_fields:
                if field_id in self.widgets:
                    self.widgets[field_id].text = ""
                    # 只有在非只读模式下才重置只读状态
                    if not getattr(self.screen, '_readonly_mode', False):
                        # 如果是文本输入框，还需要重置其只读状态
                        if hasattr(self.widgets[field_id], 'readonly'):
                            self.widgets[field_id].readonly = False
                        if hasattr(self.widgets[field_id], 'disabled'):
                            self.widgets[field_id].disabled = False
            
            # 重置体重状态标签
            if 'weight_status_label' in self.widgets:
                self.widgets['weight_status_label'].text = "状态"
            
            # 清空记录数据（可选）
            if clear_records:
                self.screen.blood_pressure_records = []
                self.screen.blood_sugar_records = []
                
                # 更新血压图表显示（清空状态）
                self.update_blood_pressure_chart()
                
                # 更新血糖图表显示（清空状态）
                self.update_blood_sugar_chart()
            # 即使不清空记录数据，也要更新图表显示以确保正确刷新
            elif hasattr(self.screen, 'blood_pressure_records') or hasattr(self.screen, 'blood_sugar_records'):
                # 更新血压图表显示
                self.update_blood_pressure_chart()
                
                # 更新血糖图表显示
                self.update_blood_sugar_chart()
            
            logger.info("所有数据已清空")
        except Exception as e:
            logger.error(f"清空数据失败: {e}")
    
    def collect_diary_data(self) -> dict:
        """收集健康日记数据
        
        Returns:
            dict: 健康日记数据
        """
        try:
            data = {
                'date': self.screen.selected_date,
                'condition': '',
                'weight': '',
                'weight_status': '',
                'blood_pressure': self.screen.blood_pressure_records[:],  # 直接使用记录列表
                'blood_sugar': self.screen.blood_sugar_records[:],       # 直接使用记录列表
                'exercise': '',
                'sleep': ''
            }
            
            # 收集文本字段数据
            text_fields = {
                'condition': 'condition_field',
                'weight': 'weight_field',
                'exercise': 'exercise_field',
                'sleep': 'sleep_field'
            }
            
            for key, field_id in text_fields.items():
                if field_id in self.widgets and hasattr(self.widgets[field_id], 'text'):
                    data[key] = self.widgets[field_id].text.strip()
            
            # 获取体重状态下拉菜单的值
            if 'weight_status_label' in self.widgets:
                data['weight_status'] = self.widgets['weight_status_label'].text.strip()
            
            # 过滤空记录
            data['blood_pressure'] = [record for record in data['blood_pressure'] if record.get('systolic') and record.get('diastolic')]
            data['blood_sugar'] = [record for record in data['blood_sugar'] if record.get('value') and record.get('time')]
            
            logger.debug(f"收集到的数据: {data}")
            return data
        except Exception as e:
            logger.error(f"收集日记数据失败: {e}")
            return {}
    
    def disable_all_inputs(self):
        """禁用所有输入控件"""
        try:
            # 禁用所有文本输入框
            text_fields = ['condition_field', 'weight_field', 'exercise_field', 'sleep_field']
            for field_id in text_fields:
                if field_id in self.widgets:
                    widget = self.widgets[field_id]
                    if hasattr(widget, 'readonly'):
                        widget.readonly = True
                    if hasattr(widget, 'disabled'):
                        widget.disabled = True
                    logger.debug(f"禁用输入控件: {field_id}")
            
            # 禁用所有按钮
            button_fields = ['blood_pressure_panel_add_btn', 'blood_sugar_panel_add_btn']
            for field_id in button_fields:
                if field_id in self.widgets:
                    widget = self.widgets[field_id]
                    if hasattr(widget, 'disabled'):
                        widget.disabled = True
                    logger.debug(f"禁用按钮: {field_id}")
            
            # 禁用日期选择器
            if 'date_field' in self.widgets:
                date_field = self.widgets['date_field']
                if hasattr(date_field, 'readonly'):
                    date_field.readonly = True
                if hasattr(date_field, 'disabled'):
                    date_field.disabled = True
                logger.debug("禁用日期选择器")
                
            if 'date_picker_btn' in self.widgets:
                date_btn = self.widgets['date_picker_btn']
                if hasattr(date_btn, 'disabled'):
                    date_btn.disabled = True
                logger.debug("禁用日期选择按钮")
                
            logger.info("所有输入控件已禁用")
        except Exception as e:
            logger.error(f"禁用输入控件失败: {e}")
    
    def enable_all_inputs(self):
        """启用所有输入控件"""
        try:
            # 启用所有文本输入框
            text_fields = ['condition_field', 'weight_field', 'exercise_field', 'sleep_field']
            for field_id in text_fields:
                if field_id in self.widgets:
                    widget = self.widgets[field_id]
                    if hasattr(widget, 'readonly'):
                        widget.readonly = False
                    if hasattr(widget, 'disabled'):
                        widget.disabled = False
                    logger.debug(f"启用输入控件: {field_id}")
            
            # 启用所有按钮
            button_fields = ['blood_pressure_panel_add_btn', 'blood_sugar_panel_add_btn']
            for field_id in button_fields:
                if field_id in self.widgets:
                    widget = self.widgets[field_id]
                    if hasattr(widget, 'disabled'):
                        widget.disabled = False
                    logger.debug(f"启用按钮: {field_id}")
            
            # 启用日期选择器
            if 'date_field' in self.widgets:
                date_field = self.widgets['date_field']
                if hasattr(date_field, 'readonly'):
                    date_field.readonly = False
                if hasattr(date_field, 'disabled'):
                    date_field.disabled = False
                logger.debug("启用日期选择器")
                
            if 'date_picker_btn' in self.widgets:
                date_btn = self.widgets['date_picker_btn']
                if hasattr(date_btn, 'disabled'):
                    date_btn.disabled = False
                logger.debug("启用日期选择按钮")
                
            logger.info("所有输入控件已启用")
        except Exception as e:
            logger.error(f"启用输入控件失败: {e}")
    
    def bind_panel_events(self):
        """绑定面板事件"""
        try:
            # 绑定面板事件
            panel_keys = ["date", "condition", "blood_pressure", "blood_sugar", "weight", "exercise", "sleep"]
            for key in panel_keys:
                panel_id = f"{key}_panel"
                if panel_id in self.widgets:
                    panel = self.widgets[panel_id]
                    if hasattr(panel, 'screen_ref'):
                        panel.screen_ref = self.screen
                    if hasattr(panel, 'panel_id'):
                        panel.panel_id = key
                    logger.debug(f"面板 {panel_id} 事件绑定完成")
            logger.info("所有面板事件绑定完成")
        except Exception as e:
            logger.error(f"绑定面板事件失败: {e}")
    
    def _create_action_panel(self):
        """创建操作按钮区域"""
        # 不再创建底部操作按钮区域，因为使用顶端导航栏
        # 返回一个空的容器
        box = MDBoxLayout(
            orientation="horizontal",
            adaptive_height=True,
            spacing=dp(16),  # 增加间距
            padding=[dp(20), dp(24), dp(20), dp(24)],  # 增加内边距
            size_hint_x=1   # 自适应宽度
        )
        # 不添加任何按钮
        return box
