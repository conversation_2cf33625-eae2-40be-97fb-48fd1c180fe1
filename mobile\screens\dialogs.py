from kivy.metrics import dp, sp
from kivy.core.window import Window
from kivy.clock import Clock
from kivy.uix.widget import Widget
from kivymd.uix.dialog import (
    MDDialog,
    MDDialogHeadlineText,
    MDDialogSupportingText,
    MDDialogButtonContainer,
    MDDialogContentContainer,
)
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.textfield import MDTextField, MDTextFieldHelperText, MDTextFieldHintText, MDTextFieldMaxLengthText
from kivymd.uix.button import MDButton, MDButtonText, MDIconButton
from kivymd.uix.label import MDLabel
from kivymd.uix.menu import MDDropdownMenu
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试从 theme.py 读取 DIMENSIONS
try:
    from mobile.theme import ThemeManager
    dims = ThemeManager.get_app_theme().DIMENSIONS
except ImportError:
    # 如果无法从mobile包导入，则尝试直接导入
    try:
        from theme import ThemeManager
        dims = ThemeManager.get_app_theme().DIMENSIONS
    except ImportError:
        # 如果两个都失败，使用默认配置
        dims = {
            'text_field_dialog_height': dp(56),
            'med_dialog_expanded_height': dp(500),
            'button_height': dp(52),
            'dialog_padding': dp(16),
            'dialog_spacing': dp(12),
            'field_spacing_medium': dp(8),
            'box_padding': dp(16)
        }


def _calc_dialog_height(target_height: int = 600, max_ratio: float = 0.8, min_height: int = 400) -> int:
    """计算对话框目标高度 - 优化KivyMD 2.0.1 dev0显示，支持Window.height为0的兜底处理
    
    Args:
        target_height: 目标高度
        max_ratio: 最大屏幕高度比例
        min_height: 最小高度
        
    Returns:
        计算后的对话框高度
    """
    try:
        # 获取配置的对话框高度，如果没有则使用默认值
        dialog_target_h = dims.get('med_dialog_expanded_height', dp(target_height))
        # 如果Window.height为0或无效，使用默认高度作为兜底
        if Window.height <= 0:
            max_height = dp(target_height)
        else:
            # 确保对话框高度不超过屏幕高度的指定比例
            max_height = int(Window.height * max_ratio)
        calculated_height = min(max_height, int(dialog_target_h))
        return int(max(calculated_height, dp(min_height)))
    except Exception:
        # 异常情况下返回安全的默认高度
        return int(dp(target_height))

def _calc_blood_pressure_dialog_height() -> int:
    """计算血压对话框目标高度 - 适合血压记录表单的紧凑高度"""
    return _calc_dialog_height(target_height=320, max_ratio=0.6, min_height=280)

class FormFieldConfigs:
    """
    通用表单字段配置类
    支持所有表单类型的字段定义和验证规则
    """

    # 字段配置
    FIELD_CONFIGS = {
        # 药物字段
        'name': {
            'label': '药物名称',
            'hint_text': '请输入药物名称',
            'required': True,
            'max_length': 100,
            'helper_text': '必填项',
            'icon': 'pill',
            'type': 'text'
        },
        'dosage': {
            'label': '剂量',
            'hint_text': '如：100mg, 2片',
            'required': True,
            'max_length': 50,
            'helper_text': '请输入具体剂量',
            'icon': 'scale-balance',
            'type': 'text'
        },
        'frequency': {
            'label': '用药频次',
            'hint_text': '选择用药频次',
            'required': True,
            'options': [
                '一天一次',
                '一天两次',
                '一天三次',
                '一天四次',
                '每12小时一次',
                '每8小时一次',
                '每6小时一次',
                '每4小时一次',
                '按需服用',
                '其他'
            ],
            'icon': 'clock-outline',
            'type': 'dropdown'
        },
        'start_date': {
            'label': '开始日期',
            'hint_text': '选择开始用药日期',
            'required': True,
            'format': '%Y-%m-%d',
            'icon': 'calendar-start',
            'type': 'date'
        },
        'end_date': {
            'label': '结束日期',
            'hint_text': '选择结束用药日期（可选）',
            'required': False,
            'format': '%Y-%m-%d',
            'icon': 'calendar-end',
            'type': 'date'
        },
        'reason': {
            'label': '用药原因',
            'hint_text': '请输入用药原因或适应症',
            'required': False,
            'max_length': 200,
            'multiline': True,
            'icon': 'medical-bag',
            'helper_text': '如：高血压、糖尿病、高血脂等',
            'options': [
                '高血压',
                '糖尿病',
                '高血脂',
                '心脏病',
                '感冒发烧',
                '头痛',
                '胃痛',
                '失眠',
                '过敏',
                '其他'
            ],
            'allow_custom_input': True,
            'type': 'combo'
        },
        'notes': {
            'label': '注意事项',
            'hint_text': '请输入用药注意事项',
            'required': False,
            'max_length': 500,
            'multiline': True,
            'icon': 'note-text-outline',
            'type': 'text'
        },
        'instructions': {
            'label': '服用说明',
            'hint_text': '如：饭前服用、饭后服用等',
            'required': False,
            'max_length': 200,
            'multiline': True,
            'icon': 'information-outline',
            'helper_text': '如：空腹服用、餐前服用、餐后服用等',
            'options': [
                '空腹服用',
                '餐前服用',
                '餐后服用',
                '睡前服用',
                '随餐服用',
                '饭前30分钟',
                '饭后1小时',
                '每日早晨',
                '每日晚上',
                '其他'
            ],
            'allow_custom_input': True,
            'type': 'combo'
        },
        'type': {
            'label': '药物类型',
            'hint_text': '选择药物类型',
            'required': False,
            'options': [
                '处方药',
                '非处方药',
                '中药',
                '保健品',
                '维生素',
                '其他'
            ],
            'icon': 'pill',
            'type': 'dropdown'
        },
        'stop_reason': {
            'label': '停药原因',
            'hint_text': '请输入停药原因',
            'required': True,
            'max_length': 200,
            'multiline': True,
            'options': [
                '治疗完成',
                '不良反应',
                '无效果',
                '医生建议',
                '个人原因',
                '其他'
            ],
            'icon': 'stop-circle-outline',
            'type': 'combo'
        },
        # 血压字段
        'systolic': {
            'label': '收缩压',
            'hint_text': '收缩压 (mmHg)',
            'required': True,
            'input_filter': 'int',
            'min': 50,
            'max': 300,
            'unit': 'mmHg',
            'type': 'numeric'
        },
        'diastolic': {
            'label': '舒张压',
            'hint_text': '舒张压 (mmHg)',
            'required': True,
            'input_filter': 'int',
            'min': 30,
            'max': 200,
            'unit': 'mmHg',
            'type': 'numeric'
        },
        'heart_rate': {
            'label': '心率',
            'hint_text': '心率 (次/分)',
            'required': True,
            'input_filter': 'int',
            'min': 30,
            'max': 200,
            'unit': '次/分',
            'type': 'numeric'
        },
        # 血糖字段
        'value': {
            'label': '血糖值',
            'hint_text': '血糖值 (mmol/L)',
            'required': True,
            'input_filter': 'float',
            'min': 0,
            'max': 30,
            'unit': 'mmol/L',
            'type': 'numeric'
        },
        # 通用时间字段
        'time': {
            'label': '测量时间',
            'hint_text': '测量时间',
            'required': True,
            'format': '%H:%M',
            'type': 'time'
        },
        # 提醒时间字段
        'reminder_time': {
            'label': '提醒时间',
            'hint_text': '提醒时间',
            'required': True,
            'format': '%H:%M',
            'type': 'time'
        },
        # 停药日期
        'stop_date': {
            'label': '停药日期',
            'hint_text': '选择停药日期',
            'required': True,
            'format': '%Y-%m-%d',
            'type': 'date'
        },
    }

    @classmethod
    def get_field_config(cls, field_name: str) -> Dict[str, Any]:
        """
        获取字段配置

        Args:
            field_name: 字段名称

        Returns:
            字段配置字典
        """
        return cls.FIELD_CONFIGS.get(field_name, {})


    
    @classmethod
    def validate_field(cls, field_name: str, value: Any) -> tuple[bool, str]:
        """
        验证单个字段

        Args:
            field_name: 字段名称
            value: 字段值

        Returns:
            (是否有效, 错误信息)
        """
        config = cls.get_field_config(field_name)
        if not config:
            return True, ""

        # 特殊处理开始日期：如果为空且是必填字段，设置默认值为当前日期
        if field_name in ['start_date', 'stop_date'] and config.get('required', False) and (not value or str(value).strip() == ""):
            # 返回当前日期作为默认值，不报错
            return True, ""

        # 检查必填字段
        if config.get('required', False) and (not value or str(value).strip() == ""):
            return False, f"{config.get('label', field_name)}不能为空"

        # 检查最大长度
        if value and config.get('max_length'):
            if len(str(value)) > config['max_length']:
                return False, f"{config.get('label', field_name)}长度不能超过{config['max_length']}个字符"

        # 检查日期格式 - 支持多种日期格式并统一为YYYY-MM-DD
        if value and config.get('format') == '%Y-%m-%d':
            try:
                value_str = str(value).strip()
                # 如果值为空，跳过验证
                if not value_str:
                    return True, ""
                    
                # 尝试多种日期格式
                date_formats = [
                    '%Y-%m-%d',           # 2024-01-01
                    '%Y-%m-%d %H:%M:%S',  # 2024-01-01 12:00:00
                    '%Y-%m-%d %H:%M:%S.%f', # 2024-01-01 12:00:00.000000
                    '%Y/%m/%d',           # 2024/01/01
                    '%m/%d/%Y',           # 01/01/2024
                ]
                
                parsed_date = None
                for fmt in date_formats:
                    try:
                        parsed_date = datetime.strptime(value_str, fmt)
                        break
                    except ValueError:
                        continue
                
                if parsed_date is None:
                    return False, f"{config.get('label', field_name)}日期格式不正确"
                    
            except Exception:
                return False, f"{config.get('label', field_name)}日期格式不正确"

        # 检查选项值（支持自定义输入）
        if value and config.get('options'):
            # 如果不允许自定义输入且值不在选项中，则验证失败
            if not config.get('allow_custom_input', False) and str(value) not in config['options']:
                return False, f"{config.get('label', field_name)}选项无效"

        # 检查数值范围
        if value and 'min' in config and 'max' in config:
            try:
                val = float(value) if config.get('input_filter') == 'float' else int(value)
                if val < config['min'] or val > config['max']:
                    return False, f"{config.get('label', field_name)}应在{config['min']}-{config['max']}之间"
            except ValueError:
                return False, f"{config.get('label', field_name)}必须是数字"

        # 检查时间格式
        if value and config.get('format') == '%H:%M':
            if not cls._is_valid_time_format(value):
                return False, f"{config.get('label', field_name)}时间格式不正确"

        return True, ""

    @classmethod
    def validate_form(cls, form_data: Dict[str, Any], fields: List[str]) -> tuple[bool, List[str]]:
        """
        验证整个表单

        Args:
            form_data: 表单数据
            fields: 需要验证的字段列表

        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []

        # 确定要验证的字段
        fields_to_validate = fields

        for field_name in fields_to_validate:
            value = form_data.get(field_name)
            is_valid, error_msg = cls.validate_field(field_name, value)
            if not is_valid:
                errors.append(error_msg)

        # 检查日期逻辑
        start_date = form_data.get('start_date')
        end_date = form_data.get('end_date')

        if start_date and end_date:
            try:
                start_dt = cls.parse_date(start_date)
                end_dt = cls.parse_date(end_date)
                if end_dt < start_dt:
                    errors.append("结束日期不能早于开始日期")
            except ValueError:
                pass  # 日期格式错误已在字段验证中处理

        # 额外逻辑验证（如血压比较）
        # 只有当字段在验证列表中时才进行血压比较
        if 'systolic' in fields_to_validate and 'diastolic' in fields_to_validate:
            systolic = form_data.get('systolic')
            diastolic = form_data.get('diastolic')
            
            if systolic and diastolic:
                try:
                    if int(systolic) <= int(diastolic):
                        errors.append("收缩压应大于舒张压")
                except ValueError:
                    pass  # 已在字段验证中处理

        return len(errors) == 0, errors

    @classmethod
    def parse_date(cls, date_str) -> datetime:
        """
        解析日期字符串为datetime对象
        
        Args:
            date_str: 日期字符串
            
        Returns:
            datetime对象
            
        Raises:
            ValueError: 如果无法解析日期字符串
        """
        # 确保输入是字符串
        if not isinstance(date_str, str):
            date_str = str(date_str)
        
        # 尝试多种日期格式
        date_formats = [
            '%Y-%m-%d',           # 2024-01-01
            '%Y-%m-%d %H:%M:%S',  # 2024-01-01 12:00:00
            '%Y-%m-%d %H:%M:%S.%f', # 2024-01-01 12:00:00.000000
            '%Y/%m/%d',           # 2024/01/01
            '%m/%d/%Y',           # 01/01/2024
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        
        # 如果所有格式都失败，抛出异常
        raise ValueError(f"无法解析日期字符串: {date_str}")

    @classmethod
    def _is_valid_time_format(cls, time_str):
        """验证时间格式是否正确"""
        try:
            if not time_str or len(time_str) != 5 or ':' not in time_str:
                return False
            parts = time_str.split(':')
            if len(parts) != 2:
                return False
            hour, minute = parts
            if not (hour.isdigit() and minute.isdigit()):
                return False
            h, m = int(hour), int(minute)
            return 0 <= h <= 23 and 0 <= m <= 59
        except Exception:
            return False

class BaseFormContent(MDBoxLayout):
    """
    通用表单内容基类
    支持配置驱动的字段创建
    """

    def __init__(self, fields: List[str], initial_data: Optional[Dict[str, Any]] = None, **kwargs):
        """
        初始化表单内容

        Args:
            fields: 要显示的字段列表
            initial_data: 初始数据
        """
        super().__init__(**kwargs)

        self.orientation = 'vertical'
        self.spacing = dp(8)  # 减少间距，从16改为8
        self.adaptive_height = True
        self.padding = [dp(24), dp(16)]

        self.fields = fields
        self.initial_data = initial_data or {}
        self.field_widgets = {}  # 存储字段组件的引用
        self.dropdown_menus = {}  # 存储下拉菜单的引用

        self._create_form_fields()
        
        # 设置初始数据
        if self.initial_data:
            self.set_form_data(self.initial_data)

    def _create_form_fields(self):
        """创建表单字段（为每个字段包装一个统一的容器，保证垂直间距一致）"""
        for field_name in self.fields:
            config = FormFieldConfigs.get_field_config(field_name)
            if not config:
                logger.warning(f"未找到字段配置: {field_name}")
                continue

            field_widget = self._create_field_widget(field_name, config)
            if not field_widget:
                continue

            # wrapper 保证每个字段上下留白一致（**必须在循环内部**）
            wrapper_height = (getattr(field_widget, 'height', None) or dp(56)) + dp(12)
            wrapper = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=wrapper_height,
                padding=[0, dp(6), 0, dp(6)]
            )
            wrapper.add_widget(field_widget)
            self.add_widget(wrapper)

            # 注册或查找实际输入控件，便于后续 get_form_data 使用
            if field_name not in self.field_widgets:
                input_widget = None
                if hasattr(field_widget, 'text'):
                    input_widget = field_widget
                else:
                    for child in getattr(field_widget, 'children', []):
                        if hasattr(child, 'text'):
                            input_widget = child
                            break
                self.field_widgets[field_name] = input_widget or field_widget

    def _create_field_widget(self, field_name: str, config: Dict[str, Any]):
        """
        根据配置创建字段组件

        Args:
            field_name: 字段名称
            config: 字段配置

        Returns:
            字段组件
        """
        try:
            # 根据字段类型创建相应组件
            field_type = config.get('type')
            if field_type == 'combo':
                # 组合输入字段
                return self._create_combo_field(field_name, config)
            elif field_type == 'dropdown':
                # 纯下拉选择字段
                return self._create_dropdown_field(field_name, config)
            elif field_type == 'date':
                # 日期字段
                return self._create_date_field(field_name, config)
            elif field_type == 'time':
                # 时间字段
                return self._create_time_field(field_name, config)
            elif field_type == 'numeric':
                # 数值字段
                return self._create_numeric_field(field_name, config)
            else:
                # 文本字段（默认）
                return self._create_text_field(field_name, config)
        except Exception as e:
            logger.error(f"创建字段组件失败 {field_name}: {e}")
            return None

    def _create_text_field(self, field_name: str, config: Dict[str, Any]) -> MDTextField:
        """
        创建文本输入字段

        Args:
            field_name: 字段名称
            config: 字段配置

        Returns:
            文本字段组件
        """
        # 获取初始值
        initial_value = self.initial_data.get(field_name, "")

        # 统一字段高度，多行字段稍高一些但保持视觉一致性
        field_height = dp(72) if config.get('multiline') else dp(56)

        # 创建文本字段
        text_field = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=field_height,
            multiline=config.get('multiline', False),
            text=str(initial_value) if initial_value else "",
            input_filter=config.get('input_filter')
        )

        # 添加最大长度限制（如果需要）
        if config.get('max_length', 0) > 0:
            text_field.add_widget(
                MDTextFieldMaxLengthText(
                    max_text_length=config['max_length']
                )
            )

        # 添加提示文本
        if config.get('hint_text'):
            text_field.add_widget(
                MDTextFieldHintText(text=config['hint_text'])
            )

        # 添加帮助文本
        if config.get('helper_text'):
            text_field.add_widget(
                MDTextFieldHelperText(
                    text=config['helper_text'],
                    mode="on_focus"
                )
            )

        return text_field

    def _create_dropdown_field(self, field_name: str, config: Dict[str, Any]):
        """
        创建下拉选择字段

        Args:
            field_name: 字段名称
            config: 字段配置

        Returns:
            下拉字段容器组件
        """
        # 获取初始值
        initial_value = self.initial_data.get(field_name, "")

        # 创建容器
        container = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(56),
            spacing=dp(8)
        )

        # 创建文本字段
        text_field = MDTextField(
            mode="outlined",
            size_hint_x=0.85,
            readonly=True,
            text=str(initial_value) if initial_value else ""
        )

        # 添加提示文本
        if config.get('hint_text'):
            text_field.add_widget(
                MDTextFieldHintText(text=config['hint_text'])
            )

        # 添加帮助文本
        if config.get('helper_text'):
            text_field.add_widget(
                MDTextFieldHelperText(
                    text=config['helper_text'],
                    mode="on_focus"
                )
            )

        # 创建下拉按钮
        dropdown_button = MDButton(
            MDButtonText(text="▼"),
            style="outlined",
            size_hint=(None, None),
            size=(dp(48), dp(48)),
            on_release=lambda x: self._open_dropdown_menu(field_name, text_field, config['options'])
        )

        container.add_widget(text_field)
        container.add_widget(dropdown_button)

        # 存储文本字段引用
        self.field_widgets[field_name] = text_field

        return container

    def _create_combo_field(self, field_name: str, config: Dict[str, Any]):
        """
        创建组合输入字段（既有下拉菜单又能手动输入）

        Args:
            field_name: 字段名称
            config: 字段配置

        Returns:
            组合字段容器组件
        """
        # 获取初始值
        initial_value = self.initial_data.get(field_name, "")

        # 统一容器高度，多行字段稍高一些但保持视觉一致性
        container_height = dp(72) if config.get('multiline', False) else dp(56)

        # 创建容器
        container = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=container_height,
            spacing=dp(8)
        )

        # 创建可编辑文本字段
        text_field = MDTextField(
            mode="outlined",
            size_hint_x=0.85,
            readonly=False,  # 允许手动输入
            text=str(initial_value) if initial_value else "",
            input_filter=config.get('input_filter')
        )

        # 设置多行属性
        if config.get('multiline', False):
            text_field.multiline = True
            # 为多行文本添加最大长度限制
            max_length = config.get('max_length', 200)
            if max_length > 0:
                text_field.add_widget(
                    MDTextFieldMaxLengthText(
                        max_text_length=max_length
                    )
                )

        # 添加提示文本
        if config.get('hint_text'):
            text_field.add_widget(
                MDTextFieldHintText(text=config['hint_text'])
            )

        # 添加帮助文本
        if config.get('helper_text'):
            text_field.add_widget(
                MDTextFieldHelperText(
                    text=config['helper_text'],
                    mode="on_focus"
                )
            )

        # 创建下拉按钮
        dropdown_button = MDButton(
            MDButtonText(text="▼"),
            style="outlined",
            size_hint=(None, None),
            size=(dp(48), dp(48)),
            on_release=lambda x: self._open_dropdown_menu(field_name, text_field, config['options'])
        )

        container.add_widget(text_field)
        container.add_widget(dropdown_button)

        # 存储文本字段引用
        self.field_widgets[field_name] = text_field

        return container

    def _create_date_field(self, field_name: str, config: Dict[str, Any]) -> MDTextField:
        """
        创建日期选择字段

        Args:
            field_name: 字段名称
            config: 字段配置

        Returns:
            日期字段组件
        """
        # 获取初始值，确保开始日期有默认值
        initial_value = self.initial_data.get(field_name, "")
        if not initial_value and field_name in ['start_date', 'stop_date']:
            initial_value = datetime.now().strftime('%Y-%m-%d')
            # 同时更新initial_data以确保数据一致性
            self.initial_data[field_name] = initial_value

        # 创建容器
        container = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(56),
            spacing=dp(8)
        )

        # 创建文本字段
        text_field = MDTextField(
            mode="outlined",
            size_hint_x=0.85,
            readonly=True,
            text=str(initial_value) if initial_value else ""
        )

        # 添加提示文本
        if config.get('hint_text'):
            text_field.add_widget(
                MDTextFieldHintText(text=config['hint_text'])
            )

        # 创建日期选择按钮
        date_button = MDButton(
            MDButtonText(text="📅"),
            style="outlined",
            size_hint=(None, None),
            size=(dp(48), dp(48)),
            on_release=lambda x: self._open_date_picker(field_name, text_field)
        )

        container.add_widget(text_field)
        container.add_widget(date_button)

        # 存储文本字段引用
        self.field_widgets[field_name] = text_field

        return container

    def _create_time_field(self, field_name: str, config: Dict[str, Any]):
        """
        创建时间选择字段

        Args:
            field_name: 字段名称
            config: 字段配置

        Returns:
            时间字段组件
        """
        # 获取初始值
        initial_value = self.initial_data.get(field_name, datetime.now().strftime("%H:%M"))

        # 创建容器
        container = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(56),
            spacing=dp(8)
        )

        # 创建文本字段
        text_field = MDTextField(
            mode="outlined",
            size_hint_x=0.7,
            readonly=True,
            text=str(initial_value) if initial_value else ""
        )

        # 添加提示文本
        if config.get('hint_text'):
            text_field.add_widget(
                MDTextFieldHintText(text=config['hint_text'])
            )

        # 创建时间选择按钮
        time_button = MDButton(
            MDButtonText(text="选择时间"),
            style="outlined",
            size_hint_x=0.3,
            on_release=lambda x: self._open_time_picker(text_field)
        )

        container.add_widget(text_field)
        container.add_widget(time_button)

        # 存储文本字段引用
        self.field_widgets[field_name] = text_field

        return container

    def _create_numeric_field(self, field_name: str, config: Dict[str, Any]):
        """
        创建数值输入字段（带单位显示）

        Args:
            field_name: 字段名称
            config: 字段配置

        Returns:
            数值字段组件
        """
        # 获取初始值
        initial_value = self.initial_data.get(field_name, "")

        # 创建容器
        container = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(56),
            spacing=dp(8)
        )

        # 创建数值输入字段
        numeric_field = MDTextField(
            mode="outlined",
            size_hint_x=0.7,
            text=str(initial_value) if initial_value else "",
            input_filter=config.get('input_filter')
        )

        # 添加提示文本
        if config.get('hint_text'):
            numeric_field.add_widget(
                MDTextFieldHintText(text=config['hint_text'])
            )

        # 创建单位标签
        unit_label = MDLabel(
            text=config.get('unit', ''),
            theme_text_color="Secondary",
            size_hint_x=0.3,
            halign="left",
            valign="center"
        )

        container.add_widget(numeric_field)
        container.add_widget(unit_label)

        # 存储数值字段引用
        self.field_widgets[field_name] = numeric_field

        return container

    def _open_dropdown_menu(self, field_name: str, text_field: MDTextField, options: List[str]):
        """
        打开下拉菜单

        Args:
            field_name: 字段名称
            text_field: 文本字段
            options: 选项列表
        """
        try:
            menu_items = []
            for option in options:
                # 修复闭包问题：使用默认参数捕获当前option值
                menu_items.append({
                    "text": option,
                    "on_release": lambda x=None, opt=option: self._on_dropdown_select(field_name, opt)
                })

            dropdown_menu = MDDropdownMenu(
                caller=text_field,
                items=menu_items,
                width=dp(200),
                max_height=dp(200),
                position="bottom"
            )

            self.dropdown_menus[field_name] = dropdown_menu
            dropdown_menu.open()
        except Exception as e:
            logger.error(f"打开下拉菜单失败 {field_name}: {e}")

    def _on_dropdown_select(self, field_name: str, selected_value: str):
        """
        下拉菜单选择回调

        Args:
            field_name: 字段名称
            selected_value: 选择的值
        """
        try:
            text_field = self.field_widgets.get(field_name)
            if text_field:
                text_field.text = selected_value
                # 强制刷新文本字段显示
                text_field.canvas.ask_update()

            # 关闭菜单
            if field_name in self.dropdown_menus:
                self.dropdown_menus[field_name].dismiss()
                # 清理菜单引用
                del self.dropdown_menus[field_name]
        except Exception as e:
            logger.error(f"下拉菜单选择失败 {field_name}: {e}")

    def _open_date_picker(self, field_name: str, text_field: MDTextField):
        """
        打开日期选择器 - 使用KivyMD内置组件

        Args:
            field_name: 字段名称
            text_field: 文本字段
        """
        try:
            # 优先使用KivyMD内置日期选择器
            from utils.kivymd_date_picker import show_kivymd_date_picker
            
            def on_date_selected(selected_date):
                """日期选择回调"""
                try:
                    if hasattr(selected_date, 'strftime'):
                        formatted_date = selected_date.strftime("%Y-%m-%d")
                    else:
                        # 如果是字符串，尝试解析后重新格式化
                        date_obj = FormFieldConfigs.parse_date(str(selected_date))
                        formatted_date = date_obj.strftime("%Y-%m-%d")
                    
                    text_field.text = formatted_date
                    logger.info(f"日期选择完成 {field_name}: {formatted_date}")
                except Exception as e:
                    logger.error(f"日期格式化失败 {field_name}: {e}")
                    text_field.text = str(selected_date)

            # 解析当前日期作为默认值
            try:
                current_date = FormFieldConfigs.parse_date(text_field.text).date() if text_field.text else datetime.now().date()
            except:
                current_date = datetime.now().date()

            # 使用KivyMD内置日期选择器
            field_config = FormFieldConfigs.get_field_config(field_name)
            title = f"选择{field_config.get('label', '日期')}"
            
            show_kivymd_date_picker(
                callback=on_date_selected,
                initial_date=current_date,
                title=title
            )
            
        except ImportError as import_error:
            logger.warning(f"无法导入kivymd_date_picker: {import_error}")
            # 直接使用当前日期作为回退方案
            text_field.text = datetime.now().strftime("%Y-%m-%d")
        except Exception as e:
            logger.warning(f"KivyMD日期选择器失败 {field_name}: {e}")
            # 最终回退方案：使用当前日期
            text_field.text = datetime.now().strftime("%Y-%m-%d")

    def _open_time_picker(self, time_field: MDTextField):
        """打开时间选择器"""
        from utils.time_picker_utils import show_time_picker
        from datetime import datetime
        
        # 解析当前时间
        current_time = None
        if time_field.text.strip():
            try:
                current_time = datetime.strptime(time_field.text.strip(), "%H:%M").time()
            except ValueError:
                current_time = datetime.now().time()
        else:
            current_time = datetime.now().time()
        
        # 显示时间选择器，使用24小时制和5分钟步进
        show_time_picker(
            callback_function=lambda time: self._set_time(time_field, time),
            default_time=current_time,
            picker_type="custom",  # 使用自定义24小时制选择器
            time_format="24h",     # 使用24小时制
            minute_step=5          # 5分钟步进，便于选择
        )
    
    def _set_time(self, time_field, time):
        """设置选择的时间"""
        time_field.text = time.strftime("%H:%M")


    def get_form_data(self) -> Dict[str, Any]:
        """
        获取表单数据

        Returns:
            表单数据字典
        """
        form_data = {}

        for field_name, widget in self.field_widgets.items():
            try:
                if hasattr(widget, 'text'):
                    value = widget.text
                    # 特殊处理开始日期：如果为空，设置为当前日期
                    if field_name in ['start_date', 'stop_date'] and (not value or value.strip() == ""):
                        value = datetime.now().strftime('%Y-%m-%d')
                        # 同时更新界面显示
                        widget.text = value
                    form_data[field_name] = value
            except Exception as e:
                logger.warning(f"获取字段数据失败 {field_name}: {e}")
                # 对于开始日期，即使出错也要设置默认值
                if field_name in ['start_date', 'stop_date']:
                    form_data[field_name] = datetime.now().strftime('%Y-%m-%d')
                else:
                    form_data[field_name] = ""

        return form_data

    def set_form_data(self, data: Dict[str, Any]):
        """
        设置表单数据

        Args:
            data: 表单数据
        """
        for field_name, value in data.items():
            widget = self.field_widgets.get(field_name)
            if widget and hasattr(widget, 'text'):
                try:
                    # 特殊处理日期字段，确保格式为YYYY-MM-DD，只保留日期部分
                    if field_name in ['start_date', 'end_date', 'stop_date'] and value:
                        try:
                            value_str = str(value).strip()
                            if value_str:
                                # 使用FormFieldConfigs的工具方法解析日期
                                try:
                                    parsed_date = FormFieldConfigs.parse_date(value_str)
                                    widget.text = parsed_date.strftime('%Y-%m-%d')
                                except ValueError:
                                    # 如果解析失败，使用当前日期作为默认值（针对start_date和stop_date）
                                    if field_name in ['start_date', 'stop_date']:
                                        widget.text = datetime.now().strftime('%Y-%m-%d')
                                    else:
                                        widget.text = str(value) if value else ""
                            else:
                                # 如果值为空，对于start_date和stop_date使用当前日期作为默认值
                                if field_name in ['start_date', 'stop_date']:
                                    widget.text = datetime.now().strftime('%Y-%m-%d')
                                else:
                                    widget.text = ""
                        except Exception:
                            # 如果出现异常，对于start_date和stop_date使用当前日期作为默认值
                            if field_name in ['start_date', 'stop_date']:
                                widget.text = datetime.now().strftime('%Y-%m-%d')
                            else:
                                widget.text = str(value) if value else ""
                    else:
                        widget.text = str(value) if value else ""
                except Exception as e:
                    logger.warning(f"设置字段数据失败 {field_name}: {e}")
                    # 对于开始日期和停药日期，即使出错也要设置默认值
                    if field_name in ['start_date', 'stop_date']:
                        widget.text = datetime.now().strftime('%Y-%m-%d')

    def validate_form(self) -> tuple[bool, List[str]]:
        """
        验证表单数据

        Returns:
            (是否有效, 错误信息列表)
        """
        form_data = self.get_form_data()
        return FormFieldConfigs.validate_form(form_data, self.fields)


class MedicationFormContent(BaseFormContent):
    """
    药物表单内容组件
    继承自BaseFormContent，复用通用功能
    """
    pass





def _build_med_dialog(title: str, fields: List[str], initial_data: Optional[Dict[str, Any]], save_callback: Optional[Callable], screen_instance, dialog_attr_name: str):
    """构建药物对话框的公共函数
    
    Args:
        title: 对话框标题
        fields: 需要显示的字段列表
        initial_data: 初始数据
        save_callback: 保存回调函数
        screen_instance: 屏幕实例
        dialog_attr_name: 对话框属性名称（如'add_dialog'或'edit_dialog'）
    """
    content_cls = MedicationFormContent(fields=fields, initial_data=initial_data)
    
    # 如果是编辑模式且有medication_id，设置到content_cls
    if initial_data and 'id' in initial_data:
        content_cls.medication_id = initial_data['id']
    elif initial_data and 'medication_id' in initial_data:
        content_cls.medication_id = initial_data['medication_id']

    def on_save(x):
        if save_callback:
            form_data = content_cls.get_form_data()
            valid, errors = FormFieldConfigs.validate_form(form_data, content_cls.fields)
            if not valid:
                try:
                    from kivymd.toast import toast
                    toast("\n".join(errors))
                except Exception:
                    logger.warning("表单验证失败：%s", errors)
                return
            # 统一回调数据格式：传递表单数据字典
            # 如果是编辑模式，需要包含id字段（medication_service期望的字段名）
            if hasattr(content_cls, 'medication_id'):
                form_data['id'] = content_cls.medication_id
            # 修复：确保在主线程中调用保存回调
            if save_callback is not None:
                try:
                    from kivy.clock import Clock
                    def _schedule_save(dt):
                        if save_callback is not None:
                            save_callback(form_data)
                    Clock.schedule_once(_schedule_save, 0)
                except Exception:
                    if save_callback is not None:
                        save_callback(form_data)
            else:
                logger.warning("保存回调函数未定义")
        getattr(screen_instance, dialog_attr_name).dismiss()

    # 创建对话框 - 符合KivyMD 2.0.1 dev0规范
    dialog = MDDialog(
        MDDialogHeadlineText(
            text=title,
            halign="center"
        ),
        MDDialogContentContainer(
            content_cls,
            orientation="vertical",
            spacing=dp(8)
        ),
        MDDialogButtonContainer(
            Widget(),  # 占位符，用于右对齐按钮
            MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: getattr(screen_instance, dialog_attr_name).dismiss()
            ),
            MDButton(
                MDButtonText(text="保存"),
                style="text",
                on_release=on_save
            ),
            spacing=dp(8)
        ),
        # 对话框属性设置
        size_hint=(0.9, None),
        height=int(_calc_dialog_height() + dp(120)),  # 增加标题和按钮高度
        auto_dismiss=False,
        radius=[dp(16), dp(16), dp(16), dp(16)]
    )
    
    setattr(screen_instance, dialog_attr_name, dialog)
    dialog.open()

def open_add_medication_dialog(screen_instance, save_callback=None):
    """打开新增药物对话框

    Args:
        screen_instance: HealthDiaryCreateScreen实例
        save_callback: 保存回调函数
    """
    # 为新增药物对话框指定需要的字段（只包含药物相关字段）
    required_fields = [
        'name',
        'dosage',
        'frequency',
        'start_date',
        'reason',
        'instructions'
    ]
    
    # 使用新的通用表单类
    content_cls = MedicationFormContent(fields=required_fields)
    
    # 使用通用对话框构建函数
    _build_dialog(
        title="添加药物",
        content_cls=content_cls,
        save_callback=save_callback,
        screen_instance=screen_instance,
        dialog_attr_name='add_dialog'
    )

def open_add_blood_pressure_dialog(screen_instance, save_callback=None):
    """
    打开新增血压记录对话框

    Args:
        screen_instance: HealthDiaryCreateScreen实例
        save_callback: 保存回调函数
    """
    # 使用新的通用测量表单类
    content_cls = MeasurementFormContent(measurement_type='blood_pressure')
    
    # 使用通用对话框构建函数
    _build_dialog(
        title="添加血压记录",
        content_cls=content_cls,
        save_callback=save_callback,
        screen_instance=screen_instance,
        dialog_attr_name='blood_pressure_dialog',
        height=_calc_blood_pressure_dialog_height()
    )

def open_edit_medication_dialog(screen_instance, medication, save_callback=None):
    """
    打开编辑药物对话框

    Args:
        screen_instance: MedicationManagementScreen实例
        medication: 药物数据字典
        save_callback: 保存回调函数
    """
    # 为编辑药物对话框指定需要的字段
    required_fields = [
        'name',
        'dosage',
        'frequency',
        'start_date',
        'reason',
        'instructions'
    ]
    
    # 使用新的通用表单类
    content_cls = MedicationFormContent(fields=required_fields, initial_data=medication)
    
    # 如果是编辑模式且有medication_id，设置到content_cls
    if medication and 'id' in medication:
        content_cls.medication_id = medication['id']
    elif medication and 'medication_id' in medication:
        content_cls.medication_id = medication['medication_id']
    
    # 使用通用对话框构建函数
    _build_dialog(
        title="修改药物",
        content_cls=content_cls,
        save_callback=save_callback,
        screen_instance=screen_instance,
        dialog_attr_name="edit_dialog"
    )

def open_reminder_dialog(screen_instance, medication, save_callback=None):
    """打开提醒设置对话框

    Args:
        screen_instance: MedicationManagementScreen实例
        medication: 药物数据字典
        save_callback: 保存回调函数
    """
    # 使用新的提醒表单类
    content_cls = ReminderFormContent(medication_data=medication)
    
    # 使用通用对话框构建函数
    _build_dialog(
        title="设置服药提醒",
        content_cls=content_cls,
        save_callback=save_callback,
        screen_instance=screen_instance,
        dialog_attr_name='reminder_dialog',
        height=int(_calc_dialog_height(target_height=600, max_ratio=0.8, min_height=400) + dp(120))
    )

def open_stop_medication_dialog(screen_instance, name, confirm_callback=None):
    """打开停药确认对话框

    Args:
        screen_instance: MedicationManagementScreen实例
        name: 药物名称
        confirm_callback: 确认回调函数
    """
    # 使用新的停药表单类
    content_cls = StopMedicationFormContent(name=name)
    
    # 定义回调函数，将name和停药数据合并
    def combined_callback(stop_data):
        combined_data = {
            'name': name,
            **stop_data
        }
        if confirm_callback:
            confirm_callback(combined_data)
    
    # 使用通用对话框构建函数
    _build_dialog(
        title="停药设置",
        content_cls=content_cls,
        save_callback=combined_callback,
        screen_instance=screen_instance,
        dialog_attr_name='stop_dialog',
        height=int(dp(420))
    )

def open_delete_confirm_dialog(screen_instance, name, confirm_callback=None):
    """打开删除确认对话框

    Args:
        screen_instance: MedicationManagementScreen实例
        name: 药物名称
        confirm_callback: 确认回调函数
    """
    # 使用通用对话框构建函数，这是一个简单的确认对话框
    _build_dialog(
        title="删除确认",
        content_cls=MDBoxLayout(),  # 空内容容器
        save_callback=confirm_callback,
        screen_instance=screen_instance,
        dialog_attr_name='delete_dialog',
        height=int(dp(200)),
        supporting_text=f"确认要删除 {name} 吗？此操作不可撤销。"
    )

class ReminderFormContent(BaseFormContent):
    """提醒设置表单内容类 - 增强版本支持人工控制、默认时间和铃声设置"""
    
    # 最大提醒次数常量
    MAX_REMINDERS = 6

    def __init__(self, medication_data=None, **kwargs):
        self.medication_data = medication_data or {}
        # 修复：设置medication_id属性，从medication_data中获取
        self.medication_id = self.medication_data.get('id') or self.medication_data.get('medication_id')
        self.reminder_times = []  # 存储提醒时间字段
        self.max_reminders = self._get_max_reminders()
        self.default_times = self._get_default_times()  # 获取默认时间
        self.selected_ringtone = "默认铃声"  # 默认铃声

        # 调用基类初始化，但不传入字段，因为提醒表单有特殊结构
        super().__init__(fields=[], **kwargs)
        
        # 构建提醒表单的特殊结构
        self.build_form()

    def _get_max_reminders(self):
        """根据用药频次获取最大提醒次数"""
        frequency = self.medication_data.get('frequency', '一天一次')
        frequency_map = {
            '一天一次': 1,
            '一天两次': 2,
            '一天三次': 3,
            '一天四次': 4,
            '每12小时一次': 2,
            '每8小时一次': 3,
            '每6小时一次': 4,
            '每4小时一次': 6,
            '按需服用': 1,
            '其他': 4
        }
        return frequency_map.get(frequency, 1)

    def _get_default_times(self):
        """根据用药频次获取默认提醒时间"""
        frequency = self.medication_data.get('frequency', '一天一次')
        default_times_map = {
            '一天一次': ['07:00'],
            '一天两次': ['07:00', '19:00'],
            '一天三次': ['07:00', '12:00', '18:00'],
            '一天四次': ['07:00', '12:00', '18:00', '22:00'],
            '每12小时一次': ['07:00', '19:00'],
            '每8小时一次': ['07:00', '15:00', '23:00'],
            '每6小时一次': ['06:00', '12:00', '18:00', '00:00'],
            '每4小时一次': ['06:00', '10:00', '14:00', '18:00', '22:00', '02:00'],
            '按需服用': ['07:00'],
            '其他': ['07:00', '12:00', '18:00', '22:00']
        }
        return default_times_map.get(frequency, ['07:00'])

    def build_form(self):
        """构建提醒设置表单 - 增强版本"""
        # 药物名称显示
        name_label = MDLabel(
            text=f"药物：{self.medication_data.get('name', '未知药物')}",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        self.add_widget(name_label)

        # 用药频次显示
        frequency_label = MDLabel(
            text=f"用药频次：{self.medication_data.get('frequency', '一天一次')}",
            theme_text_color="Secondary",
            size_hint_y=None,
            height=dp(40)
        )
        self.add_widget(frequency_label)

        # 最大提醒次数说明
        max_label = MDLabel(
            text=f"建议设置 {self.max_reminders} 个提醒时间，可手动调整",
            theme_text_color="Hint",
            size_hint_y=None,
            height=dp(30)
        )
        self.add_widget(max_label)

        # 添加默认的提醒时间字段
        for i, default_time in enumerate(self.default_times[:self.max_reminders]):
            field_name = f'reminder_time_{i}'
            if field_name in self.field_widgets:
                self.field_widgets[field_name].text = default_time

        # 控制按钮区域
        control_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(8),
            size_hint_y=None,
            height=dp(48)
        )

        # 添加提醒时间按钮
        add_button = MDButton(
            MDButtonText(text="+ 添加提醒"),
            style="outlined",
            size_hint_x=0.5,
            on_release=self._add_reminder_time,
            disabled=len([f for f in self.field_widgets.keys() if f.startswith('reminder_time_')]) >= ReminderFormContent.MAX_REMINDERS
        )
        control_container.add_widget(add_button)

        # 删除提醒时间按钮
        remove_button = MDButton(
            MDButtonText(text="- 删除提醒"),
            style="outlined",
            size_hint_x=0.5,
            on_release=self._remove_last_reminder_time,
            disabled=len([f for f in self.field_widgets.keys() if f.startswith('reminder_time_')]) <= 1
        )
        control_container.add_widget(remove_button)

        self.add_widget(control_container)

        # 铃声设置
        ringtone_label = MDLabel(
            text="提醒铃声设置",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(40)
        )
        self.add_widget(ringtone_label)

        # 铃声选择字段
        self.ringtone_field = MDTextField(
            MDTextFieldHelperText(
                text="点击选择提醒铃声",
                mode="on_focus"
            ),
            mode="outlined",
            text=self.selected_ringtone,
            readonly=True,
            size_hint_y=None,
            height=dp(56),
            on_release=self._show_ringtone_menu
        )
        self.add_widget(self.ringtone_field)

    def _add_reminder_time(self, *args):
        """添加提醒时间字段"""
        # 计算当前提醒时间字段数量
        current_reminder_fields = [f for f in self.field_widgets.keys() if f.startswith('reminder_time_')]
        if len(current_reminder_fields) >= ReminderFormContent.MAX_REMINDERS:
            return

        # 创建新的字段名称
        new_field_index = len(current_reminder_fields)
        field_name = f'reminder_time_{new_field_index}'

        # 创建字段配置
        config = FormFieldConfigs.get_field_config('reminder_time')

        # 创建时间字段
        time_field = self._create_time_field(field_name, config)
        time_field.text = "07:00"  # 默认时间

        # 添加到布局中
        wrapper_height = (getattr(time_field, 'height', None) or dp(56)) + dp(12)
        wrapper = MDBoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=wrapper_height,
            padding=[0, dp(6), 0, dp(6)]
        )
        wrapper.add_widget(time_field)
        self.add_widget(wrapper, index=4)  # 插入到控制按钮之前

        # 注册字段
        self.field_widgets[field_name] = time_field

        # 更新UI
        self._update_ui()

    def _remove_last_reminder_time(self, *args):
        """删除最后一个提醒时间"""
        # 获取所有提醒时间字段
        reminder_fields = [f for f in self.field_widgets.keys() if f.startswith('reminder_time_')]
        if len(reminder_fields) <= 1:
            return

        # 获取最后一个字段
        last_field_name = sorted(reminder_fields)[-1]
        last_field = self.field_widgets[last_field_name]

        # 从布局中移除字段的包装器
        for child in self.children:
            if isinstance(child, MDBoxLayout) and last_field in child.children:
                self.remove_widget(child)
                break

        # 从字段字典中移除
        del self.field_widgets[last_field_name]

        # 更新UI
        self._update_ui()

    def _update_ui(self):
        """更新UI：更新控制按钮状态"""
        # 更新控制按钮状态
        try:
            # 查找控制按钮容器
            for child in self.children:
                if isinstance(child, MDBoxLayout) and child.orientation == "horizontal":
                    # 检查是否是控制按钮容器
                    if len(child.children) == 2:
                        for button in child.children:
                            if isinstance(button, MDButton):
                                button_text = button.children[0].text if button.children else ""
                                if "添加" in button_text:
                                    # 更新添加按钮状态
                                    button.disabled = len([f for f in self.field_widgets.keys() if f.startswith('reminder_time_')]) >= ReminderFormContent.MAX_REMINDERS
                                elif "删除" in button_text:
                                    # 更新删除按钮状态
                                    button.disabled = len([f for f in self.field_widgets.keys() if f.startswith('reminder_time_')]) <= 1
                        break
        except Exception as e:
            logger.warning(f"更新控制按钮状态失败: {e}")

    def _show_ringtone_menu(self, *args):
        """显示铃声选择菜单"""
        from kivymd.uix.menu import MDDropdownMenu

        ringtone_items = [
            {
                "text": "默认铃声",
                "on_release": lambda x="默认铃声": self._set_ringtone(x),
            },
            {
                "text": "温和提醒",
                "on_release": lambda x="温和提醒": self._set_ringtone(x),
            },
            {
                "text": "清脆铃声",
                "on_release": lambda x="清脆铃声": self._set_ringtone(x),
            },
            {
                "text": "柔和音调",
                "on_release": lambda x="柔和音调": self._set_ringtone(x),
            },
        ]

        self.ringtone_menu = MDDropdownMenu(
            caller=self.ringtone_field,
            items=ringtone_items,
            width=dp(240),
        )
        self.ringtone_menu.open()

    def _set_ringtone(self, ringtone_name):
        """设置选中的铃声"""
        self.selected_ringtone = ringtone_name
        self.ringtone_field.text = ringtone_name
        self.ringtone_menu.dismiss()

    def get_form_data(self):
        """获取提醒设置数据 - 统一返回平铺字典格式"""
        # 获取提醒时间
        times = []
        for field_name, widget in self.field_widgets.items():
            if field_name.startswith('reminder_time_') and hasattr(widget, 'text') and widget.text.strip():
                time_str = widget.text.strip()
                # 验证时间格式
                if FormFieldConfigs._is_valid_time_format(time_str):
                    times.append(time_str)
                else:
                    logger.warning(f"无效的时间格式: {time_str}")

        # 返回平铺的字典结构
        return {
            'times': times,
            'ringtone': self.selected_ringtone,
            'enabled': len(times) > 0,
            'type': 'daily',
            'advance_minutes': 15
        }
    
class StopMedicationFormContent(BaseFormContent):
    """停药表单内容组件"""
    def __init__(self, name="", **kwargs):
        self.name = name
        
        # 使用配置驱动的方式创建字段
        fields = ['stop_reason', 'stop_date']
        
        super().__init__(fields=fields, **kwargs)
        
        # 添加特殊组件
        self._add_special_components()

    def _add_special_components(self):
        """添加特殊组件（提示标签等）"""
        # 在字段之前添加提示标签
        hint_label = MDLabel(
            text=f"请设置 [{self.name}] 的停药信息",
            theme_text_color="Primary",
            halign="left",
            valign="center",
            size_hint_y=None,
            height=dp(56)
        )
        # 将提示标签插入到布局的开头
        self.add_widget(hint_label, index=len(self.children))

    def get_form_data(self):
        """获取停药数据 - 统一返回平铺字典格式"""
        form_data = super().get_form_data()
        # 直接返回基类获取的数据，无需额外处理
        return form_data

class MeasurementFormContent(BaseFormContent):
    """
    通用测量记录表单内容组件
    支持血压和血糖记录
    """

    def __init__(self, measurement_type: str = 'blood_pressure', initial_data: Optional[Dict[str, Any]] = None, **kwargs):
        """
        初始化测量记录表单内容

        Args:
            measurement_type: 测量类型 ('blood_pressure' 或 'blood_sugar')
            initial_data: 初始数据
        """
        self.measurement_type = measurement_type
        
        # 根据测量类型确定字段
        if measurement_type == 'blood_pressure':
            fields = ['systolic', 'diastolic', 'heart_rate', 'time']
        else:  # blood_sugar
            fields = ['value', 'time']
        
        super().__init__(fields=fields, initial_data=initial_data, **kwargs)

    def get_form_data(self) -> Dict[str, Any]:
        """
        获取表单数据

        Returns:
            表单数据字典
        """
        form_data = super().get_form_data()
        # 确保数值字段为正确的类型
        if self.measurement_type == 'blood_pressure':
            for field in ['systolic', 'diastolic', 'heart_rate']:
                if field in form_data and form_data[field]:
                    try:
                        form_data[field] = int(form_data[field])
                    except ValueError:
                        pass  # 保持原值
        else:  # blood_sugar
            if 'value' in form_data and form_data['value']:
                try:
                    form_data['value'] = float(form_data['value'])
                except ValueError:
                    pass  # 保持原值
        return form_data


def _build_dialog(title: str, content_cls, save_callback: Optional[Callable], screen_instance, dialog_attr_name: str, height: Optional[int] = None, supporting_text: Optional[str] = None):
    """构建通用对话框的公共函数
    
    Args:
        title: 对话框标题
        content_cls: 内容类实例
        save_callback: 保存回调函数
        screen_instance: 屏幕实例
        dialog_attr_name: 对话框属性名称（如'add_dialog'或'edit_dialog'）
        height: 对话框高度，如果为None则自动计算
        supporting_text: 支持文本（可选）
    """
    def on_save(x):
        if save_callback:
            # 获取表单数据
            if hasattr(content_cls, 'get_form_data'):
                form_data = content_cls.get_form_data()
            else:
                form_data = {}
            
            # 验证表单数据
            valid = True
            errors = []
            if hasattr(content_cls, 'validate_form'):
                valid, errors = content_cls.validate_form()
            elif hasattr(content_cls, 'validate_data'):
                valid, errors = content_cls.validate_data()
            
            if not valid:
                try:
                    from kivymd.toast import toast
                    toast("\n".join(errors))
                except Exception:
                    logger.warning("表单验证失败：%s", errors)
                return
            
            # 调用保存回调
            try:
                from kivy.clock import Clock
                def _schedule_save(dt):
                    if save_callback:
                        save_callback(form_data)
                Clock.schedule_once(_schedule_save, 0)
            except Exception:
                if save_callback:
                    save_callback(form_data)
        # 关闭对话框
        dialog = getattr(screen_instance, dialog_attr_name, None)
        if dialog:
            dialog.dismiss()

    # 确保height有默认值，避免None导致的错误
    if height is None:
        height = _calc_dialog_height()

    # 创建对话框 - 符合KivyMD 2.0.1 dev0规范
    dialog = MDDialog(
        MDDialogHeadlineText(
            text=title,
            halign="center"
        ),
        MDDialogSupportingText(
            text=supporting_text,
            halign="center"
        ) if supporting_text else None,
        MDDialogContentContainer(
            content_cls,
            orientation="vertical",
            spacing=dp(8)
        ),
        MDDialogButtonContainer(
            Widget(),  # 占位符，用于右对齐按钮
            MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: getattr(screen_instance, dialog_attr_name).dismiss()
            ),
            MDButton(
                MDButtonText(text="保存" if save_callback else "确认"),
                style="text",
                on_release=on_save if save_callback else lambda x: getattr(screen_instance, dialog_attr_name).dismiss()
            ),
            spacing=dp(8)
        ),
        # 对话框属性设置
        size_hint=(0.9, None),
        height=height,
        auto_dismiss=False,
        radius=[dp(16), dp(16), dp(16), dp(16)]
    )
    
    setattr(screen_instance, dialog_attr_name, dialog)
    dialog.open()


def open_add_blood_sugar_dialog(screen_instance, save_callback=None):
    """
    打开新增血糖记录对话框

    Args:
        screen_instance: HealthDiaryCreateScreen实例
        save_callback: 保存回调函数
    """
    # 使用新的通用测量表单类
    content_cls = MeasurementFormContent(measurement_type='blood_sugar')
    
    # 使用通用对话框构建函数
    _build_dialog(
        title="添加血糖记录",
        content_cls=content_cls,
        save_callback=save_callback,
        screen_instance=screen_instance,
        dialog_attr_name='blood_sugar_dialog',
        height=_calc_blood_pressure_dialog_height()
    )
