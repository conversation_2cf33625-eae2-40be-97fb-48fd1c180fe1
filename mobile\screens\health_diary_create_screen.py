#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
health_diary_create_screen_refactored.py - 重构后的健康日记创建页面
- 遵循 KivyMD 2.0.1 dev0 规范与 theme.py 配置
- 折叠式布局（DiaryPanelCard）
- UI优化：统一间距/输入框高度、滚动体验优化、按钮自适应
- 优化代码结构：移除重复逻辑、统一组件管理
- 增强异常处理和错误日志记录
- 重构：改进保存刷新逻辑、实现编辑模式、数据库优化
- 整体优化：强制使用经理模块、合并方法、标准化异常、性能提升
"""

from __future__ import annotations
import os
import logging
import json
from datetime import datetime, date
from typing import Dict, Optional, List, Tuple

from kivy.app import App
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.properties import StringProperty, BooleanProperty, ObjectProperty, ListProperty
from kivy.storage.jsonstore import JsonStore
from kivy.lang import Builder

from kivymd.uix.card import MDCard
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.label import MDLabel, MDIcon
from kivymd.uix.textfield import MDTextField
from kivymd.uix.divider import MDDivider
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.scrollview import MDScrollView

# 常量定义
ERROR_DB_MANAGER_NOT_INITIALIZED = "数据库管理器未初始化"
ERROR_NO_DB_MANAGER_AVAILABLE = "未找到可用的数据库管理器"
ERROR_LOCAL_BACKUP_SAVE_FAILED = "本地备份保存失败"

# 必须依赖导入 - 如果失败，抛出异常
from mobile.screens.base_screen import BaseScreen, BASE_SCREEN_KV
from mobile.widgets.common_kv_components import load_common_kv_components
from mobile.utils.toast import show_toast
from mobile.theme import HealthAppTheme as AppThemeManager
from mobile.utils.health_diary_validator import HealthDiaryValidator
from mobile.utils.health_diary_ui_manager import HealthDiaryUIManager
from mobile.utils.health_diary_data_manager import HealthDiaryDataManager

# 可选依赖 - 图表
try:
    from mobile.utils.health_chart_utils import HealthChartView, create_health_chart_data
    HEALTH_CHART_AVAILABLE = True
    logging.info("健康图表工具导入成功")
except ImportError as e:
    logging.warning(f"健康图表工具导入失败: {e}，将使用简化版图表显示")
    HEALTH_CHART_AVAILABLE = False
    HealthChartView = None



logger = logging.getLogger(__name__)

# ---------------- 折叠面板组件 ----------------
class DiaryPanelCard(MDCard):
    """健康日记折叠面板卡片组件"""
    title = StringProperty("")
    icon = StringProperty("")
    icon_color = ListProperty([1, 1, 1, 1])
    expanded = BooleanProperty(True)
    panel_id = StringProperty("")
    screen_ref = ObjectProperty(None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.style = "elevated"
        self.elevation = 2
        self.radius = [12, 12, 12, 12]
        self.adaptive_height = True

    def toggle_expand(self):
        self.expanded = not self.expanded
        if self.screen_ref and hasattr(self.screen_ref, 'save_panel_states'):
            self.screen_ref.save_panel_states(self.panel_id, self.expanded)

DIARY_PANEL_KV = '''
<DiaryPanelCard>:
    style: "elevated"
    elevation: 3
    radius: [16, 16, 16, 16]
    adaptive_height: True
    theme_bg_color: "Custom"
    md_bg_color: app.theme.CARD_BACKGROUND if hasattr(app, 'theme') and hasattr(app.theme, 'CARD_BACKGROUND') else [0.95, 0.95, 0.95, 1]

    MDBoxLayout:
        orientation: 'vertical'
        size_hint_y: None
        height: self.minimum_height
        spacing: dp(0)
        padding: [dp(0), dp(0), dp(0), dp(0)]

        # 顶部标题区域
        MDBoxLayout:
            orientation: 'horizontal'
            size_hint_y: None
            height: dp(50)
            spacing: dp(8)
            padding: [dp(10), dp(6), dp(10), dp(6)]
            on_touch_down: root.toggle_expand() if self.collide_point(*args[1].pos) else None  # 整个标题区域都可以点击

            MDIcon:
                icon: root.icon
                theme_icon_color: "Custom"
                icon_color: root.icon_color
                size_hint_x: None
                width: dp(24)
                pos_hint: {"center_y": 0.5}

            MDLabel:
                text: root.title
                bold: True
                theme_text_color: "Primary"
                pos_hint: {"center_y": 0.5}
                font_size: dp(14)
                on_touch_down: root.toggle_expand() if self.collide_point(*args[1].pos) else None  # 标签也可以点击

            Widget:
                size_hint_x: 0.01

            MDIconButton:
                icon: "chevron-down" if root.expanded else "chevron-right"
                icon_size: dp(24)  # 增大图标提高灵敏度
                theme_icon_color: "Custom"
                icon_color: app.theme.TEXT_SECONDARY if hasattr(app, 'theme') else [0.6,0.6,0.6,1]
                size_hint_x: None
                width: dp(32)  # 增大点击区域
                on_release: root.toggle_expand()
                pos_hint: {"center_y": 0.5}  # 居中对齐

        # 分隔线
        MDDivider:
            height: dp(1)

        # 内容区域
        MDBoxLayout:
            id: content_layout
            orientation: 'vertical'
            size_hint_y: None
            height: self.minimum_height if root.expanded else 0
            opacity: 1 if root.expanded else 0
            spacing: dp(8)  # 减小间距
            padding: [dp(12), dp(8), dp(12), dp(12)]  # 调整内边距
'''

Builder.load_string(DIARY_PANEL_KV)

class HealthDiaryCreateScreen(BaseScreen):
    """健康日记创建页面（重构版：使用经理模块、合并逻辑）"""
    selected_date = StringProperty("")
    _STATE_STORE_KEY = "health_diary_panels"

    def __init__(self, **kwargs):
        # 设置BaseScreen所需的kwargs参数
        kwargs['screen_title'] = '创建健康日记'
        kwargs['show_top_bar'] = True
        kwargs['top_bar_action_icon'] = 'refresh'
        
        # 调用父类初始化
        super().__init__(**kwargs)
        
        # 初始化基本属性
        self.selected_date = date.today().strftime("%Y-%m-%d")
        load_common_kv_components()
        self.app_theme = AppThemeManager()
        self._store = self._get_store()
        self._panel_states: Dict[str, bool] = {}
        self._is_edit_mode = False
        self._current_diary_data = None
        self._readonly_mode = False
        self.blood_pressure_records = []
        self.blood_sugar_records = []
        
        # 初始化经理 - 恢复完整功能
        self._init_managers()
        
        # 初始化状态
        self._initialized = False

    def _init_managers(self):
        """初始化所有经理模块"""
        current_user_id = self._get_current_user_id()
        self.db_manager = HealthDiaryDataManager(custom_id=current_user_id)
        self.validator = HealthDiaryValidator()
        self.ui_manager = HealthDiaryUIManager(self)
        logger.info("所有经理模块初始化成功")

    def on_enter(self, *args):
        """页面进入时调用 - 遵循BaseScreen模式"""
        super().on_enter(*args)
        logger.debug(f"on_enter called, _initialized: {self._initialized}")
        
        # 延迟执行数据操作，确保UI已完全初始化
        Clock.schedule_once(self._delayed_on_enter_setup, 0.1)

    def _delayed_on_enter_setup(self, dt):
        """延迟执行的on_enter设置，确保UI已完全初始化"""
        try:
            if self._current_diary_data and self._is_edit_mode:
                logger.debug("Loading diary for edit (delayed)")
                self.load_diary_for_edit(self._current_diary_data)
            else:
                logger.debug("Clearing all data (delayed)")
                if hasattr(self, 'ui_manager') and self.ui_manager:
                    self.ui_manager.clear_all_data()
        except Exception as e:
            logger.error(f"延迟设置失败: {e}")

    def do_content_setup(self):
        """在content_container中添加内容 - 使用兼容BaseScreen的UI管理器"""
        try:
            # 安全地获取content_container
            content_container = None
            if hasattr(self, 'ids') and isinstance(self.ids, dict):
                content_container = self.ids.get('content_container')
            
            if not content_container:
                logger.error("无法找到content_container")
                return
            
            # 清空content_container中的现有内容
            content_container.clear_widgets()
            
            # 创建main_layout供UI管理器使用，但不创建额外的ScrollView
            main_layout = MDBoxLayout(
                orientation='vertical',
                adaptive_height=True,
                spacing=dp(16),
                padding=[dp(0), dp(0), dp(0), dp(0)]
            )
            
            # 直接将main_layout添加到content_container
            content_container.add_widget(main_layout)
            
            # 将main_layout添加到ids中供UI管理器访问
            if not hasattr(self, 'ids'):
                self.ids = {}
            self.ids['main_layout'] = main_layout
            
            # 使用修改后的UI管理器创建面板 - 跳过ScrollView创建
            logger.debug("Creating panels using modified UI manager")
            self.ui_manager.create_panels_for_basescreen()
            
            self._initialized = True
            logger.debug("Scheduling _after_ui_init")
            Clock.schedule_once(self._after_ui_init, 0.1)
            
            logger.info("BaseScreen兼容模式的UI创建完成")
        except Exception as e:
            logger.error(f"do_content_setup失败: {e}")
            import traceback
            logger.error(traceback.format_exc())



    def on_action(self):
        """处理顶部操作栏按钮（刷新）"""
        try:
            logger.info("刷新健康日记创建页面")
            self.refresh_data()
        except Exception as e:
            logger.error(f"刷新操作失败: {e}")
            show_toast("刷新失败")

    def _after_ui_init(self, dt):
        logger.debug("Restoring panel states")
        self.restore_panel_states()
        logger.debug("Binding panel events")
        self.ui_manager.bind_panel_events()
        logger.debug("_after_ui_init completed")

    # ----------- 血压和血糖相关方法 -----------
    def add_blood_pressure_record(self):
        """添加血压记录 - 通过对话框方式"""
        try:
            # 导入对话框函数
            from mobile.screens.dialogs import open_add_blood_pressure_dialog
            
            def save_callback(form_data):
                """保存血压记录回调"""
                try:
                    # 添加新的血压记录到数据列表
                    self.blood_pressure_records.append(form_data)
                    
                    # 根据时间排序所有记录
                    self.blood_pressure_records.sort(key=lambda x: x.get('time', ''))
                    
                    # 更新血压图表
                    self.ui_manager.update_blood_pressure_chart()
                    
                except Exception as e:
                    logger.error(f"保存血压记录失败: {e}")
                    show_toast("保存血压记录失败")
            
            # 打开对话框
            open_add_blood_pressure_dialog(self, save_callback)
        except Exception as e:
            logger.error(f"打开血压记录对话框失败: {e}")
            show_toast("打开血压记录对话框失败")
    
    def add_blood_sugar_record(self):
        """添加血糖记录 - 通过对话框方式"""
        try:
            # 导入对话框函数
            from mobile.screens.dialogs import open_add_blood_sugar_dialog
            
            def save_callback(form_data):
                """保存血糖记录回调"""
                try:
                    # 添加新的血糖记录到数据列表
                    self.blood_sugar_records.append(form_data)
                    
                    # 根据时间排序所有记录
                    self.blood_sugar_records.sort(key=lambda x: x.get('time', ''))
                    
                    # 更新血糖图表
                    self.ui_manager.update_blood_sugar_chart()
                    
                except Exception as e:
                    logger.error(f"保存血糖记录失败: {e}")
                    show_toast("保存血糖记录失败")
            
            # 打开对话框
            open_add_blood_sugar_dialog(self, save_callback)
        except Exception as e:
            logger.error(f"打开血糖记录对话框失败: {e}")
            show_toast("打开血糖记录对话框失败")

    # ----------- 下拉菜单相关方法 -----------
    def show_blood_sugar_time_menu(self, dropdown_item):
        """显示血糖时间选择菜单"""
        try:
            time_options = ["早餐前", "早餐后2小时", "午餐前", "午餐后2小时", "晚餐前", "晚餐后2小时", "睡前", "随机"]
            menu_items = []
            for time in time_options:
                # 使用默认参数确保正确捕获变量
                menu_items.append({
                    "text": time,
                    "on_release": lambda t=time: self.set_blood_sugar_time(dropdown_item, t)
                })
            self.blood_sugar_time_menu = MDDropdownMenu(
                caller=dropdown_item,
                items=menu_items,
                position="bottom",
                width=dp(240),
                max_height=dp(200)
            )
            self.blood_sugar_time_menu.open()
        except Exception as e:
            logger.error(f"显示血糖时间菜单失败: {e}")

    def show_blood_pressure_time_menu(self, dropdown_item):
        """显示血压时间选择菜单"""
        try:
            time_options = ["早晨", "上午", "中午", "下午", "晚上", "睡前", "随机"]
            menu_items = []
            for time in time_options:
                # 使用默认参数确保正确捕获变量
                menu_items.append({
                    "text": time,
                    "on_release": lambda t=time: self.set_blood_pressure_time(dropdown_item, t)
                })
            self.blood_pressure_time_menu = MDDropdownMenu(
                caller=dropdown_item,
                items=menu_items,
                position="bottom",
                width=dp(240),
                max_height=dp(200)
            )
            self.blood_pressure_time_menu.open()
        except Exception as e:
            logger.error(f"显示血压时间菜单失败: {e}")

    def set_blood_sugar_time(self, dropdown_item, time):
        """设置血糖时间"""
        try:
            # 查找并更新文本组件
            for child in dropdown_item.children:
                if hasattr(child, 'text'):
                    child.text = time
                    break
            if hasattr(self, 'blood_sugar_time_menu') and self.blood_sugar_time_menu:
                self.blood_sugar_time_menu.dismiss()
        except Exception as e:
            logger.error(f"设置血糖时间失败: {e}")

    def set_blood_pressure_time(self, dropdown_item, time):
        """设置血压时间"""
        try:
            # 查找并更新文本组件
            for child in dropdown_item.children:
                if hasattr(child, 'text'):
                    child.text = time
                    break
            if hasattr(self, 'blood_pressure_time_menu') and self.blood_pressure_time_menu:
                self.blood_pressure_time_menu.dismiss()
        except Exception as e:
            logger.error(f"设置血压时间失败: {e}")

    def show_weight_status_menu(self, dropdown_item):
        """显示体重状态选择菜单"""
        try:
            status_options = ["空腹", "餐后"]
            menu_items = []
            for status in status_options:
                # 使用默认参数确保正确捕获变量
                menu_items.append({
                    "text": status,
                    "on_release": lambda s=status: self.set_weight_status(dropdown_item, s)
                })
            self.weight_status_menu = MDDropdownMenu(
                caller=dropdown_item,
                items=menu_items,
                position="bottom",
                width=dp(240),
                max_height=dp(200)
            )
            self.weight_status_menu.open()
        except Exception as e:
            logger.error(f"显示体重状态菜单失败: {e}")

    def set_weight_status(self, dropdown_item, status):
        """设置体重状态"""
        try:
            # 查找并更新文本组件
            for child in dropdown_item.children:
                if hasattr(child, 'text'):
                    child.text = status
                    break
            if hasattr(self, 'weight_status_menu') and self.weight_status_menu:
                self.weight_status_menu.dismiss()
        except Exception as e:
            logger.error(f"设置体重状态失败: {e}")

    # ----------- 日期选择 -----------
    def show_date_picker(self):
        from mobile.utils.kivymd_date_picker import show_kivymd_date_picker
        def on_date_selected(selected_date):
            formatted_date = selected_date.strftime("%Y-%m-%d")
            self.selected_date = formatted_date
            # 确保date_field存在后再访问它
            if "date_field" in self.ui_manager.widgets:
                self.ui_manager.widgets["date_field"].text = formatted_date
            else:
                logger.warning("date_field not found in UI manager widgets during date picker")
        current_date = datetime.strptime(self.selected_date, "%Y-%m-%d").date()
        show_kivymd_date_picker(callback=on_date_selected, initial_date=current_date, title="选择日期")

    # ----------- 面板状态管理 -----------
    def save_panel_states(self, panel_id: Optional[str] = None, expanded: Optional[bool] = None):
        if panel_id and expanded is not None:
            self._panel_states[panel_id] = expanded
        else:
            for key in self._panel_states:
                panel = self.ui_manager.widgets.get(f"{key}_panel")
                if panel:
                    self._panel_states[key] = panel.expanded
        self._store.put(self._STATE_STORE_KEY, states=self._panel_states)

    def restore_panel_states(self):
        if self._store.exists(self._STATE_STORE_KEY):
            self._panel_states = self._store.get(self._STATE_STORE_KEY).get("states", {})
        # 初始化默认面板状态
        default_states = {
            "date": True, 
            "condition": True,
            "blood_pressure": False, 
            "blood_sugar": False,
            "weight": False, 
            "exercise": False, 
            "sleep": False
        }
        for key, default in default_states.items():
            state = self._panel_states.get(key, default)
            panel = self.ui_manager.widgets.get(f"{key}_panel")
            if panel:
                panel.expanded = state

    # ----------- 编辑模式和只读模式 -----------
    def set_edit_mode(self, diary_data: Dict, is_readonly: bool = False):
        logger.debug(f"set_edit_mode called, _initialized: {self._initialized}")
        
        self._current_diary_data = diary_data
        self._is_edit_mode = True
        diary_date = self._parse_date(diary_data.get('diary_date', ''))
        if diary_date:
            self.selected_date = diary_date.strftime('%Y-%m-%d')
            # 延迟设置date_field，确保UI已初始化
            Clock.schedule_once(lambda dt: self._update_date_field(), 0.1)
        
        # 延迟加载编辑数据，确保UI已初始化
        Clock.schedule_once(lambda dt: self.load_diary_for_edit(diary_data), 0.2)
        
        if is_readonly:
            Clock.schedule_once(lambda dt: self._set_readonly_mode(True), 0.3)

    def _update_date_field(self):
        """更新日期字段的文本"""
        try:
            if "date_field" in self.ui_manager.widgets:
                self.ui_manager.widgets["date_field"].text = self.selected_date
                logger.debug(f"Updated date_field to: {self.selected_date}")
            else:
                logger.warning("date_field not found in UI manager widgets during update")
                logger.debug(f"Available widgets: {list(self.ui_manager.widgets.keys())}")
        except Exception as e:
            logger.error(f"更新日期字段失败: {e}")

    def _set_readonly_mode(self, readonly: bool):
        # 使用UI管理器设置只读模式
        if readonly:
            self.ui_manager.disable_all_inputs()
        else:
            self.ui_manager.enable_all_inputs()
        self._readonly_mode = readonly

    # ----------- 数据操作 -----------
    def collect_diary_data(self) -> dict:
        return self.ui_manager.collect_diary_data()

    def save_diary(self):
        try:
            data = self.collect_diary_data()
            if not self.validator.validate_health_record(self._prepare_record_data(data)):
                return
            if self.db_manager.save_health_record(self._prepare_record_data(data)):
                show_toast("保存成功")
                # 设置刷新并导航
                self._navigate_after_save()
            else:
                self._save_to_json_backup(data)
        except Exception as e:
            logger.error(f"保存失败: {e}")
            show_toast("保存失败")

    def _prepare_record_data(self, data: Dict) -> Dict:
        """准备记录数据以供验证和保存"""
        record_data = {
            'record_date': data.get('date', ''),
            'recordtype': 'health_diary',
            'record_subtype': 'daily_log',
            'description': '健康日记',
            'weight': data.get('weight', None),
            'blood_pressure_systolic': None,
            'blood_pressure_diastolic': None,
            'blood_sugar': None,
            'notes_data': json.dumps({
                'condition': data.get('condition', ''),
                'blood_pressure': data.get('blood_pressure', []),
                'blood_sugar': data.get('blood_sugar', []),
                'exercise': data.get('exercise', ''),
                'sleep': data.get('sleep', ''),
                'weight_status': data.get('weight_status', '')
            }, ensure_ascii=False)
        }
        
        # 处理血压数据
        blood_pressure_records = data.get('blood_pressure', [])
        if blood_pressure_records:
            first_record = blood_pressure_records[0]
            record_data['blood_pressure_systolic'] = first_record.get('systolic')
            record_data['blood_pressure_diastolic'] = first_record.get('diastolic')
        
        # 处理血糖数据
        blood_sugar_records = data.get('blood_sugar', [])
        if blood_sugar_records:
            first_record = blood_sugar_records[0]
            record_data['blood_sugar'] = first_record.get('value')
            
        return record_data

    def _navigate_after_save(self):
        """保存后导航到健康日记屏幕"""
        try:
            if self.manager and hasattr(self.manager, 'get_screen'):
                diary_screen = self.manager.get_screen('health_diary_screen')
                if hasattr(diary_screen, 'needs_refresh'):
                    diary_screen.needs_refresh = True
                self.manager.current = 'health_diary_screen'
        except Exception as e:
            logger.error(f"导航失败: {e}")

    def navigate_to_health_diary(self, *args):
        """导航到健康日记 - 支持*args参数"""
        try:
            if self.manager:
                self.manager.current = 'health_diary_screen'
        except Exception as e:
            logger.error(f"导航失败: {e}")
            show_toast(f"导航失败: {str(e)}")

    def _save_to_json_backup(self, data: Dict):
        """保存数据到JSON备份文件"""
        try:
            app = App.get_running_app()
            user_dir = getattr(app, "user_data_dir", ".")
            backup_path = os.path.join(user_dir, "health_diary_backup.json")
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
            backup_data = []
            if os.path.exists(backup_path):
                with open(backup_path, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)
            backup_data.append({'timestamp': datetime.now().isoformat(), 'data': data})
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)
            logger.info("数据已保存到JSON备份文件")
        except Exception as e:
            logger.error(f"保存JSON备份失败: {e}")
            show_toast("本地备份保存失败")

    def load_diary_for_edit(self, diary_data: Dict):
        """加载现有日记数据进行编辑"""
        try:
            logger.debug(f"load_diary_for_edit called, _initialized: {self._initialized}")
            self._current_diary_data = diary_data
            
            # 设置编辑模式标志
            self._is_edit_mode = True
            
            # 设置日期 - 确保日期为字符串类型
            diary_date = diary_data.get('diary_date', '')
            if diary_date:
                # 确保diary_date是字符串类型
                if hasattr(diary_date, 'strftime'):  # 如果是datetime或date对象
                    diary_date_str = diary_date.strftime('%Y-%m-%d')
                elif isinstance(diary_date, str):
                    diary_date_str = diary_date
                else:
                    diary_date_str = str(diary_date)
                
                self.selected_date = diary_date_str
                # 更新UI上的日期显示，确保date_field存在
                if "date_field" in self.ui_manager.widgets:
                    self.ui_manager.widgets["date_field"].text = diary_date_str
                else:
                    logger.warning("date_field not found in UI manager widgets during load_diary_for_edit")
                    logger.debug(f"Available widgets: {list(self.ui_manager.widgets.keys())}")
            
            # 解析content字段中的JSON数据
            content = diary_data.get('content', '')
            content_data = {}
            if content:
                if isinstance(content, str):
                    try:
                        content_data = json.loads(content)
                    except json.JSONDecodeError as e:
                        logger.error(f"解析日记内容JSON失败: {e}")
                        content_data = {}
                else:
                    content_data = content
            
            # 清空现有数据，但不清空记录数据（因为后面会重新加载）
            self.ui_manager.clear_all_data(clear_records=False)
            
            # 重新设置日期（因为clear_all_data会重置为今天），确保date_field存在
            if "date_field" in self.ui_manager.widgets:
                self.ui_manager.widgets["date_field"].text = self.selected_date
            else:
                logger.warning("date_field not found in UI manager widgets during date reset")
                logger.debug(f"Available widgets: {list(self.ui_manager.widgets.keys())}")
            
            # 确保清空所有记录数据后再加载新数据
            self.blood_pressure_records = []
            self.blood_sugar_records = []
            
            # 加载各种数据
            self.ui_manager.load_condition_data(content_data)
            self.ui_manager.load_weight_data(content_data)
            self.ui_manager.load_blood_pressure_data(content_data)
            self.ui_manager.load_blood_sugar_data(content_data)
            self.ui_manager.load_exercise_data(content_data)
            self.ui_manager.load_sleep_data(content_data)
            
            # 强制更新图表
            self.ui_manager.update_blood_pressure_chart()
            self.ui_manager.update_blood_sugar_chart()
            
            logger.info(f"加载编辑数据成功: {diary_date}")
        except Exception as e:
            logger.error(f"加载编辑数据失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            show_toast("加载数据失败")

    def _parse_date(self, diary_date_raw) -> Optional[date]:
        """解析日期数据"""
        if not diary_date_raw:
            return None
            
        if hasattr(diary_date_raw, 'strftime'):
            # datetime或date对象
            return diary_date_raw.date() if hasattr(diary_date_raw, 'date') else diary_date_raw
        
        if isinstance(diary_date_raw, str):
            return self._parse_date_string(diary_date_raw)
        
        # 其他类型，尝试转换为字符串后解析
        return datetime.strptime(str(diary_date_raw), '%Y-%m-%d').date()
    
    def _parse_date_string(self, date_str: str) -> date:
        """解析日期字符串"""
        # 尝试完整的日期时间格式
        try:
            return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S').date()
        except ValueError:
            pass
        
        # 尝试只有日期的格式
        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            pass
        
        # 尝试分割后取日期部分
        date_parts = date_str.split()
        if date_parts:
            return datetime.strptime(date_parts[0], '%Y-%m-%d').date()
        
        # 如果都失败，使用今天日期
        logger.warning(f"无法解析日期字符串: {date_str}，使用今天日期")
        return date.today()

    def _get_store(self) -> JsonStore:
        """获取状态存储"""
        try:
            app = App.get_running_app()
            user_dir = getattr(app, "user_data_dir", ".")
            path = os.path.join(user_dir, "ui_state.json")
            return JsonStore(path)
        except Exception as e:
            logger.error(f"获取存储失败: {e}")
            return JsonStore("ui_state.json")

    def _get_current_user_id(self) -> str:
        """
        获取当前用户ID
        优先级：user_data.json > 用户管理器 > 应用 > 主屏幕 > 默认值
        
        Returns:
            str: 当前用户的custom_id
        """
        try:
            # 按优先级尝试获取custom_id
            custom_id = self._get_custom_id_from_user_data()
            if custom_id:
                return custom_id
                
            custom_id = self._get_custom_id_from_user_manager()
            if custom_id:
                return custom_id
                
            custom_id = self._get_custom_id_from_app()
            if custom_id:
                return custom_id
                
            custom_id = self._get_custom_id_from_main_screen()
            if custom_id:
                return custom_id
            
            # 默认值
            logger.warning("无法获取custom_id，使用默认值: default_user")
            return "default_user"
        except Exception as e:
            logger.error(f"获取用户ID时发生错误: {e}")
            return "default_user"
    
    def _get_custom_id_from_user_data(self) -> str | None:
        """从user_data.json获取custom_id"""
        try:
            user_data_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'user_data.json')
            if not os.path.exists(user_data_path):
                return None
                
            with open(user_data_path, 'r', encoding='utf-8') as f:
                user_data = json.load(f)
                current_account = user_data.get('current_account')
                accounts = user_data.get('accounts', [])
                
                # 查找当前账户的custom_id
                for account in accounts:
                    if account.get('username') == current_account:
                        custom_id = account.get('custom_id')
                        if custom_id:
                            logger.info(f"从user_data.json获取到custom_id: {custom_id}")
                            return custom_id
                
                # 如果没有找到当前账户，使用第一个账户的custom_id
                if accounts and accounts[0].get('custom_id'):
                    custom_id = accounts[0]['custom_id']
                    logger.info(f"从user_data.json获取到第一个账户的custom_id: {custom_id}")
                    return custom_id
        except Exception as e:
            logger.debug(f"从user_data.json获取custom_id失败: {e}")
        return None
    
    def _get_custom_id_from_user_manager(self) -> str | None:
        """从用户管理器获取custom_id"""
        try:
            app = App.get_running_app()
            if app and hasattr(app, 'user_manager') and app.user_manager:
                custom_id = getattr(app.user_manager, 'custom_id', None)
                if custom_id:
                    logger.info(f"从用户管理器获取到custom_id: {custom_id}")
                    return custom_id
        except Exception as e:
            logger.debug(f"从用户管理器获取custom_id失败: {e}")
        return None
    
    def _get_custom_id_from_app(self) -> str | None:
        """从应用获取custom_id"""
        try:
            app = App.get_running_app()
            if app and hasattr(app, 'custom_id') and app.custom_id:
                logger.info(f"从应用获取到custom_id: {app.custom_id}")
                return app.custom_id
        except Exception as e:
            logger.debug(f"从应用获取custom_id失败: {e}")
        return None
    
    def _get_custom_id_from_main_screen(self) -> str | None:
        """从主屏幕获取custom_id"""
        try:
            app = App.get_running_app()
            if app and hasattr(app, 'root') and app.root and hasattr(app.root, 'get_screen'):
                main_screen = app.root.get_screen('main')
                if main_screen and hasattr(main_screen, 'custom_id') and main_screen.custom_id:
                    logger.info(f"从主屏幕获取到custom_id: {main_screen.custom_id}")
                    return main_screen.custom_id
        except Exception as e:
            logger.debug(f"从主屏幕获取custom_id失败: {e}")
        return None

    # ----------- 血压和血糖相关方法 -----------
    def add_blood_pressure_record(self):
        """添加血压记录 - 通过对话框方式"""
        try:
            # 导入对话框函数
            from mobile.screens.dialogs import open_add_blood_pressure_dialog
            
            def save_callback(form_data):
                """保存血压记录回调"""
                try:
                    # 添加新的血压记录到数据列表
                    self.blood_pressure_records.append(form_data)
                    
                    # 根据时间排序所有记录
                    self.blood_pressure_records.sort(key=lambda x: x.get('time', ''))
                    
                    # 更新血压图表
                    self.ui_manager.update_blood_pressure_chart()
                    

                except Exception as e:
                    logger.error(f"保存血压记录失败: {e}")
                    show_toast("保存血压记录失败")
            
            # 打开对话框
            open_add_blood_pressure_dialog(self, save_callback)
        except Exception as e:
            logger.error(f"打开血压记录对话框失败: {e}")
            show_toast("打开血压记录对话框失败")
    
    def add_blood_sugar_record(self):
        """添加血糖记录 - 通过对话框方式"""
        try:
            # 导入对话框函数
            from mobile.screens.dialogs import open_add_blood_sugar_dialog
            
            def save_callback(form_data):
                """保存血糖记录回调"""
                try:
                    # 添加新的血糖记录到数据列表
                    self.blood_sugar_records.append(form_data)
                    
                    # 根据时间排序所有记录
                    self.blood_sugar_records.sort(key=lambda x: x.get('time', ''))
                    
                    # 更新血糖图表
                    self.ui_manager.update_blood_sugar_chart()
                    

                except Exception as e:
                    logger.error(f"保存血糖记录失败: {e}")
                    show_toast("保存血糖记录失败")
            
            # 打开对话框
            open_add_blood_sugar_dialog(self, save_callback)
        except Exception as e:
            logger.error(f"打开血糖记录对话框失败: {e}")
            show_toast("打开血糖记录对话框失败")

    # ----------- 下拉菜单相关方法 -----------
    def show_blood_sugar_time_menu(self, dropdown_item):
        """显示血糖时间选择菜单"""
        try:
            time_options = ["早餐前", "早餐后2小时", "午餐前", "午餐后2小时", "晚餐前", "晚餐后2小时", "睡前", "随机"]
            menu_items = []
            for time in time_options:
                # 使用默认参数确保正确捕获变量
                menu_items.append({
                    "text": time,
                    "on_release": lambda t=time: self.set_blood_sugar_time(dropdown_item, t)
                })
            self.blood_sugar_time_menu = MDDropdownMenu(
                caller=dropdown_item,
                items=menu_items,
                position="bottom",
                width=dp(240),
                max_height=dp(200)
            )
            self.blood_sugar_time_menu.open()
        except Exception as e:
            logger.error(f"显示血糖时间菜单失败: {e}")

    def show_blood_pressure_time_menu(self, dropdown_item):
        """显示血压时间选择菜单"""
        try:
            time_options = ["早晨", "上午", "中午", "下午", "晚上", "睡前", "随机"]
            menu_items = []
            for time in time_options:
                # 使用默认参数确保正确捕获变量
                menu_items.append({
                    "text": time,
                    "on_release": lambda t=time: self.set_blood_pressure_time(dropdown_item, t)
                })
            self.blood_pressure_time_menu = MDDropdownMenu(
                caller=dropdown_item,
                items=menu_items,
                position="bottom",
                width=dp(240),
                max_height=dp(200)
            )
            self.blood_pressure_time_menu.open()
        except Exception as e:
            logger.error(f"显示血压时间菜单失败: {e}")

    def set_blood_sugar_time(self, dropdown_item, time):
        """设置血糖时间"""
        try:
            # 查找并更新文本组件
            for child in dropdown_item.children:
                if hasattr(child, 'text'):
                    child.text = time
                    break
            if hasattr(self, 'blood_sugar_time_menu') and self.blood_sugar_time_menu:
                self.blood_sugar_time_menu.dismiss()
        except Exception as e:
            logger.error(f"设置血糖时间失败: {e}")

    def set_blood_pressure_time(self, dropdown_item, time):
        """设置血压时间"""
        try:
            # 查找并更新文本组件
            for child in dropdown_item.children:
                if hasattr(child, 'text'):
                    child.text = time
                    break
            if hasattr(self, 'blood_pressure_time_menu') and self.blood_pressure_time_menu:
                self.blood_pressure_time_menu.dismiss()
        except Exception as e:
            logger.error(f"设置血压时间失败: {e}")

    def show_weight_status_menu(self, dropdown_item):
        """显示体重状态选择菜单"""
        try:
            status_options = ["空腹", "餐后"]
            menu_items = []
            for status in status_options:
                # 使用默认参数确保正确捕获变量
                menu_items.append({
                    "text": status,
                    "on_release": lambda s=status: self.set_weight_status(dropdown_item, s)
                })
            self.weight_status_menu = MDDropdownMenu(
                caller=dropdown_item,
                items=menu_items,
                position="bottom",
                width=dp(240),
                max_height=dp(200)
            )
            self.weight_status_menu.open()
        except Exception as e:
            logger.error(f"显示体重状态菜单失败: {e}")

    def set_weight_status(self, dropdown_item, status):
        """设置体重状态"""
        try:
            # 查找并更新文本组件
            for child in dropdown_item.children:
                if hasattr(child, 'text'):
                    child.text = status
                    break
            if hasattr(self, 'weight_status_menu') and self.weight_status_menu:
                self.weight_status_menu.dismiss()
        except Exception as e:
            logger.error(f"设置体重状态失败: {e}")

    # ----------- 其他方法 -----------
    def cancel_diary(self, *args):
        """取消创建健康日记 - 支持*args参数"""
        try:
            logger.info("[HealthDiary] 取消创建健康日记")
            show_toast("已取消")
        except Exception as e:
            logger.error(f"取消操作失败: {e}")

    def refresh_data(self, *args):
        """刷新数据 - 支持*args参数"""
        try:
            if self._current_diary_data:  # 统一命名为 _current_diary_data 而不是 _edit_diary_data
                # 重新加载编辑数据
                self.load_diary_for_edit(self._current_diary_data)
                show_toast("数据已刷新")
            logger.info("数据刷新完成")
        except Exception as e:
            logger.error(f"刷新数据失败: {e}")
            show_toast("刷新失败")