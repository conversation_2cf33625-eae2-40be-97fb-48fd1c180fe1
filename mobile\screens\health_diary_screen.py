"""健康日记屏幕模块 - 重构版

提供健康日记的列表展示、查看、删除等功能。
遵循KivyMD 2.0.1 dev0规范以及theme.py配置。
完全继承BaseScreen基类，遵循UI页面改造规范。
"""

import os
import logging
import json
from datetime import datetime, date
from typing import Dict, Any, List, Optional

from kivy.app import App
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.properties import StringProperty, BooleanProperty
from kivy.lang import Builder

from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.label import MDLabel
from kivymd.uix.dialog import MDDialog
from kivymd.uix.card import MDCard
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText

# 导入KivyMD 2.0.1对话框组件
try:
    from kivymd.uix.dialog import (
        MDDialogHeadlineText,
        MDDialogSupportingText,
        MDDialogButtonContainer
    )
except ImportError:
    # 如果导入失败，使用简单的MDDialog
    MDDialogHeadlineText = None
    MDDialogSupportingText = None
    MDDialogButtonContainer = None

# 导入基础屏幕和主题
from .base_screen import BaseScreen

try:
    from theme import AppTheme, AppMetrics, FontStyles
except Exception:
    AppTheme = None
    AppMetrics = None
    FontStyles = None

# 导入数据库管理器
try:
    from utils.unified_db_models import UnifiedDBManager
except ImportError:
    UnifiedDBManager = None

# 导入健康日记API
try:
    from utils.health_diary_api import get_health_diary_api
    HealthDiaryAPI = get_health_diary_api()
except ImportError:
    HealthDiaryAPI = None

# 导入健康日记工具脚本
try:
    from mobile.utils.health_diary_validator import HealthDiaryValidator
    HEALTH_DIARY_VALIDATOR_AVAILABLE = True
    logging.info("健康日记验证器导入成功")
except ImportError as e:
    logging.warning(f"健康日记验证器导入失败: {e}")
    HEALTH_DIARY_VALIDATOR_AVAILABLE = False
    HealthDiaryValidator = None

try:
    from mobile.utils.health_diary_ui_manager import HealthDiaryUIManager
    HEALTH_DIARY_UI_MANAGER_AVAILABLE = True
    logging.info("健康日记UI管理器导入成功")
except ImportError as e:
    logging.warning(f"健康日记UI管理器导入失败: {e}")
    HEALTH_DIARY_UI_MANAGER_AVAILABLE = False
    HealthDiaryUIManager = None

try:
    from mobile.utils.health_diary_data_manager import HealthDiaryDataManager
    HEALTH_DIARY_DATA_MANAGER_AVAILABLE = True
    logging.info("健康日记数据管理器导入成功")
except ImportError as e:
    logging.warning(f"健康日记数据管理器导入失败: {e}")
    HEALTH_DIARY_DATA_MANAGER_AVAILABLE = False
    HealthDiaryDataManager = None

# 导入新的健康日记列表管理器
try:
    from mobile.utils.health_diary_list_ui_manager import HealthDiaryListUIManager
    HEALTH_DIARY_LIST_UI_MANAGER_AVAILABLE = True
    logging.info("健康日记列表UI管理器导入成功")
except ImportError as e:
    logging.warning(f"健康日记列表UI管理器导入失败: {e}")
    HEALTH_DIARY_LIST_UI_MANAGER_AVAILABLE = False
    HealthDiaryListUIManager = None

try:
    from mobile.utils.health_diary_list_data_manager import HealthDiaryListDataManager
    HEALTH_DIARY_LIST_DATA_MANAGER_AVAILABLE = True
    logging.info("健康日记列表数据管理器导入成功")
except ImportError as e:
    logging.warning(f"健康日记列表数据管理器导入失败: {e}")
    HEALTH_DIARY_LIST_DATA_MANAGER_AVAILABLE = False
    HealthDiaryListDataManager = None

# 导入健康日记卡片组件
try:
    from mobile.widgets.health_diary_card import HealthDiaryCard
    HEALTH_DIARY_CARD_AVAILABLE = True
    logging.info("健康日记卡片组件导入成功")
except ImportError as e:
    logging.warning(f"健康日记卡片组件导入失败: {e}")
    HEALTH_DIARY_CARD_AVAILABLE = False
    HealthDiaryCard = None

# 设置日志
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 定义KV语言字符串 - 简化版，只设置基本属性
KV = '''
<HealthDiaryScreen>:
    name: 'health_diary_screen'
'''

# 注册KV字符串
Builder.load_string(KV)


class HealthDiaryScreen(BaseScreen):
    """健康日记屏幕 - 完全继承BaseScreen基类"""

    def __init__(self, **kwargs):
        # 设置页面标题和导航栏属性
        kwargs['screen_title'] = '健康日记'
        kwargs['show_top_bar'] = True
        kwargs['top_bar_action_icon'] = 'plus'

        super().__init__(**kwargs)

        self.app = MDApp.get_running_app()
        self.db_manager = None
        self.health_diary_api = None
        self.diary_api = None
        self.diary_entries = []
        self.diary_data = []
        self.selected_items = []
        self.long_press_mode = False
        self.needs_refresh = True  # 标记是否需要刷新数据

        # 添加初始化状态标记
        self._ui_initialized = False
        self._content_setup = False

        # 初始化健康日记工具脚本
        self._init_health_diary_tools()

        # 初始化diary_api
        if HealthDiaryAPI:
            self.diary_api = HealthDiaryAPI
            logger.info("[HealthDiaryScreen] diary_api初始化成功")
        else:
            logger.warning("[HealthDiaryScreen] HealthDiaryAPI不可用，diary_api初始化失败")
    
    def _init_health_diary_tools(self):
        """初始化健康日记工具脚本"""
        # 初始化数据管理器
        self.data_manager = None
        if HEALTH_DIARY_LIST_DATA_MANAGER_AVAILABLE and HealthDiaryListDataManager is not None:
            try:
                # 获取当前用户ID
                current_user_id = self._get_current_user_id()
                self.data_manager = HealthDiaryListDataManager(custom_id=current_user_id)
                logger.info(f"[HealthDiaryScreen] 健康日记列表数据管理器初始化成功，custom_id: {current_user_id}")
            except Exception as e:
                logger.error(f"[HealthDiaryScreen] 健康日记列表数据管理器初始化失败: {e}")
                self.data_manager = None
        
        # 初始化验证器
        self.validator = None
        if HEALTH_DIARY_VALIDATOR_AVAILABLE and HealthDiaryValidator is not None:
            try:
                self.validator = HealthDiaryValidator()
                logger.info("[HealthDiaryScreen] 健康日记验证器初始化成功")
            except Exception as e:
                logger.error(f"[HealthDiaryScreen] 健康日记验证器初始化失败: {e}")
                self.validator = None
        
        # 初始化UI管理器
        self.ui_manager = None
        if HEALTH_DIARY_LIST_UI_MANAGER_AVAILABLE and HealthDiaryListUIManager is not None:
            try:
                self.ui_manager = HealthDiaryListUIManager(self)
                logger.info("[HealthDiaryScreen] 健康日记列表UI管理器初始化成功")
            except Exception as e:
                logger.error(f"[HealthDiaryScreen] 健康日记列表UI管理器初始化失败: {e}")
                self.ui_manager = None
    
    def _get_theme_color(self, color_name):
        """安全获取主题颜色"""
        try:
            app = MDApp.get_running_app()
            if app is not None and hasattr(app, 'theme') and app.theme is not None:
                return getattr(app.theme, color_name, self._get_fallback_color(color_name))
            else:
                return self._get_fallback_color(color_name)
        except Exception:
            return self._get_fallback_color(color_name)
    
    def _get_fallback_color(self, color_name):
        """获取fallback颜色"""
        fallback_colors = {
            'primary': [0.129, 0.588, 0.953, 1],
            'primaryColor': [0.129, 0.588, 0.953, 1],
            'secondary': [1.0, 0.251, 0.505, 1],
            'accent': [1.0, 0.251, 0.505, 1],
            'surface': [1.0, 1.0, 1.0, 1],
            'background': [0.98, 0.98, 0.98, 1],
            'error': [0.957, 0.263, 0.212, 1],
            'success': [0.298, 0.686, 0.314, 1],
            'warning': [1.0, 0.596, 0.0, 1],
            'info': [0.129, 0.588, 0.953, 1],
            'text_primary': [0.129, 0.129, 0.129, 1],
            'text_secondary': [0.459, 0.459, 0.459, 1],
            'surfaceContainerHighestColor': [0.95, 0.95, 0.95, 1],
            'onSurfaceVariantColor': [0.3, 0.3, 0.3, 1],
            'primaryContainerColor': [0.8, 0.9, 1.0, 1],
            'onPrimaryContainerColor': [0.1, 0.3, 0.5, 1],
            'onPrimaryColor': [1.0, 1.0, 1.0, 1],
            'errorColor': [0.9, 0.2, 0.2, 1],
            'outlineColor': [0.7, 0.7, 0.7, 1],
        }
        return fallback_colors.get(color_name, [0.129, 0.588, 0.953, 1])
    
    def do_content_setup(self):
        """在content_container中添加内容"""
        try:
            # 安全地获取content_container
            content_container = None
            if hasattr(self, 'ids') and isinstance(self.ids, dict):
                content_container = self.ids.get('content_container')

            if not content_container:
                logger.error("[HealthDiaryScreen] ERROR: 无法找到content_container")
                return

            # 清空content_container中的现有内容
            content_container.clear_widgets()

            # 创建主布局
            main_layout = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=int(self.height if self.height > 0 else dp(800)),
                padding=[int(dp(16)), int(dp(16)), int(dp(16)), int(dp(100))],
                spacing=int(dp(20))
            )
            main_layout.bind(minimum_height=main_layout.setter('height'))

            # 将主布局添加到content_container
            content_container.add_widget(main_layout)

            # 创建日记列表UI
            self.create_diary_list_ui(main_layout)

            # 延迟加载健康日记数据
            Clock.schedule_once(lambda dt: self.load_diary_data(), 0.2)

            logger.info("[HealthDiaryScreen] 成功添加内容到content_container")
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 添加内容到content_container失败: {e}")
            import traceback
            traceback.print_exc()
    
    def create_diary_list_ui(self, main_layout):
        """创建健康日记列表UI"""
        try:
            # 创建日记列表容器
            diary_container = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                spacing=int(dp(12))
            )
            diary_container.bind(minimum_height=diary_container.setter('height'))

            # 添加到主布局
            main_layout.add_widget(diary_container)

            # 存储容器引用以便后续更新
            self.diary_container = diary_container

            logger.info("[HealthDiaryScreen] 健康日记列表UI创建完成")
            return True
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 创建健康日记列表UI失败: {e}")
            return False
    
    def create_diary_card(self, diary_data):
        """创建健康日记卡片"""
        try:
            # 获取主题颜色
            app = MDApp.get_running_app()
            theme = getattr(app, 'theme', None) if app else None
            card_bg_color = getattr(theme, 'CARD_BACKGROUND', [1, 1, 1, 1]) if theme else [1, 1, 1, 1]

            # 创建卡片
            card = MDCard(
                orientation='vertical',
                size_hint_y=None,
                height=dp(120),
                padding=[int(dp(16)), int(dp(16)), int(dp(16)), int(dp(16))],
                spacing=int(dp(8)),
                radius=[int(dp(16))],
                elevation=2,
                md_bg_color=card_bg_color,
                on_release=lambda x: self.navigate_to_detail(diary_data)
            )

            # 添加日期标题
            date_label = MDLabel(
                text=diary_data.get('diary_date', '未知日期'),
                font_style="Title",
                role="medium",
                size_hint_y=None,
                height=dp(24)
            )
            card.add_widget(date_label)

            # 添加内容预览
            content_preview = self._get_content_preview(diary_data.get('content', ''))
            content_label = MDLabel(
                text=content_preview,
                font_style="Body",
                role="medium",
                size_hint_y=None,
                height=dp(60)
            )
            card.add_widget(content_label)

            return card
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 创建健康日记卡片失败: {e}")
            return None

    def _load_data_via_api(self):
        """通过API加载数据"""
        logger.info("[HealthDiaryScreen] 使用diary_api加载数据")
        try:
            # 使用API获取数据
            if self.diary_api is not None:
                self.diary_entries = self.diary_api.get_diary_entries(limit=100)
                logger.info(f"[HealthDiaryScreen] 从diary_api加载到 {len(self.diary_entries)} 条记录")
                
                # 转换为diary_data格式以兼容现有显示逻辑
                self.diary_data = self._convert_entries_to_diary_format(self.diary_entries)
                return True
            else:
                logger.error("[HealthDiaryScreen] diary_api为None")
                return False
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 从diary_api加载数据失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.diary_entries = []
            self.diary_data = []
            return False

    def _load_data_via_data_manager(self):
        """通过数据管理器加载数据"""
        logger.info("[HealthDiaryScreen] diary_api不可用，使用健康日记数据管理器加载数据")
        try:
            # 获取当前用户ID
            self._get_current_user_id()
            
            # 获取最近的健康记录（可以调整数量）
            if self.data_manager is not None:
                self.diary_data = self.data_manager.get_all_health_records(limit=100)
                logger.info("[HealthDiaryScreen] 从数据管理器加载到 {} 条记录".format(len(self.diary_data)))
                return True
            else:
                logger.error("[HealthDiaryScreen] data_manager为None")
                return False
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 从数据管理器加载数据失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.diary_data = []
            return False

    def _load_data_via_fallback(self):
        """备用数据加载方法"""
        logger.warning("[HealthDiaryScreen] diary_api和数据管理器都未初始化")
        # 如果都不可用，设置为空数据
        self.diary_entries = []
        self.diary_data = []
        return True

    def load_diary_data(self):
        """加载健康日记数据"""
        try:
            logger.info("[HealthDiaryScreen] 开始加载健康日记数据")
            
            # 根据可用的工具选择加载方法
            if self.diary_api:
                success = self._load_data_via_api()
            elif self.data_manager:
                success = self._load_data_via_data_manager()
            else:
                success = self._load_data_via_fallback()
            
            if not success:
                self.show_error_message("加载数据失败，请尝试刷新")
                return
            
            # 更新日记列表显示
            self.update_diary_list()
            
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 加载健康日记数据失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error_message("加载数据失败，请尝试刷新")
    
    def _convert_entries_to_diary_format(self, entries: List[Dict]) -> List[Dict]:
        """将diary_api返回的条目格式转换为健康日记屏幕的格式
        
        Args:
            entries: diary_api返回的条目列表
            
        Returns:
            List[Dict]: 转换后的日记数据列表
        """
        diary_data = []
        try:
            for entry in entries:
                # diary_api返回的数据格式已经是标准格式，直接使用
                diary_entry = {
                    'id': entry.get('id', 0),
                    'custom_id': entry.get('custom_id', ''),
                    'diary_date': entry.get('diary_date', ''),  # 修复：使用正确的字段名
                    'diary_type': entry.get('diary_type', 'health'),  # 使用API返回的类型
                    'title': entry.get('title', '健康日记'),
                    'content': entry.get('content', '{}'),  # 直接使用API返回的content字段
                    'created_at': entry.get('created_at', ''),
                    'updated_at': entry.get('updated_at', ''),
                }
                
                # 调试日志：记录转换后的数据
                logger.debug("[HealthDiaryScreen] API条目转换: ID={}, 日期={}, 内容长度={}".format(diary_entry['id'], diary_entry['diary_date'], len(diary_entry['content'])))
                
                diary_data.append(diary_entry)
                
            return diary_data
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 转换条目格式失败: {e}")
            return []
    
    def _get_content_preview(self, content):
        """获取内容预览"""
        try:
            if isinstance(content, str):
                # 如果是JSON字符串，尝试解析
                try:
                    content_dict = json.loads(content)
                    # 提取主要内容字段并格式化显示
                    preview_parts = []

                    # 定义字段映射和单位
                    field_mapping = {
                        'symptoms': {'label': '', 'unit': ''},  # 症状不需要标签和单位
                        'blood_pressure': {'label': 'BP', 'unit': 'mmHg'},
                        'heart_rate': {'label': 'HR', 'unit': '次/分'},
                        'weight': {'label': 'WT', 'unit': 'Kg'},
                        'temperature': {'label': 'T', 'unit': '℃'},
                        'blood_sugar': {'label': 'BS', 'unit': 'mmol/L'},
                        'sleep_hours': {'label': '睡眠', 'unit': '小时'},
                        'exercise_duration': {'label': '运动', 'unit': '分钟'},
                        'mood': {'label': '心情', 'unit': ''},
                        'notes': {'label': '备注', 'unit': ''}
                    }

                    for key, value in content_dict.items():
                        if value and str(value).strip():
                            mapping = field_mapping.get(key, {'label': key, 'unit': ''})

                            if key == 'symptoms':
                                # 症状直接显示，不加标签
                                preview_parts.append(str(value))
                            else:
                                # 其他字段按格式显示
                                label = mapping['label']
                                unit = mapping['unit']
                                if label and unit:
                                    preview_parts.append(f"{label} {value}{unit}")
                                elif label:
                                    preview_parts.append(f"{label} {value}")
                                else:
                                    preview_parts.append(str(value))

                    # 用分号和空格连接，符合医学记录习惯
                    preview = '; '.join(preview_parts)

                except json.JSONDecodeError:
                    preview = content
            else:
                preview = str(content)

            # 限制预览长度
            if len(preview) > 150:
                preview = preview[:150] + "..."

            return preview if preview else "暂无内容"
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 获取内容预览失败: {e}")
            return "内容解析失败"

    def update_diary_list(self):
        """更新日记列表显示"""
        try:
            logger.info("[HealthDiaryScreen] 开始更新日记列表，当前数据量: {}".format(len(self.diary_data)))

            # 获取日记容器
            if not hasattr(self, 'diary_container') or not self.diary_container:
                # 如果容器不存在，重新创建UI
                content_container = self.ids.get('content_container')
                if content_container and content_container.children:
                    main_layout = content_container.children[0]
                    self.create_diary_list_ui(main_layout)
                else:
                    logger.error("[HealthDiaryScreen] 无法找到主布局")
                    return

            # 清空现有内容
            self.diary_container.clear_widgets()

            # 如果没有数据，显示空状态
            if not self.diary_data:
                self.show_empty_state()
                return

            # 创建日记卡片
            for diary_item in self.diary_data:
                card = self.create_diary_card(diary_item)
                if card:
                    self.diary_container.add_widget(card)

            logger.info("[HealthDiaryScreen] 日记列表更新完成")

        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 更新日记列表失败: {e}")
            import traceback
            traceback.print_exc()
            # 显示错误提示
            self.show_error_message("更新列表失败，请尝试刷新")
    
    def show_empty_state(self):
        """显示空状态界面"""
        try:
            # 创建空状态卡片
            empty_card = MDCard(
                orientation='vertical',
                size_hint_y=None,
                height=dp(200),
                padding=[int(dp(32)), int(dp(32)), int(dp(32)), int(dp(32))],
                spacing=int(dp(16)),
                radius=[int(dp(16))],
                elevation=1,
                md_bg_color=self._get_theme_color('surface')
            )

            # 添加空状态图标
            from kivymd.uix.label import MDIcon
            empty_icon = MDIcon(
                icon="book-open-variant",
                size_hint_y=None,
                height=dp(48),
                theme_icon_color="Custom",
                icon_color=self._get_theme_color('onSurfaceVariantColor'),
                halign="center"
            )
            empty_card.add_widget(empty_icon)

            # 添加空状态文本
            empty_label = MDLabel(
                text="暂无健康日记",
                font_style="Title",
                role="medium",
                size_hint_y=None,
                height=dp(32),
                halign="center",
                theme_text_color="Custom",
                text_color=self._get_theme_color('onSurfaceVariantColor')
            )
            empty_card.add_widget(empty_label)

            # 添加提示文本
            hint_label = MDLabel(
                text="点击右上角的 + 按钮开始记录您的健康日记",
                font_style="Body",
                role="medium",
                size_hint_y=None,
                height=dp(48),
                halign="center",
                theme_text_color="Custom",
                text_color=self._get_theme_color('onSurfaceVariantColor')
            )
            empty_card.add_widget(hint_label)

            # 添加到容器
            self.diary_container.add_widget(empty_card)

        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 显示空状态失败: {e}")
        

    def refresh_data(self):
        """刷新数据"""
        try:
            logger.info("[HealthDiaryScreen] 手动刷新健康日记数据")
            self.needs_refresh = True
            self.load_diary_data()
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 刷新数据失败: {e}")
            self.show_error_message("刷新失败，请重试")

    def enter_selection_mode(self, initial_card=None):
        """进入选择模式"""
        self.long_press_mode = True
        self.selected_items = []

        # 选中初始卡片
        if initial_card:
            initial_card.selected = True
            self.selected_items.append(initial_card.data)

    def exit_selection_mode(self, *args):
        """退出选择模式"""
        self.long_press_mode = False
        self.selected_items = []

    def delete_selected_items(self, *args):
        """删除选中的项目"""
        if not self.selected_items:
            return

        # 显示确认对话框
        self.show_delete_confirmation()
        
    def show_delete_confirmation(self):
        """显示删除确认对话框"""
        count = len(self.selected_items)
        
        # 获取应用和主题
        app = App.get_running_app()
        theme = getattr(app, 'theme', None) if app else None
        error_color = theme.ERROR_COLOR if theme else [1, 0, 0, 1]
        
        # 检查是否支持新的对话框组件
        if MDDialogHeadlineText and MDDialogSupportingText and MDDialogButtonContainer:
            # 使用KivyMD 2.0.1规范创建对话框
            dialog = MDDialog(
                MDDialogHeadlineText(text="确认删除"),
                MDDialogSupportingText(text=f"确定要删除选中的 {count} 条健康日记吗？此操作不可撤销。"),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="删除"),
                        style="filled",
                        theme_bg_color="Custom",
                        md_bg_color=error_color,
                        on_release=lambda x: self.confirm_delete(dialog)
                    )
                )
            )
        else:
            # 降级处理，使用简单的对话框
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(16),
                padding=dp(16),
                size_hint_y=None,
                height=dp(120)
            )
            
            title_label = MDLabel(
                text="确认删除",
                font_style="Headline",
                role="small",
                halign="center",
                size_hint_y=None,
                height=dp(30)
            )
            
            message_label = MDLabel(
                text=f"确定要删除选中的 {count} 条健康日记吗？此操作不可撤销。",
                font_style="Body",
                role="medium",
                halign="center",
                size_hint_y=None,
                height=dp(60)
            )
            
            content.add_widget(title_label)
            content.add_widget(message_label)
            
            # 创建按钮容器
            button_container = MDBoxLayout(
                orientation='horizontal',
                spacing=dp(8),
                size_hint_y=None,
                height=dp(30)
            )
            
            cancel_btn = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: dialog.dismiss()
            )
            
            delete_btn = MDButton(
                MDButtonText(text="删除"),
                style="filled",
                theme_bg_color="Custom",
                md_bg_color=error_color,
                on_release=lambda x: self.confirm_delete(dialog)
            )
            
            button_container.add_widget(cancel_btn)
            button_container.add_widget(delete_btn)
            content.add_widget(button_container)
            
            dialog = MDDialog(content)
        
        dialog.open()
        
    def show_single_delete_confirmation(self, card):
        """显示单个日记条目删除确认对话框"""
        # 获取应用和主题
        app = App.get_running_app()
        theme = getattr(app, 'theme', None) if app else None
        error_color = theme.ERROR_COLOR if theme else [1, 0, 0, 1]
        
        # 检查是否支持新的对话框组件
        if MDDialogHeadlineText and MDDialogSupportingText and MDDialogButtonContainer:
            # 使用KivyMD 2.0.1规范创建对话框
            dialog = MDDialog(
                MDDialogHeadlineText(text="确认删除"),
                MDDialogSupportingText(text=f"确定要删除 {card.diary_date} 的健康日记吗？此操作不可撤销。"),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: dialog.dismiss()
                    ),
                    MDButton(
                        MDButtonText(text="删除"),
                        style="filled",
                        theme_bg_color="Custom",
                        md_bg_color=error_color,
                        on_release=lambda x: self.confirm_single_delete(dialog, card)
                    )
                )
            )
        else:
            # 降级处理，使用简单的对话框
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(16),
                padding=dp(16),
                size_hint_y=None,
                height=dp(120)
            )
            
            title_label = MDLabel(
                text="确认删除",
                font_style="Headline",
                role="small",
                halign="center",
                size_hint_y=None,
                height=dp(30)
            )
            
            message_label = MDLabel(
                text=f"确定要删除 {card.diary_date} 的健康日记吗？此操作不可撤销。",
                font_style="Body",
                role="medium",
                halign="center",
                size_hint_y=None,
                height=dp(60)
            )
            
            content.add_widget(title_label)
            content.add_widget(message_label)
            
            # 创建按钮容器
            button_container = MDBoxLayout(
                orientation='horizontal',
                spacing=dp(8),
                size_hint_y=None,
                height=dp(30)
            )
            
            cancel_btn = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: dialog.dismiss()
            )
            
            delete_btn = MDButton(
                MDButtonText(text="删除"),
                style="filled",
                theme_bg_color="Custom",
                md_bg_color=error_color,
                on_release=lambda x: self.confirm_single_delete(dialog, card)
            )
            
            button_container.add_widget(cancel_btn)
            button_container.add_widget(delete_btn)
            content.add_widget(button_container)
            
            dialog = MDDialog(content)
        
        dialog.open()
        
    def confirm_delete(self, dialog):
        """确认删除操作"""
        try:
            # 执行删除操作
            deleted_count = 0
            
            for item in self.selected_items:
                if self.delete_diary_item(item):
                    deleted_count += 1
                    
            # 关闭对话框
            dialog.dismiss()
            
            # 退出选择模式
            self.exit_selection_mode()
            
            # 重新加载数据
            self.load_diary_data()
            
            # 显示成功消息
            self.show_success_message(f"已删除 {deleted_count} 条记录")
            
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 删除操作失败: {e}")
            dialog.dismiss()
            self.show_error_message("删除失败")
            
    def confirm_single_delete(self, dialog, card):
        """确认单个日记条目删除操作"""
        try:
            # 执行删除操作
            if self.delete_diary_item(card.data):
                # 关闭对话框
                dialog.dismiss()
                
                # 重新加载数据
                self.load_diary_data()
                
                # 显示成功消息
                self.show_success_message(f"已删除 {card.diary_date} 的健康日记")
            else:
                # 关闭对话框
                dialog.dismiss()
                self.show_error_message("删除失败")
                
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 删除操作失败: {e}")
            dialog.dismiss()
            self.show_error_message("删除失败")
            
    def delete_diary_item(self, item: Dict[str, Any]) -> bool:
        """删除单个日记项目"""
        try:
            diary_id = item.get('id')
            if not diary_id:
                return False
                
            # 使用HealthDiaryListDataManager删除
            if self.data_manager:
                try:
                    # 使用数据管理器的删除方法
                    result = self.data_manager.delete_health_record(diary_id)
                    if result:
                        logger.info(f"[HealthDiaryScreen] 使用数据管理器成功删除日记项目: {diary_id}")
                        return True
                    else:
                        logger.error(f"[HealthDiaryScreen] 使用数据管理器删除日记项目失败: {diary_id}")
                        return False
                except Exception as e:
                    logger.error(f"[HealthDiaryScreen] 使用数据管理器删除日记项目异常: {e}")
                    return False
            else:
                logger.warning("[HealthDiaryScreen] 健康日记数据管理器未初始化")
                return False
            
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 删除日记项目失败: {e}")
            return False
            
    def on_action(self):
        """处理顶部操作按钮点击（添加按钮）"""
        self.add_diary()
        
    def _ensure_create_screen_loaded(self, screen_manager):
        """确保健康日记创建屏幕已加载"""
        # 检查屏幕是否已存在，如果不存在则懒加载
        if not screen_manager.has_screen('health_diary_create_screen'):
            try:
                from screens.health_diary_create_screen import HealthDiaryCreateScreen
                create_screen = HealthDiaryCreateScreen(name='health_diary_create_screen')
                screen_manager.add_widget(create_screen)
                logger.info("[HealthDiaryScreen] 健康日记创建屏幕已懒加载")
                return screen_manager.get_screen('health_diary_create_screen')
            except ImportError as e:
                logger.error(f"[HealthDiaryScreen] 导入健康日记创建屏幕失败: {e}")
                self.show_error_message("页面加载失败")
                return None
        return screen_manager.get_screen('health_diary_create_screen')

    def _find_todays_diary_via_api(self, today):
        """通过API查找今天的日记"""
        try:
            # 使用API查询今天的日记
            if self.diary_api is not None:
                entries = self.diary_api.get_diary_entries()
                if entries:
                    # 查找今天的日记
                    for entry in entries:
                        entry_date = entry.get('diary_date', '')
                        if entry_date == today or entry_date.startswith(today):
                            logger.info(f"[HealthDiaryScreen] 通过API找到今天的日记: {entry.get('id')}")
                            return entry
            else:
                logger.warning("[HealthDiaryScreen] diary_api为None")
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 使用API检查现有日记失败: {e}")
        return None

    def _find_todays_diary_via_data_manager(self, today):
        """通过数据管理器查找今天的日记"""
        try:
            # 使用健康日记数据管理器查询今天的日记
            if self.data_manager is not None:
                from datetime import datetime
                today_date = datetime.strptime(today, "%Y-%m-%d").date()
                records = self.data_manager.get_health_records_by_date_range(today_date, today_date)
                if records:
                    # 取第一条记录
                    existing_diary = records[0] if len(records) > 0 else None
                    logger.info("[HealthDiaryScreen] 通过数据管理器找到今天的日记")
                    return existing_diary
            else:
                logger.warning("[HealthDiaryScreen] data_manager为None")
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 使用数据管理器检查现有日记失败: {e}")
        return None

    def _find_todays_diary(self, today):
        """查找今天的日记"""
        existing_diary = None
        
        # 优先使用diary_api检查
        if self.diary_api and self.diary_api.is_available():
            existing_diary = self._find_todays_diary_via_api(today)
        
        # 如果API不可用，使用数据管理器
        if not existing_diary and self.data_manager:
            existing_diary = self._find_todays_diary_via_data_manager(today)
        elif not self.data_manager:
            logger.warning("[HealthDiaryScreen] 健康日记数据管理器未初始化")
            
        return existing_diary

    def _setup_diary_mode(self, create_screen, existing_diary):
        """设置日记模式（编辑或新建）"""
        # 修改：总是允许创建新日记，不管是否是今天的日记
        if existing_diary:
            # 如果已创建日记，直接进入编辑模式
            logger.info("[HealthDiaryScreen] 已创建日记，进入编辑模式")
            self.show_info_message("已创建日记，进入编辑模式")
            
            # 设置编辑模式为可编辑（今天日记）
            if hasattr(create_screen, 'set_edit_mode'):
                create_screen.set_edit_mode(existing_diary, is_readonly=False)
            else:
                logger.warning("[HealthDiaryScreen] 创建屏幕不支持编辑模式")
                # 清空数据并进入新建模式
                if hasattr(create_screen, 'clear_all_data'):
                    create_screen.clear_all_data()
        else:
            # 清空数据并进入新建模式
            logger.info("[HealthDiaryScreen] 进入新建模式")
            if hasattr(create_screen, 'clear_all_data'):
                create_screen.clear_all_data()

    def add_diary(self, *args):
        """添加新的健康日记"""
        try:
            logger.info("[HealthDiaryScreen] 导航到健康日记创建页面")
            
            # 获取屏幕管理器
            screen_manager = self.manager
            if not screen_manager:
                self.show_error_message("无法获取屏幕管理器")
                return
            
            # 确保创建屏幕已加载
            create_screen = self._ensure_create_screen_loaded(screen_manager)
            if not create_screen:
                return
            
            # 检查今天是否已创建日记
            from datetime import date
            today = date.today().strftime("%Y-%m-%d")
            
            # 查找今天的日记
            existing_diary = self._find_todays_diary(today)
            
            # 设置日记模式
            self._setup_diary_mode(create_screen, existing_diary)
            
            # 导航到健康日记创建页面
            screen_manager.current = 'health_diary_create_screen'
            
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 导航到健康日记创建页面失败: {e}")
            self.show_error_message("导航失败，请重试")
        
    def show_success_message(self, message: str):
        """显示成功消息"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
            )
            snackbar.open()
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 显示成功消息失败: {e}")
            
    def show_error_message(self, message: str):
        """显示错误消息"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
            )
            snackbar.open()
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 显示错误消息失败: {e}")
            
    def show_info_message(self, message: str):
        """显示信息消息"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
            )
            snackbar.open()
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 显示信息消息失败: {e}")

    def navigate_to_detail(self, data):
        """导航到详情页面（编辑模式）
        
        Args:
            data: 日记数据
        """
        try:
            logger.info(f"导航到日记详情页面: {data.get('id', 'unknown')}")
            
            # 获取屏幕管理器
            screen_manager = self.manager
            if not screen_manager:
                logger.error("无法获取屏幕管理器")
                self.show_error_message("无法获取屏幕管理器")
                return
            
            # 确保创建屏幕存在
            create_screen = self._ensure_create_screen_loaded(screen_manager)
            if not create_screen:
                return
            
            # 设置编辑模式
            if hasattr(create_screen, 'set_edit_mode'):
                # 检查日期是否为今天，决定是否为只读模式
                diary_date_str = data.get('diary_date', '')
                is_readonly = self._determine_readonly_mode(diary_date_str)
                create_screen.set_edit_mode(data, is_readonly)
            else:
                logger.warning("创建屏幕不支持编辑模式")
            
            # 导航到创建屏幕
            screen_manager.current = 'health_diary_create_screen'
            
        except Exception as e:
            logger.error(f"导航到详情页面失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.show_error_message("导航失败，请重试")

    def _determine_readonly_mode(self, diary_date_str):
        """确定是否为只读模式
        
        Args:
            diary_date_str: 日记日期字符串
            
        Returns:
            bool: 是否为只读模式
        """
        from datetime import date
        try:
            # 支持多种日期格式解析
            diary_date = self._parse_diary_date(diary_date_str)
            
            today = date.today()
            # 修改：今日日记也应该可以编辑，非今日日记才设为只读
            is_readonly = diary_date < today  # 今日及以后的日记都可以编辑
            
            logger.info(f"日记日期: {diary_date}, 今天: {today}, 只读模式: {is_readonly}")
            return is_readonly
        except Exception as e:
            logger.error(f"解析日期失败: {e}")
            return True  # 默认为只读

    def _parse_diary_date(self, diary_date_str):
        """解析日记日期
        
        Args:
            diary_date_str: 日记日期字符串
            
        Returns:
            date: 解析后的日期对象
        """
        from datetime import datetime, date
        # 检查日期字符串是否为空
        if not diary_date_str or not diary_date_str.strip():
            logger.warning("日期字符串为空，使用今天日期")
            return date.today()
        
        # 尝试完整的日期时间格式
        try:
            return datetime.strptime(diary_date_str, '%Y-%m-%d %H:%M:%S').date()
        except ValueError:
            try:
                # 尝试只有日期的格式
                return datetime.strptime(diary_date_str, '%Y-%m-%d').date()
            except ValueError:
                # 尝试分割后取日期部分（安全处理）
                date_parts = diary_date_str.split()
                if date_parts:
                    return datetime.strptime(date_parts[0], '%Y-%m-%d').date()
                else:
                    # 如果分割后没有内容，使用今天日期
                    logger.warning("无法解析日期字符串: {}，使用今天日期".format(diary_date_str))
                    return date.today()

    def on_enter(self, *args):
        """进入屏幕时调用"""
        try:
            logger.info("[HealthDiaryScreen] 进入健康日记屏幕")

            # 调用父类的on_enter方法
            super().on_enter(*args)

            # 强制校验登录状态
            app = MDApp.get_running_app()
            is_logged_in = False
            user_data = getattr(app, 'user_data', None)
            if user_data is not None and user_data.get('username'):
                try:
                    from utils.cloud_api import get_cloud_api
                    cloud_api = get_cloud_api()
                    if cloud_api and cloud_api.is_authenticated():
                        is_logged_in = True
                except Exception as e:
                    logger.error(f"检查cloud_api认证状态时出错: {e}")

            if not is_logged_in:
                # 未登录或认证无效，强制跳转回登录页并提示
                if self.manager:
                    self.manager.current = 'login_screen'
                snackbar = MDSnackbar(MDSnackbarText(text="请先登录"))
                snackbar.open()
                return

            # 每次进入都刷新数据，避免显示错误的缓存数据
            logger.info("[HealthDiaryScreen] 每次进入都刷新数据，避免缓存问题")
            self.load_diary_data()
            self.needs_refresh = False

            logger.info("[HealthDiaryScreen] 健康日记屏幕数据刷新完成")

        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 进入健康日记屏幕时出错: {e}")
            # 显示错误提示但不阻止屏幕显示
            self.show_error_message("数据加载失败，请尝试刷新")

    def mark_needs_refresh(self):
        """标记需要刷新数据"""
        self.needs_refresh = True
        logger.info("[HealthDiaryScreen] 标记健康日记数据需要刷新")
            
    def on_back(self):
        """返回处理"""
        try:
            logger.info("[HealthDiaryScreen] 健康日记页面返回")
            # 使用BaseScreen的默认返回处理
            super().on_back()
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 返回处理失败: {e}")
            # 降级处理：直接跳转到首页
            if self.manager:
                self.manager.current = 'homepage_screen'
    
    def _get_custom_id_from_user_data(self, user_data, source_name):
        """从用户数据中提取custom_id的通用方法"""
        custom_id = None
        
        # 统一处理字典类型和具有get方法的对象
        if isinstance(user_data, dict) or hasattr(user_data, 'get'):
            custom_id = user_data.get('custom_id')
            
        if custom_id:
            logger.info(f"[HealthDiaryScreen] 从{source_name}获取到custom_id: {custom_id}")
            return custom_id
            
        return None

    def _get_current_user_id(self) -> str:
        """获取当前登录用户ID"""
        try:
            # 尝试多种方式获取用户ID
            custom_id = self._try_get_user_id_from_file()
            if custom_id:
                return custom_id
                
            custom_id = self._try_get_user_id_from_manager()
            if custom_id:
                return custom_id
                
            custom_id = self._try_get_user_id_from_app()
            if custom_id:
                return custom_id
                
            custom_id = self._try_get_user_id_from_main_screen()
            if custom_id:
                return custom_id
            
            logger.warning("[HealthDiaryScreen] 无法获取有效的用户ID，使用默认值")
            return 'default_user'
        except Exception as e:
            logger.error(f"[HealthDiaryScreen] 获取用户ID失败: {e}")
            return 'default_user'
    
    def _try_get_user_id_from_file(self) -> Optional[str]:
        """尝试从user_data.json文件获取用户ID"""
        try:
            import json
            user_data_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'user_data.json')
            if not os.path.exists(user_data_file):
                return None
                
            with open(user_data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return self._extract_custom_id_from_file_data(data)
        except Exception as e:
            logger.warning(f"[HealthDiaryScreen] 从user_data.json获取用户ID失败: {e}")
            return None
    
    def _extract_custom_id_from_file_data(self, data: dict) -> Optional[str]:
        """从文件数据中提取custom_id"""
        current_account = data.get('current_account')
        accounts = data.get('accounts', [])
        
        # 查找当前账户的custom_id
        for account in accounts:
            if account.get('username') == current_account:
                custom_id = account.get('custom_id')
                if custom_id:
                    logger.info(f"[HealthDiaryScreen] 从user_data.json获取到custom_id: {custom_id}")
                    return custom_id
        
        # 如果没有找到当前账户，使用第一个账户的custom_id
        if accounts and accounts[0].get('custom_id'):
            custom_id = accounts[0]['custom_id']
            logger.info(f"[HealthDiaryScreen] 从user_data.json获取到第一个账户的custom_id: {custom_id}")
            return custom_id
        
        return None
    
    def _try_get_user_id_from_manager(self) -> Optional[str]:
        """尝试从用户管理器获取用户ID"""
        try:
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            if current_user and hasattr(current_user, 'custom_id') and current_user.custom_id:
                logger.info(f"[HealthDiaryScreen] 从用户管理器获取到custom_id: {current_user.custom_id}")
                return current_user.custom_id
        except Exception as e:
            logger.warning(f"[HealthDiaryScreen] 从用户管理器获取用户ID失败: {e}")
        return None
    
    def _try_get_user_id_from_app(self) -> Optional[str]:
        """尝试从应用获取用户ID"""
        from kivy.app import App
        app = App.get_running_app()
        if app is not None and hasattr(app, 'user_data') and app.user_data:
            custom_id = self._get_custom_id_from_user_data(app.user_data, "应用user_data")
            if custom_id:
                return custom_id
        return None
    
    def _try_get_user_id_from_main_screen(self) -> Optional[str]:
        """尝试从主屏幕获取用户ID"""
        if not (hasattr(self, 'manager') and self.manager and hasattr(self.manager, 'get_screen')):
            return None
            
        try:
            main_screen = self.manager.get_screen('main')
            if hasattr(main_screen, 'user_data') and main_screen.user_data:
                custom_id = self._get_custom_id_from_user_data(main_screen.user_data, "主屏幕")
                if custom_id:
                    return custom_id
        except Exception as e:
            logger.warning(f"[HealthDiaryScreen] 从主屏幕获取用户ID失败: {e}")
        return None