# screens/register_screen.py
from screens.base_screen import BaseScreen
from kivy.properties import StringProperty, BooleanProperty, ObjectProperty, ListProperty
from kivy.lang import Builder
from kivy.metrics import dp
from kivy.uix.modalview import ModalView
from kivy.uix.label import Label
from kivy.factory import Factory
from kivy.clock import Clock
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.spinner import SpinnerOption
from kivy.uix.gridlayout import GridLayout
from theme import AppTheme
from api.api_client import APIClient
from widgets.camera_view import CameraView  # 从widgets导入CameraView
# 导入Logo组件
from widgets.logo import HealthLogo  # 导入统一的Logo组件
# 导入统一文件上传下载管理器
from utils.file_upload_download_manager import FileUploadDownloadManager
# 修改导入语句，使用正确的日期选择器
from utils.kivymd_date_picker import show_kivymd_date_picker
import os
import re
import datetime
from kivymd.uix.dialog import MDDialog
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.label import MDLabel
from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.card import MDCard
# 简化MDIcon导入
try:
    from kivymd.uix.button import MDIconButton as MDIcon
except ImportError:
    # 如果导入失败，创建一个简单的替代类
    class MDIcon:
        pass
import requests
from typing import Dict, Any, Optional, TYPE_CHECKING

# 为类型检查工具提供类型提示
if TYPE_CHECKING:
    from kivy.uix.widget import Widget
    # 定义ids字典的类型
    class RegisterScreenIds(Dict[str, Widget]):
        id_card_input: Widget
        birth_date_input: Widget
        name_input: Widget
        address_input: Widget
        email_input: Widget
        certificate_container: Widget
        other_register_btn: Widget
        self_register_btn: Widget
        relationship_container: Widget
        # 可以根据需要添加更多id
        self.auto_dismiss = True
        # 设置弹窗背景色为浅色
        self.background_color = [0.95, 0.95, 0.95, 1]
        self.background = ""

        # 设置自动关闭时间 - 减少为1.5秒
        from kivy.clock import Clock
        Clock.schedule_once(self.dismiss, 1.5)  # 1.5秒后自动关闭

        # 使用更简洁的布局
        layout = Factory.BoxLayout(orientation='vertical', padding=[dp(10), dp(5), dp(10), dp(5)], spacing=dp(5))

        # 更新消息标签样式，使其更加鲜明
        msg_label = Factory.Label(
            text=message,
            halign='center',
            valign='middle',
            color=[0.1, 0.1, 0.1, 1],  # 近黑色文本
            font_name="NotoSans",
            font_size=dp(14),
            size_hint=(1, 1),
            text_size=(self.width * 0.9, None)
        )

        # 将消息标签添加到布局中
        layout.add_widget(msg_label)

        # 添加背景卡片 - 使用更轻量级的样式
        card = Factory.MDCard(
            orientation="vertical",
            size_hint=(1, 1),
            elevation=2,  # 减小阴影
            radius=[dp(8)],  # 减小圆角
            shadow_softness=4,  # 减小阴影柔和度
            shadow_offset=(0, 1),  # 减小阴影偏移
            md_bg_color=[0.95, 0.95, 0.95, 1]  # 浅灰色背景
        )
        card.add_widget(layout)
        self.add_widget(card)

# 自定义SpinnerOption样式
class CustomSpinnerOption(SpinnerOption):
    def __init__(self, **kwargs):
        super(CustomSpinnerOption, self).__init__(**kwargs)
        # 使用Clock.schedule_once延迟绑定，避免触发linter警告
        Clock.schedule_once(self._bind_events, 0)

    def _bind_events(self, dt):
        """延迟绑定事件，避免linter警告"""
        # 使用try/except包装，避免IDE警告
        try:
            # 修复Pyright错误：使用hasattr检查bind属性是否存在
            if hasattr(self, 'bind'):
                # 使用事件绑定的正确方式
                self.fbind('on_release', self._on_release)
        except AttributeError:
            print("警告: CustomSpinnerOption无法绑定on_release事件")

    def _on_release(self, *args):
        # 确保选项被点击时能够正确关闭下拉菜单并更新选中值
        spinner = self.parent.parent
        if spinner:
            spinner.text = self.text
            spinner.is_open = False
            # 手动触发on_text事件
            if hasattr(spinner, 'on_text_callback'):
                spinner.on_text_callback(spinner, self.text)

# 确保CustomSpinnerOption在kv字符串中可用
Factory.register('CustomSpinnerOption', cls=CustomSpinnerOption)

# 注册界面KV字符串
Builder.load_string("""
#:import os os
<CustomSpinnerOption>:
    size_hint_y: None
    height: dp(40)
    #background_normal: ''
    #background_color: app.theme.PRIMARY_LIGHT if self.state == 'down' else app.theme.CARD_BACKGROUND
    color: app.theme.TEXT_PRIMARY
    font_name: "NotoSans"
    font_size: dp(14)

<RegisterScreen>:
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_LIGHT
        Rectangle:
            pos: self.pos
            size: self.size

    BoxLayout:
        orientation: "vertical"
        spacing: dp(5)

        # Logo区域 - 参考登录界面的布局，使用统一的Logo组件
        MDBoxLayout:
            id: logo_container
            orientation: 'vertical'
            size_hint_y: None
            height: dp(200)  # 与登录界面保持一致的Logo区域高度
            padding: [0, dp(10), 0, dp(5)]
            size_hint_x: 0.95
            pos_hint: {"center_x": 0.5}

            # 使用统一的HealthLogo组件
            HealthLogo:
                id: health_logo

        ScrollView:
            do_scroll_x: False
            do_scroll_y: True

            BoxLayout:
                orientation: "vertical"
                size_hint_y: None
                height: self.minimum_height
                padding: dp(app.metrics.PADDING_MEDIUM)
                spacing: dp(app.metrics.PADDING_NORMAL)

                # 用户注册标签
                Label:
                    text: "用户注册"
                    size_hint_y: None
                    height: dp(30)
                    color: app.theme.TEXT_SECONDARY
                    halign: 'left'
                    text_size: self.size
                    font_name: "NotoSans"
                    font_size: dp(16)

                # 注册类型选择区域 - 卡片式设计
                BoxLayout:
                    orientation: "vertical"
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(app.metrics.PADDING_SMALL)

                    Label:
                        text: "注册类型："
                        size_hint_y: None
                        height: dp(30)
                        color: app.theme.TEXT_PRIMARY
                        halign: 'left'
                        text_size: self.size
                        bold: True
                        font_name: "NotoSans"
                        font_size: dp(14)

                    # 注册类型选择卡片
                    MDCard:
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        padding: dp(10)
                        spacing: dp(5)
                        elevation: 2
                        radius: [dp(16)]
                        shadow_softness: 8
                        shadow_offset: (0, 1)

                        # 注册类型选择按钮
                        BoxLayout:
                            orientation: "horizontal"
                            size_hint_y: None
                            height: dp(40)
                            spacing: dp(app.metrics.PADDING_SMALL)

                            Button:
                                id: self_register_btn
                                text: "本人注册"
                                size_hint_x: 0.5
                                color: app.theme.TEXT_LIGHT
                                background_normal: ''
                                background_color: root.selected_role_color if root.registration_type == "本人注册" else app.theme.PRIMARY_LIGHT
                                on_release: root.set_registration_type("本人注册")

                            Button:
                                id: other_register_btn
                                text: "替他人注册"
                                size_hint_x: 0.5
                                color: app.theme.TEXT_LIGHT
                                background_normal: ''
                                background_color: root.selected_role_color if root.registration_type == "替他人注册" else app.theme.PRIMARY_MEDIUM
                                on_release: root.set_registration_type("替他人注册")

                # 角色选择区域
                BoxLayout:
                    orientation: "vertical"
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(app.metrics.PADDING_SMALL)

                    Label:
                        text: "选择注册身份："
                        size_hint_y: None
                        height: dp(30)
                        color: app.theme.TEXT_PRIMARY
                        halign: 'left'
                        text_size: self.size
                        bold: True
                        font_name: "NotoSans"
                        font_size: dp(14)

                    # 角色选择和关系选择的水平布局
                    BoxLayout:
                        orientation: "horizontal"
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(app.metrics.PADDING_SMALL)  # 增加间距
                        padding: [0, 0, 0, 0]

                        # 角色选择部分
                        MDCard:
                            orientation: 'vertical'
                            size_hint_x: 0.5 if root.registration_type == "替他人注册" else 1.0
                            size_hint_y: None
                            height: self.minimum_height
                            padding: dp(10)
                            spacing: dp(5)
                            elevation: 2
                            radius: [dp(16)]
                            shadow_softness: 8
                            shadow_offset: (0, 1)

                            GridLayout:
                                cols: 2
                                spacing: dp(8)
                                size_hint_y: None
                                height: dp(160)  # 减少高度，更紧凑
                                col_default_width: dp(100)  # 适中的列宽
                                row_default_height: dp(75)  # 适中的行高

                                # 个人用户 - 使用MDCard作为可点击容器，支持多选
                                MDCard:
                                    id: personal_user_card
                                    orientation: "vertical"
                                    size_hint: (1, 1)
                                    elevation: 3 if "个人用户" in root.selected_roles else 1
                                    radius: [dp(8)]
                                    md_bg_color: root.selected_role_color if "个人用户" in root.selected_roles else app.theme.CARD_BACKGROUND
                                    padding: dp(6)
                                    on_release: root.select_role("个人用户")
                                    ripple_behavior: False
                                    # 禁用默认的hover效果，确保选中状态颜色持续保留
                                    theme_bg_color: "Custom"

                                    MDBoxLayout:
                                        orientation: "vertical"
                                        size_hint_y: None
                                        height: self.minimum_height
                                        spacing: dp(3)
                                        pos_hint: {"center_x": 0.5, "center_y": 0.5}

                                        MDIcon:
                                            icon: "account"
                                            size_hint: (None, None)
                                            size: (dp(28), dp(28))
                                            pos_hint: {"center_x": 0.5}
                                            theme_icon_color: "Custom"
                                            icon_color: app.theme.PRIMARY_COLOR

                                        MDLabel:
                                            text: "个人用户"
                                            size_hint_y: None
                                            height: dp(18)
                                            theme_text_color: "Primary"
                                            font_style: "Label"
                                            halign: "center"

                                # 单位管理员 - 使用MDCard作为可点击容器，支持多选
                                MDCard:
                                    id: unit_admin_card
                                    orientation: "vertical"
                                    size_hint: (1, 1)
                                    elevation: 3 if "单位管理员" in root.selected_roles else 1
                                    radius: [dp(8)]
                                    md_bg_color: root.selected_role_color if "单位管理员" in root.selected_roles else app.theme.CARD_BACKGROUND
                                    padding: dp(6)
                                    disabled: root.registration_type == "替他人注册"
                                    opacity: 1.0 if root.registration_type != "替他人注册" else 0.5
                                    on_release: root.select_role("单位管理员") if root.registration_type != "替他人注册" else None
                                    ripple_behavior: False
                                    # 禁用默认的hover效果，确保选中状态颜色持续保留
                                    theme_bg_color: "Custom"

                                    MDBoxLayout:
                                        orientation: "vertical"
                                        size_hint_y: None
                                        height: self.minimum_height
                                        spacing: dp(3)
                                        pos_hint: {"center_x": 0.5, "center_y": 0.5}

                                        MDIcon:
                                            icon: "medical-bag"
                                            size_hint: (None, None)
                                            size: (dp(28), dp(28))
                                            pos_hint: {"center_x": 0.5}
                                            theme_icon_color: "Custom"
                                            icon_color: app.theme.PRIMARY_COLOR

                                        MDLabel:
                                            text: "单位管理员"
                                            size_hint_y: None
                                            height: dp(18)
                                            theme_text_color: "Primary"
                                            font_style: "Label"
                                            halign: "center"

                                # 健康顾问 - 使用MDCard作为可点击容器，支持多选
                                MDCard:
                                    id: health_advisor_card
                                    orientation: "vertical"
                                    size_hint: (1, 1)
                                    elevation: 3 if "健康顾问" in root.selected_roles else 1
                                    radius: [dp(8)]
                                    md_bg_color: root.selected_role_color if "健康顾问" in root.selected_roles else app.theme.CARD_BACKGROUND
                                    padding: dp(6)
                                    disabled: root.registration_type == "替他人注册"
                                    opacity: 1.0 if root.registration_type != "替他人注册" else 0.5
                                    on_release: root.select_role("健康顾问") if root.registration_type != "替他人注册" else None
                                    ripple_behavior: False
                                    # 禁用默认的hover效果，确保选中状态颜色持续保留
                                    theme_bg_color: "Custom"

                                    MDBoxLayout:
                                        orientation: "vertical"
                                        size_hint_y: None
                                        height: self.minimum_height
                                        spacing: dp(3)
                                        pos_hint: {"center_x": 0.5, "center_y": 0.5}

                                        MDIcon:
                                            icon: "heart-pulse"
                                            size_hint: (None, None)
                                            size: (dp(28), dp(28))
                                            pos_hint: {"center_x": 0.5}
                                            theme_icon_color: "Custom"
                                            icon_color: app.theme.PRIMARY_COLOR

                                        MDLabel:
                                            text: "健康顾问"
                                            size_hint_y: None
                                            height: dp(18)
                                            theme_text_color: "Primary"
                                            font_style: "Label"
                                            halign: "center"

                                # 超级管理员 - 使用MDCard作为可点击容器，支持多选
                                MDCard:
                                    id: super_admin_card
                                    orientation: "vertical"
                                    size_hint: (1, 1)
                                    elevation: 3 if "超级管理员" in root.selected_roles else 1
                                    radius: [dp(8)]
                                    md_bg_color: root.selected_role_color if "超级管理员" in root.selected_roles else app.theme.CARD_BACKGROUND
                                    padding: dp(6)
                                    disabled: root.registration_type == "替他人注册"
                                    opacity: 1.0 if root.registration_type != "替他人注册" else 0.5
                                    on_release: root.select_role("超级管理员") if root.registration_type != "替他人注册" else None
                                    ripple_behavior: False
                                    # 禁用默认的hover效果，确保选中状态颜色持续保留
                                    theme_bg_color: "Custom"

                                    MDBoxLayout:
                                        orientation: "vertical"
                                        size_hint_y: None
                                        height: self.minimum_height
                                        spacing: dp(3)
                                        pos_hint: {"center_x": 0.5, "center_y": 0.5}

                                        MDIcon:
                                            icon: "shield-account"
                                            size_hint: (None, None)
                                            size: (dp(28), dp(28))
                                            pos_hint: {"center_x": 0.5}
                                            theme_icon_color: "Custom"
                                            icon_color: app.theme.PRIMARY_COLOR

                                        MDLabel:
                                            text: "超级管理员"
                                            size_hint_y: None
                                            height: dp(18)
                                            theme_text_color: "Primary"
                                            font_style: "Label"
                                            halign: "center"

                        # 与被注册人关系选择（仅当选择"替他人注册"时显示）
                        BoxLayout:
                            id: relationship_container
                            orientation: "vertical"
                            size_hint_x: 0.3
                            size_hint_y: None
                            height: dp(180) if root.registration_type == "替他人注册" else 0
                            opacity: 1 if root.registration_type == "替他人注册" else 0
                            disabled: False if root.registration_type == "替他人注册" else True
                            spacing: dp(5)


                            MDCard:
                                orientation: 'vertical'
                                size_hint_y: None
                                height: dp(180)
                                padding: dp(10)
                                elevation: 2
                                radius: [dp(16)]
                                shadow_softness: 8
                                shadow_offset: (0, 1)
                                opacity: 1 if root.registration_type == "替他人注册" else 0

                                # 添加居中容器
                                BoxLayout:
                                    orientation: 'vertical'
                                    padding: [dp(10), dp(40), dp(10), dp(40)]

                                    Spinner:
                                        id: relationship_spinner
                                        text: "请选择关系" if not root.relationship else root.relationship
                                        values: ("父亲", "母亲", "岳父", "岳母", "公公", "婆婆", "丈夫", "妻子", "儿子", "女儿", "兄弟", "姐妹", "其他")
                                        option_cls: 'CustomSpinnerOption'
                                        size_hint_y: None
                                        height: dp(40)
                                        background_normal: ''
                                        background_color: app.theme.PRIMARY_MEDIUM
                                        on_text: root.relationship = self.text if self.text != "请选择关系" else ""

                # 拍照上传区域
                BoxLayout:
                    orientation: "vertical"
                    size_hint_y: None
                    height: dp(150)
                    spacing: dp(app.metrics.PADDING_SMALL)

                    Label:
                        text: "身份证照片："
                        size_hint_y: None
                        height: dp(30)
                        color: app.theme.TEXT_PRIMARY
                        halign: 'left'
                        text_size: self.size
                        bold: True

                    # 照片区域
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: dp(120)
                        canvas.before:
                            Color:
                                rgba: app.theme.CARD_BACKGROUND
                            RoundedRectangle:
                                pos: self.pos
                                size: self.size
                                radius: [dp(app.metrics.CORNER_RADIUS)]

                        # 照片预览或拍照按钮
                        BoxLayout:
                            orientation: "vertical"
                            padding: dp(app.metrics.PADDING_SMALL)

                            Image:
                                id: id_photo
                                source: "" if not root.photo_taken else root.photo_path
                                size_hint: (1, 1)

                            Button:
                                text: "点击拍摄身份证照片"
                                size_hint_y: None
                                height: dp(40)
                                background_color: app.theme.PRIMARY_COLOR
                                on_release: root.open_camera()

                # 资格证书上传区域 - 仅当选择健康顾问角色时显示
                BoxLayout:
                    id: certificate_container
                    orientation: "vertical"
                    size_hint_y: None
                    height: dp(250) if "健康顾问" in root.selected_roles else 0
                    opacity: 1 if "健康顾问" in root.selected_roles else 0
                    disabled: not "健康顾问" in root.selected_roles
                    spacing: dp(app.metrics.PADDING_SMALL)
                    # 使用cache_geometry属性提高性能
                    cache_geometry: True

                    Label:
                        text: "资格证书上传（申请健康顾问必填）："
                        size_hint_y: None
                        height: dp(30)
                        color: app.theme.TEXT_PRIMARY
                        halign: 'left'
                        text_size: self.size
                        bold: True

                    # 医师资格证书
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: dp(100)
                        canvas.before:
                            Color:
                                rgba: app.theme.CARD_BACKGROUND
                            RoundedRectangle:
                                pos: self.pos
                                size: self.size
                                radius: [dp(app.metrics.CORNER_RADIUS)]

                        BoxLayout:
                            orientation: "vertical"
                            padding: dp(app.metrics.PADDING_SMALL)

                            Label:
                                text: "医师资格证书"
                                size_hint_y: None
                                height: dp(20)
                                color: app.theme.TEXT_PRIMARY
                                halign: 'left'
                                text_size: self.size

                            Image:
                                id: medical_license_photo
                                source: "" if not root.medical_license_taken else root.medical_license_path
                                size_hint: (1, 0.6)

                            Button:
                                text: "上传医师资格证书"
                                size_hint_y: None
                                height: dp(40)
                                background_color: app.theme.PRIMARY_COLOR
                                on_release: root.upload_certificate("medical_license")

                    # 医师执业证书
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: dp(100)
                        canvas.before:
                            Color:
                                rgba: app.theme.CARD_BACKGROUND
                            RoundedRectangle:
                                pos: self.pos
                                size: self.size
                                radius: [dp(app.metrics.CORNER_RADIUS)]

                        BoxLayout:
                            orientation: "vertical"
                            padding: dp(app.metrics.PADDING_SMALL)

                            Label:
                                text: "医师执业证书"
                                size_hint_y: None
                                height: dp(20)
                                color: app.theme.TEXT_PRIMARY
                                halign: 'left'
                                text_size: self.size

                            Image:
                                id: practice_license_photo
                                source: "" if not root.practice_license_taken else root.practice_license_path
                                size_hint: (1, 0.6)

                            Button:
                                text: "上传医师执业证书"
                                size_hint_y: None
                                height: dp(40)
                                background_color: app.theme.PRIMARY_COLOR
                                on_release: root.upload_certificate("practice_license")

                # 基础信息部分
                BoxLayout:
                    orientation: "vertical"
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(app.metrics.PADDING_SMALL)

                    Label:
                        text: "基础信息"
                        size_hint_y: None
                        height: dp(30)
                        color: app.theme.TEXT_PRIMARY
                        halign: 'left'
                        text_size: self.size
                        bold: True
                        font_name: "NotoSans"
                        font_size: dp(14)

                    # 表单网格，两列布局
                    GridLayout:
                        cols: 2
                        spacing: dp(app.metrics.PADDING_SMALL)
                        size_hint_y: None
                        height: self.minimum_height

                        # 用户名
                        Label:
                            text: "用户名："
                            size_hint: None, None
                            size: dp(100), dp(40)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            valign: 'middle'
                            text_size: self.size
                            font_name: "NotoSans"
                            font_size: dp(14)

                        TextInput:
                            id: username_input
                            size_hint_y: None
                            height: dp(40)
                            multiline: False
                            hint_text: "请输入用户名"
                            font_name: "NotoSans"
                            font_size: dp(14)

                        # 密码
                        Label:
                            text: "密码："
                            size_hint: None, None
                            size: dp(100), dp(40)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            valign: 'middle'
                            text_size: self.size
                            font_name: "NotoSans"
                            font_size: dp(14)

                        TextInput:
                            id: password_input
                            size_hint_y: None
                            height: dp(40)
                            multiline: False
                            password: True
                            hint_text: "请输入密码(需包含字母和数字)"
                            font_name: "NotoSans"
                            font_size: dp(14)

                        # 确认密码
                        Label:
                            text: "确认密码："
                            size_hint: None, None
                            size: dp(100), dp(40)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            valign: 'middle'
                            text_size: self.size
                            font_name: "NotoSans"
                            font_size: dp(14)

                        TextInput:
                            id: confirm_password_input
                            size_hint_y: None
                            height: dp(40)
                            multiline: False
                            password: True
                            hint_text: "请再次输入密码"
                            font_name: "NotoSans"
                            font_size: dp(14)

                        # 身份证号码
                        Label:
                            text: "身份证号码："
                            size_hint: None, None
                            size: dp(100), dp(40)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            valign: 'middle'
                            text_size: self.size
                            font_name: "NotoSans"
                            font_size: dp(14)

                        TextInput:
                            id: id_card_input
                            size_hint_y: None
                            height: dp(40)
                            multiline: False
                            hint_text: "请输入身份证号码"
                            font_name: "NotoSans"
                            font_size: dp(14)

                        # 手机号码
                        Label:
                            text: "手机号码："
                            size_hint: None, None
                            size: dp(100), dp(40)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            valign: 'middle'
                            text_size: self.size
                            font_name: "NotoSans"
                            font_size: dp(14)

                        TextInput:
                            id: phone_input
                            size_hint_y: None
                            height: dp(40)
                            multiline: False
                            hint_text: "请输入手机号码"
                            font_name: "NotoSans"
                            font_size: dp(14)

                    # 姓名
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: dp(70)

                        Label:
                            text: "姓名：*"
                            size_hint_y: None
                            height: dp(30)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            text_size: self.size
                            font_name: "NotoSans"
                            font_size: dp(14)

                        TextInput:
                            id: name_input
                            hint_text: "请输入真实姓名"
                            multiline: False
                            size_hint_y: None
                            height: dp(40)
                            font_name: "NotoSans"
                            font_size: dp(14)

                    # 性别
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: dp(70)

                        Label:
                            text: "性别："
                            size_hint: None, None
                            size: dp(100), dp(40)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            valign: 'middle'
                            text_size: self.size
                            font_name: "NotoSans"
                            font_size: dp(14)

                        BoxLayout:
                            orientation: "horizontal"
                            size_hint_y: None
                            height: dp(40)
                            spacing: dp(app.metrics.PADDING_SMALL)

                            Button:
                                text: "男"
                                size_hint_x: 0.5
                                color: app.theme.TEXT_LIGHT
                                background_normal: ''
                                background_color: app.theme.PRIMARY_COLOR if root.gender == "男" else app.theme.PRIMARY_LIGHT
                                on_release: root.set_gender("男")
                                font_name: "NotoSans"
                                font_size: dp(14)

                            Button:
                                text: "女"
                                size_hint_x: 0.5
                                color: app.theme.TEXT_LIGHT
                                background_normal: ''
                                background_color: app.theme.PRIMARY_COLOR if root.gender == "女" else app.theme.PRIMARY_LIGHT
                                on_release: root.set_gender("女")
                                font_name: "NotoSans"
                                font_size: dp(14)

                    # 出生日期
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: dp(70)

                        Label:
                            text: "出生日期：*"
                            size_hint_y: None
                            height: dp(30)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            text_size: self.size
                            font_name: "NotoSans"
                            font_size: dp(14)

                        Button:
                            id: birth_date_input
                            text: "请选择出生日期"
                            size_hint_y: None
                            height: dp(40)
                            font_name: "NotoSans"
                            font_size: dp(14)
                            background_normal: ''
                            background_color: app.theme.PRIMARY_MEDIUM
                            color: app.theme.TEXT_PRIMARY
                            on_release: root.show_birth_date_picker()

                    # 民族
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: dp(70)

                        Label:
                            text: "民族：*"
                            size_hint_y: None
                            height: dp(30)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            text_size: self.size

                        Spinner:
                            id: ethnicity_spinner
                            text: "请选择民族"
                            values: ["汉族", "蒙古族", "回族", "藏族", "维吾尔族", "苗族", "彝族", "壮族", "布依族", "朝鲜族", "满族", "侗族", "瑶族", "白族", "土家族", "哈尼族", "哈萨克族", "傣族", "黎族", "傈僳族", "佤族", "畲族", "高山族", "拉祜族", "水族", "东乡族", "纳西族", "景颇族", "柯尔克孜族", "土族", "达斡尔族", "仫佬族", "羌族", "布朗族", "撒拉族", "毛南族", "仡佬族", "锡伯族", "阿昌族", "普米族", "塔吉克族", "怒族", "乌孜别克族", "俄罗斯族", "鄂温克族", "德昂族", "保安族", "裕固族", "京族", "塔塔尔族", "独龙族", "鄂伦春族", "赫哲族", "门巴族", "珞巴族", "基诺族", "其他"]
                            option_cls: 'CustomSpinnerOption'
                            size_hint_y: None
                            height: dp(40)
                            background_normal: ''
                            background_color: app.theme.PRIMARY_MEDIUM

                    # 教育程度
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: dp(70)

                        Label:
                            text: "教育程度：*"
                            size_hint_y: None
                            height: dp(30)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            text_size: self.size

                        Spinner:
                            id: education_spinner
                            text: "请选择教育程度"
                            values: ["小学", "初中", "高中", "中专", "大专", "本科", "硕士", "博士", "其他"]
                            option_cls: 'CustomSpinnerOption'
                            size_hint_y: None
                            height: dp(40)
                            background_normal: ''
                            background_color: app.theme.PRIMARY_MEDIUM

                    # 工作单位
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: dp(70)

                        Label:
                            text: "工作单位："
                            size_hint_y: None
                            height: dp(30)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            text_size: self.size

                        TextInput:
                            id: workplace_input
                            hint_text: "请输入工作单位"
                            multiline: False
                            size_hint_y: None
                            height: dp(40)

                    # 职业
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: dp(70)

                        Label:
                            text: "职业："
                            size_hint_y: None
                            height: dp(30)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            text_size: self.size

                        TextInput:
                            id: occupation_input
                            hint_text: "请输入职业"
                            multiline: False
                            size_hint_y: None
                            height: dp(40)

                    # 联系地址
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: dp(70)

                        Label:
                            text: "联系地址："
                            size_hint_y: None
                            height: dp(30)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            text_size: self.size

                        TextInput:
                            id: address_input
                            hint_text: "请输入联系地址"
                            multiline: False
                            size_hint_y: None
                            height: dp(40)

                    # 电子邮箱
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: dp(70)

                        Label:
                            text: "电子邮箱：*"
                            size_hint_y: None
                            height: dp(30)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            text_size: self.size

                        TextInput:
                            id: email_input
                            hint_text: "请输入电子邮箱"
                            multiline: False
                            size_hint_y: None
                            height: dp(40)

                    # 紧急联系人
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: dp(70)

                        Label:
                            text: "紧急联系人：*"
                            size_hint_y: None
                            height: dp(30)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            text_size: self.size

                        TextInput:
                            id: emergency_contact_input
                            hint_text: "请输入紧急联系人姓名"
                            multiline: False
                            size_hint_y: None
                            height: dp(40)

                    # 紧急联系人电话
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: dp(70)

                        Label:
                            text: "紧急联系人电话：*"
                            size_hint_y: None
                            height: dp(30)
                            color: app.theme.TEXT_PRIMARY
                            halign: 'left'
                            text_size: self.size

                        TextInput:
                            id: emergency_phone_input
                            hint_text: "请输入紧急联系人电话"
                            multiline: False
                            size_hint_y: None
                            height: dp(40)

                # 底部按钮
                BoxLayout:
                    orientation: "horizontal"
                    size_hint_y: None
                    height: dp(50)
                    spacing: dp(app.metrics.PADDING_MEDIUM)
                    padding: [0, dp(app.metrics.PADDING_NORMAL), 0, 0]

                    Button:
                        text: "返回登录"
                        size_hint_x: 0.5
                        background_normal: ''
                        background_color: app.theme.PRIMARY_DARK
                        color: app.theme.TEXT_LIGHT
                        on_release: root.back_to_login()
                        font_name: "NotoSans"
                        font_size: dp(14)

                    Button:
                        text: "提交注册"
                        size_hint_x: 0.5
                        background_normal: ''
                        background_color: app.theme.PRIMARY_COLOR
                        color: app.theme.TEXT_LIGHT
                        on_release: root.register()
                        font_name: "NotoSans"
                        font_size: dp(14)
""")

class RegisterScreen(BaseScreen):
    # 属性定义
    selected_roles = ListProperty([])  # 使用ListProperty以支持UI自动更新
    photo_taken = BooleanProperty(False)
    photo_path = StringProperty("")
    gender = StringProperty("男")
    api_client = ObjectProperty(None)
    id_card_verified = BooleanProperty(False)  # 身份证是否已验证
    user_id = StringProperty("")  # 用户ID
    registration_type = StringProperty("本人注册")  # 注册类型: "本人注册" 或 "替他人注册"
    relationship = StringProperty("") # 与被注册人的关系
    identity = StringProperty("")  # 用户身份标识
    selected_role_color = [0.2, 0.6, 1, 0.8]  # 选中角色的背景色 - 更明显的蓝色背景
    _reg_trigger = BooleanProperty(False)  # 用于触发UI更新的内部属性

    # 证书上传相关属性
    medical_license_taken = BooleanProperty(False)  # 医师资格证书是否已上传
    medical_license_path = StringProperty("")  # 医师资格证书路径
    practice_license_taken = BooleanProperty(False)  # 医师执业证书是否已上传
    practice_license_path = StringProperty("")  # 医师执业证书路径
    certificate_document_ids = ListProperty([])  # 上传的证书文档ID列表
    
    # 出生日期相关属性
    birth_date = StringProperty("")  # 出生日期字符串

    def __init__(self, **kwargs):
        """初始化注册屏幕"""
        # 设置BaseScreen的导航栏属性
        kwargs.setdefault('screen_title', '用户注册')
        kwargs.setdefault('show_top_bar', False)  # 注册页面不显示顶部导航栏
        
        # 过滤掉无效的参数，只保留BaseScreen支持的参数
        valid_kwargs = {}
        valid_params = {'name', 'screen_title', 'show_top_bar', 'top_bar_action_icon'}
        for key, value in kwargs.items():
            if key in valid_params:
                valid_kwargs[key] = value
        
        super(RegisterScreen, self).__init__(**valid_kwargs)
        self.api_client = APIClient()
        self._reg_trigger = False
        # 初始化加载对话框
        self._loading_dialog = None
        self.current_certificate_type = None
        # 在初始化时绑定身份证输入框的事件
        Clock.schedule_once(self._bind_id_card_input, 0)
        # 初始化用户管理器
        try:
            from utils.user_manager import get_user_manager
            self.user_manager = get_user_manager()
        except ImportError:
            self.user_manager = None
        
        # 初始化统一文件上传下载管理器
        self.file_manager = FileUploadDownloadManager()




    def on_enter(self, *args):
        """每次进入注册页面时调用"""
        # BaseScreen已经统一管理Logo，无需手动处理
        pass



    def _bind_id_card_input(self, dt):
        """绑定身份证输入框的事件"""
        _ = dt  # 忽略未使用参数
        if hasattr(self, 'ids') and hasattr(self.ids, 'id_card_input'):
            self.ids.id_card_input.bind(text=self._on_id_card_input)

    def _on_id_card_input(self, instance, value):
        """身份证号码输入事件处理"""
        _ = instance  # 忽略未使用参数
        # 当输入长度达到18位时自动提取信息
        if len(value) == 18:
            self.extract_info_from_id_card(value)

    def extract_info_from_id_card(self, id_card_number):
        """从身份证号码中提取信息"""
        if not id_card_number or len(id_card_number) != 18:
            return

        try:
            # 提取出生日期
            birth_year = id_card_number[6:10]
            birth_month = id_card_number[10:12]
            birth_day = id_card_number[12:14]
            birth_date = f"{birth_year}/{birth_month}/{birth_day}"

            # 提取性别（倒数第二位，奇数为男，偶数为女）
            gender_code = int(id_card_number[-2])
            gender = "男" if gender_code % 2 == 1 else "女"

            # 提取地区代码（前6位）
            area_code = id_card_number[:6]
            _ = area_code  # 忽略未使用变量
            # 这里可以添加地区代码转换为具体地址的逻辑

            # 更新UI
            self.ids.birth_date_input.text = birth_date
            self.set_gender(gender)

            # 标记身份证已验证
            self.id_card_verified = True

            # 显示提取成功消息
            self._show_message(f"已从身份证号码中提取信息：\n出生日期：{birth_date}\n性别：{gender}")

        except Exception as e:
            self._show_message(f"身份证号码格式错误：{str(e)}")

    def process_id_card_scan(self, scan_result):
        """处理身份证扫描结果

        Args:
            scan_result (dict): 包含身份证信息的字典，包括：
                - id_number: 身份证号
                - name: 姓名
                - gender: 性别
                - birth_date: 出生日期 (YYYY/MM/DD格式)
                - address: 住址
        """
        try:
            # 更新身份证号
            self.ids.id_card_input.text = scan_result.get('id_number', '')

            # 更新姓名
            self.ids.name_input.text = scan_result.get('name', '')

            # 更新性别
            self.set_gender(scan_result.get('gender', '男'))

            # 更新出生日期
            self.ids.birth_date_input.text = scan_result.get('birth_date', '')

            # 更新地址
            self.ids.address_input.text = scan_result.get('address', '')

            # 生成一个默认邮箱地址
            try:
                name = scan_result.get('name', '')
                id_number = scan_result.get('id_number', '')
                if name and id_number:
                    # 使用姓名拼音首字母和身份证后4位生成默认邮箱
                    try:
                        # 修复Pyright错误：添加类型注释表明这个导入是可选的
                        from utils.pinyin_utils import get_first_letters  # type: ignore
                        name_pinyin = get_first_letters(name)
                    except ImportError:
                        # 如果拼音工具不可用，使用简单的替代方案
                        name_pinyin = name[:2]  # 使用姓名前两个字符
                    email = f"{name_pinyin}{id_number[-4:]}@example.com".lower()
                    self.ids.email_input.text = email
            except Exception as e:
                print(f"生成默认邮箱时出错: {e}")

            # 标记身份证已验证
            self.id_card_verified = True

            # 显示成功消息
            self._show_message("身份证信息已成功导入")

        except Exception as e:
            self._show_message(f"处理身份证信息时出错：{str(e)}")

    def open_camera(self):
        """打开相机视图拍摄照片"""
        try:
            # 适配 widgets.camera_view.CameraView 的签名: on_capture/on_dismiss/mode/callback
            camera_view = CameraView(
                on_capture=self.on_photo_captured,
                on_dismiss=self.on_camera_dismissed,
                mode="id_card",
                callback=self.process_id_card_scan
            )
            camera_view.open()
        except Exception as e:
            self._show_message(f"相机初始化失败: {str(e)}")

    def select_role(self, role):
        """选择或取消选择角色 - 优化版本，提高按钮响应性"""
        import logging
        logger = logging.getLogger(__name__)

        # 立即提供视觉反馈
        print(f"角色按钮被点击: {role}")
        logger.info(f"用户点击角色: {role}")

        # 检查是否需要更新
        was_health_advisor = "健康顾问" in self.selected_roles
        roles_list = list(self.selected_roles)

        # 如果是替他人注册，只能选择个人用户角色
        if self.registration_type == "替他人注册":
            if role != "个人用户":
                self._show_message("替他人注册只能选择'个人用户'角色")
                return
            elif "个人用户" not in roles_list:
                roles_list = ["个人用户"]
                self.selected_roles = roles_list
                self.identity = "个人用户"  # 设置身份为个人用户
                logger.info(f"替他人注册，设置角色为个人用户")
                self._trigger_ui_update()
                return
            elif role == "个人用户" and "个人用户" in roles_list:
                # 不允许取消选择个人用户角色
                self._show_message("替他人注册必须选择'个人用户'角色")
                return
        else:
            # 正常角色选择/取消逻辑
            if role in roles_list:
                # 如果角色已在列表中，则移除（取消选择）
                roles_list.remove(role)
                logger.info(f"取消选择角色: {role}")
                # 如果移除的是当前身份，则选择剩余角色中权限最高的作为身份
                if role == self.identity and roles_list:
                    # 按权限排序选择身份
                    if "超级管理员" in roles_list:
                        self.identity = "超级管理员"
                    elif "健康顾问" in roles_list:
                        self.identity = "健康顾问"
                    elif "单位管理员" in roles_list:
                        self.identity = "单位管理员"
                    else:
                        self.identity = "个人用户"
                elif role == self.identity and not roles_list:
                    self.identity = ""  # 如果没有剩余角色，清空身份
            else:
                # 如果角色不在列表中，则添加（选择）
                roles_list.append(role)
                logger.info(f"选择角色: {role}")
                # 如果当前没有设置身份，或者新添加的角色权限更高，则更新身份
                if not self.identity or (
                    role == "超级管理员" or
                    (role == "健康顾问" and self.identity not in ["超级管理员"]) or
                    (role == "单位管理员" and self.identity not in ["超级管理员", "健康顾问"])
                ):
                    self.identity = role

        # 检查健康顾问状态是否改变
        is_health_advisor = "健康顾问" in roles_list
        health_advisor_changed = was_health_advisor != is_health_advisor

        # 更新角色列表 - 使用一次性更新，避免多次触发UI更新
        self.selected_roles = roles_list
        logger.info(f"当前选择的角色: {self.selected_roles}, 身份: {self.identity}")

        # 立即触发UI更新以提供视觉反馈
        self._trigger_ui_update()

        # 如果健康顾问状态改变，手动更新证书容器
        if health_advisor_changed:
            self._update_certificate_container(is_health_advisor)

        # 强制刷新UI以确保视觉反馈立即生效
        try:
            from kivy.clock import Clock
            Clock.schedule_once(lambda dt: self._force_ui_refresh(), 0.1)
        except Exception as e:
            print(f"调度UI刷新时出错: {e}")

    def _force_ui_refresh(self):
        """强制刷新UI以确保视觉反馈立即生效"""
        try:
            # 强制更新角色卡片的视觉状态
            self._update_role_cards_visual_state()

            # 如果有父布局，强制重新布局
            if self.parent:
                self.parent.do_layout()
        except Exception as e:
            print(f"强制刷新UI时出错: {e}")

    def _trigger_ui_update(self):
        """触发UI更新以提供即时的视觉反馈"""
        # 触发属性更新，强制UI重新渲染
        self._reg_trigger = not self._reg_trigger

        # 强制更新UI绑定，确保视觉反馈立即生效
        try:
            # 手动更新角色卡片的视觉状态
            self._update_role_cards_visual_state()
        except Exception as e:
            print(f"更新角色卡片视觉状态时出错: {e}")

    def _update_role_cards_visual_state(self):
        """手动更新角色卡片的视觉状态"""
        from kivymd.app import MDApp
        app = MDApp.get_running_app()

        # 定义角色卡片的ID映射
        role_cards = {
            "个人用户": "personal_user_card",
            "单位管理员": "unit_admin_card",
            "健康顾问": "health_advisor_card",
            "超级管理员": "super_admin_card"
        }

        # 更新每个角色卡片的视觉状态
        for role, card_id in role_cards.items():
            try:
                if hasattr(self.ids, card_id):
                    card = getattr(self.ids, card_id)
                    is_selected = role in self.selected_roles

                    # 检查是否在替他人注册模式下，非个人用户角色应该被禁用
                    is_disabled = (self.registration_type == "替他人注册" and role != "个人用户")

                    # 确定背景色
                    if is_selected and not is_disabled:
                        target_color = self.selected_role_color
                        target_elevation = 3
                    else:
                        target_color = app.theme.CARD_BACKGROUND if hasattr(app, 'theme') and hasattr(app.theme, 'CARD_BACKGROUND') else [1, 1, 1, 1]
                        target_elevation = 1

                    # 更新背景色和阴影
                    card.md_bg_color = target_color
                    card.elevation = target_elevation

                    # 更新禁用状态和透明度
                    if is_disabled:
                        card.disabled = True
                        card.opacity = 0.5
                    else:
                        card.disabled = False
                        card.opacity = 1.0

                    # 强制重新设置背景色以确保持续保留
                    # 使用Clock延迟执行，确保在所有事件处理完成后再次设置
                    from kivy.clock import Clock
                    Clock.schedule_once(lambda dt, c=card, color=target_color: setattr(c, 'md_bg_color', color), 0.1)

                    print(f"更新角色卡片 {role}: 选中={is_selected}, 禁用={is_disabled}, 颜色={target_color}")
                else:
                    print(f"警告: 未找到角色卡片ID {card_id}")
            except Exception as e:
                print(f"更新角色卡片 {role} 时出错: {e}")
                import traceback
                traceback.print_exc()

    def _update_certificate_container(self, show_certificates):
        """更新证书容器的显示状态"""
        if hasattr(self, 'ids') and hasattr(self.ids, 'certificate_container'):
            from kivy.metrics import dp
            container = self.ids.certificate_container

            # 使用批量更新，减少布局计算次数
            if show_certificates:
                # 显示证书容器
                container.height = dp(250)
                container.opacity = 1
                container.disabled = False
            else:
                # 隐藏证书容器
                container.height = 0
                container.opacity = 0
                container.disabled = True

    def set_gender(self, gender):
        """设置性别"""
        self.gender = gender

    def on_camera_dismissed(self):
        """相机视图被关闭时的回调"""
        print("相机已关闭")

    def on_photo_captured(self, image):
        """拍摄照片后的回调，保存照片并设置UI显示"""
        try:
            # 确保存储路径存在
            photos_dir = os.path.join(os.path.dirname(__file__), "..", "user_photos")
            if not os.path.exists(photos_dir):
                os.makedirs(photos_dir)

            # 生成唯一的文件名
            photo_filename = f"user_photo_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.png"
            photo_path = os.path.join(photos_dir, photo_filename)

            # 保存图像
            image.save(photo_path)

            # 更新UI状态
            self.photo_taken = True
            self.photo_path = photo_path

            # 更新照片按钮
            print(f"照片已保存到: {photo_path}")
        except Exception as e:
            self._show_message(f"保存照片失败: {str(e)}")

    def set_registration_type(self, reg_type):
        """设置注册类型"""
        print(f"设置注册类型: {reg_type}, 当前类型: {self.registration_type}")

        # 如果当前类型与新类型相同，不做任何操作
        if self.registration_type == reg_type:
            return

        # 如果切换到替他人注册模式
        if reg_type == "替他人注册":
            # 重置角色为只有个人用户
            self.selected_roles = ["个人用户"]

        else:
            # 切换到本人注册，清空关系字段
            self.relationship = ""

        # 设置注册类型
        self.registration_type = reg_type

        # 只调用一次更新UI组件
        self._update_registration_ui()

    def _find_role_box(self, role_name):
        """查找指定角色名称的BoxLayout

        Args:
            role_name: 角色名称，如"个人用户"、"单位管理员"等

        Returns:
            找到的BoxLayout对象，如果未找到则返回None
        """
        # 遍历所有子控件查找角色卡片
        if not hasattr(self, 'ids'):
            return None

        # 获取GridLayout，它包含所有角色卡片
        grid_layout = None
        for child in self.walk():
            if isinstance(child, GridLayout) and child.cols == 2 and len(child.children) >= 4:
                # 找到了包含角色卡片的GridLayout
                grid_layout = child
                break

        if not grid_layout:
            return None

        # 在GridLayout中查找指定角色的BoxLayout
        for child in grid_layout.children:
            if isinstance(child, BoxLayout):
                # 查找Label子控件，检查其文本是否匹配角色名称
                for subchild in child.children:
                    if hasattr(subchild, 'text') and subchild.text == role_name:
                        return child

        return None

    def _update_registration_ui(self):
        """更新注册类型相关的UI组件"""
        # 更新按钮颜色
        if hasattr(self, 'ids'):
            if hasattr(self.ids, 'other_register_btn') and hasattr(self.ids, 'self_register_btn'):
                if self.registration_type == "替他人注册":
                    # 修复Pyright错误：使用getattr安全访问AppTheme属性
                    self.ids.other_register_btn.background_color = getattr(AppTheme, 'PRIMARY_COLOR', [0.2, 0.6, 1, 1])
                    self.ids.self_register_btn.background_color = getattr(AppTheme, 'PRIMARY_LIGHT', [0.8, 0.9, 1, 1])
                else:
                    self.ids.self_register_btn.background_color = getattr(AppTheme, 'PRIMARY_COLOR', [0.2, 0.6, 1, 1])
                    self.ids.other_register_btn.background_color = getattr(AppTheme, 'PRIMARY_MEDIUM', [0.4, 0.7, 1, 1])
                    self.ids.self_register_btn.background_color = self.selected_role_color
                    self.ids.other_register_btn.background_color = AppTheme.PRIMARY_MEDIUM

            # 更新关系选择容器
            if hasattr(self.ids, 'relationship_container'):
                if self.registration_type == "替他人注册":
                    self.ids.relationship_container.height = dp(180)
                    self.ids.relationship_container.opacity = 1
                    self.ids.relationship_container.disabled = False

                    # 确保Spinner可以交互
                    if hasattr(self.ids, 'relationship_spinner'):
                        self.ids.relationship_spinner.disabled = False

                    # 如果是替他人注册，限制只能选择个人用户角色
                    if self.selected_roles and "个人用户" not in self.selected_roles:
                        self.selected_roles = ["个人用户"]
                    elif not self.selected_roles:
                        self.selected_roles = ["个人用户"]

                    # 隐藏非个人用户的角色卡片
                    for role_name in ["单位管理员", "健康顾问", "超级管理员"]:
                        role_box = self._find_role_box(role_name)
                        if role_box:
                            role_box.opacity = 0
                            role_box.height = 0
                            role_box.size_hint_y = None
                            role_box.disabled = True

                    # 调整个人用户卡片的大小和位置
                    personal_box = self._find_role_box("个人用户")
                    if personal_box:
                        personal_box.size_hint_x = 1.0
                        personal_box.pos_hint = {"center_x": 0.5}
                        # 确保个人用户卡片高度与关系选择框匹配
                        personal_box.height = dp(180)

                        # 查找内部控件，调整它们的位置使其居中
                        for child in personal_box.children:
                            if isinstance(child, BoxLayout):
                                child.pos_hint = {"center_x": 0.5, "center_y": 0.5}
                                child.size_hint = (None, None)
                                child.size = (dp(100), dp(80))

                    print("显示关系选择控件")
                else:
                    # 在本人注册模式下，隐藏关系选择框
                    self.ids.relationship_container.height = 0
                    self.ids.relationship_container.opacity = 0
                    self.ids.relationship_container.disabled = True

                    # 获取GridLayout，用于重置所有角色框
                    grid_layout = None
                    for child in self.walk():
                        if isinstance(child, GridLayout) and child.cols == 2 and len(child.children) >= 4:
                            grid_layout = child
                            break

                    if grid_layout:
                        # 重置GridLayout属性
                        grid_layout.col_default_width = dp(90)
                        grid_layout.row_default_height = dp(25)

                    # 恢复所有角色卡片的显示和大小
                    for role_name in ["个人用户", "单位管理员", "健康顾问", "超级管理员"]:
                        role_box = self._find_role_box(role_name)
                        if role_box:
                            role_box.opacity = 1.0
                            role_box.height = dp(80)  # 恢复原始高度
                            role_box.size_hint_y = None
                            role_box.size_hint_x = None  # 清除size_hint_x
                            role_box.width = dp(90)  # 设置固定宽度，与col_default_width匹配
                            role_box.disabled = False

                            # 重置内部控件的属性
                            for child in role_box.children:
                                if isinstance(child, BoxLayout):
                                    child.pos_hint = {"center_x": 0.5}
                                    child.size_hint = (1, 1)

                    # 获取包含角色选择卡片的MDCard，重置其属性
                    for child in self.walk():
                        if isinstance(child, Factory.MDCard) and any(isinstance(c, GridLayout) for c in child.children):
                            child.size_hint_x = 1.0  # 设置为全宽
                            break

                    print("隐藏关系选择控件")

                # 强制刷新布局
                if hasattr(self.ids.relationship_container, 'parent') and self.ids.relationship_container.parent:
                    self.ids.relationship_container.parent.do_layout()

                # 延迟进一步刷新布局，确保UI正确更新
                Clock.schedule_once(self._force_layout_update, 0.1)

    def _force_layout_update(self, dt):
        """强制更新布局

        这个方法会强制更新整个屏幕的布局，确保UI元素正确显示。

        Args:
            dt: Clock调度的时间增量（未使用）
        """
        _ = dt  # 忽略未使用参数
        try:
            # 获取根布局
            root_layout = None
            if hasattr(self, 'children') and self.children:
                root_layout = self.children[0]

            # 如果找到根布局，强制更新
            if root_layout:
                # 使用do_layout方法强制更新布局
                root_layout.do_layout()

                # 更新证书容器的布局
                if hasattr(self, 'ids') and hasattr(self.ids, 'certificate_container'):
                    container = self.ids.certificate_container
                    if container.parent:
                        container.parent.do_layout()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"强制更新布局时出错: {e}")

    def on_registration_type(self, instance, value):
        """注册类型变更事件处理"""
        _ = instance  # 忽略未使用参数
        try:
            print(f"注册类型变更为: {value}")
        except Exception as ex:
            print(f"处理注册类型变更时发生异常: {ex}")

    def validate_inputs(self):
        """验证输入字段"""
        # 检查必填字段
        if not self.selected_roles:
            self._show_message("请选择至少一个用户角色")
            return False

        if not self.ids.username_input.text:
            self._show_message("请输入用户名")
            return False

        if not self.ids.password_input.text:
            self._show_message("请输入密码")
            return False

        if not self.ids.confirm_password_input.text:
            self._show_message("请确认密码")
            return False

        if self.ids.password_input.text != self.ids.confirm_password_input.text:
            self._show_message("两次输入的密码不一致")
            return False

        # 验证注册类型和关系
        if self.registration_type == "替他人注册" and not self.relationship:
            self._show_message("请选择与被注册人的关系")
            return False

        if not self.ids.id_card_input.text:
            self._show_message("请输入身份证号码")
            return False

        if not self._validate_id_card(self.ids.id_card_input.text):
            self._show_message("请输入有效的身份证号码")
            return False

        if not self.ids.name_input.text:
            self._show_message("请输入姓名")
            return False

        if self.ids.birth_date_input.text == "请选择出生日期":
            self._show_message("请选择出生日期")
            return False

        if self.ids.ethnicity_spinner.text == "请选择民族":
            self._show_message("请选择民族")
            return False

        if self.ids.education_spinner.text == "请选择教育程度":
            self._show_message("请选择教育程度")
            return False

        if not self.ids.phone_input.text:
            self._show_message("请输入联系电话")
            return False

        # 验证电子邮箱
        if not self.ids.email_input.text:
            self._show_message("请输入电子邮箱")
            return False

        # 验证电子邮箱格式
        email = self.ids.email_input.text
        if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            self._show_message("请输入有效的电子邮箱地址")
            return False

        # 验证紧急联系人（必填项）
        if not self.ids.emergency_contact_input.text:
            self._show_message("请输入紧急联系人姓名")
            return False

        # 验证紧急联系人电话（必填项）
        if not self.ids.emergency_phone_input.text:
            self._show_message("请输入紧急联系人电话")
            return False

        # 验证密码格式（字母+数字）
        password = self.ids.password_input.text
        if not (re.search(r'[A-Za-z]', password) and re.search(r'[0-9]', password)):
            self._show_message("密码必须包含字母和数字")
            return False

        # 验证手机号格式
        phone = self.ids.phone_input.text
        if not self._validate_phone(phone):
            self._show_message("请输入正确的手机号码")
            return False

        # 验证紧急联系人电话格式
        emergency_phone = self.ids.emergency_phone_input.text
        if not self._validate_phone(emergency_phone):
            self._show_message("请输入正确的紧急联系人电话")
            return False

        # 验证出生日期格式
        birth_date = self.ids.birth_date_input.text
        if not self._validate_date(birth_date):
            self._show_message("请输入正确的出生日期格式：YYYY/MM/DD")
            return False

        # 验证健康顾问角色的证书上传
        if "健康顾问" in self.selected_roles:
            if not self.medical_license_taken:
                self._show_message("申请健康顾问角色需要上传医师资格证书")
                return False
            if not self.practice_license_taken:
                self._show_message("申请健康顾问角色需要上传医师执业证书")
                return False

        return True

    def _validate_id_card(self, id_card):
        """验证身份证号码格式"""
        # 简单验证：18位，最后一位可能是X
        if len(id_card) != 18:
            return False

        # 前17位必须是数字
        if not id_card[0:17].isdigit():
            return False

        # 最后一位可以是数字或X
        if not (id_card[17].isdigit() or id_card[17].upper() == 'X'):
            return False

        # 验证出生日期部分是否合法
        try:
            birth_year = int(id_card[6:10])
            birth_month = int(id_card[10:12])
            birth_day = int(id_card[12:14])

            # 简单的日期验证
            if birth_year < 1900 or birth_year > datetime.datetime.now().year:
                return False
            if birth_month < 1 or birth_month > 12:
                return False
            if birth_day < 1 or birth_day > 31:
                return False
        except:
            return False

        return True

    def _validate_phone(self, phone):
        """验证手机号格式"""
        # 简单的手机号验证：11位数字，以1开头
        return bool(re.match(r'^1\d{10}$', phone))

    def _validate_date(self, date_str):
        """验证日期格式"""
        # 验证YYYY/MM/DD格式
        return bool(re.match(r'^\d{4}/\d{1,2}/\d{1,2}$', date_str))

    def switch_to_login(self, dt):
        """切换到登录页面"""
        _ = dt  # 忽略未使用参数
        self.manager.current = 'login_screen'

    def register(self):
        """处理用户注册 - 修改后只通过后端服务器进行注册"""
        try:
            # 获取输入信息
            username = self.ids.username_input.text.strip()
            password = self.ids.password_input.text.strip()
            confirm_password = self.ids.confirm_password_input.text.strip()
            phone = self.ids.phone_input.text.strip()
            real_name = self.ids.name_input.text.strip()
            id_number = self.ids.id_card_input.text.strip()
            gender = self.gender  # 使用属性而不是直接访问ids
            email = self.ids.email_input.text.strip()
            birth_date = self.ids.birth_date_input.text.strip()
            # 如果出生日期按钮显示默认文本，则视为未选择
            if birth_date == "请选择出生日期":
                birth_date = ""
            address = self.ids.address_input.text.strip()
            emergency_contact = self.ids.emergency_contact_input.text.strip()
            emergency_phone = self.ids.emergency_phone_input.text.strip()

            # 验证输入
            if not username:
                self._show_message("请输入用户名")
                return

            if not password:
                self._show_message("请输入密码")
                return

            if password != confirm_password:
                self._show_message("两次输入的密码不一致")
                return

            if not phone:
                self._show_message("请输入手机号")
                return

            if not real_name:
                self._show_message("请输入真实姓名")
                return

            if not id_number:
                self._show_message("请输入身份证号")
                return

            if not email:
                self._show_message("请输入电子邮箱")
                return

            # 准备后端注册数据 - 使用后端字段名称
            user_data = {
                "username": username,
                "password": password,
                "phone": phone,
                "full_name": real_name,  # 后端使用full_name，移动端使用real_name
                "id_number": id_number,
                "gender": gender,
                "email": email,
                "birth_date": birth_date,
                "address": address,
                "emergency_contact": emergency_contact,
                "emergency_phone": emergency_phone,
                "registration_type": self.registration_type,
                "relationship_type": self.relationship,
                "additional_roles": self._convert_roles_to_ids(self.selected_roles)
            }

            # 如果申请健康顾问角色，添加证书文档ID
            if "健康顾问" in self.selected_roles and self.certificate_document_ids:
                user_data["certificate_document_ids"] = self.certificate_document_ids

            # 打印注册数据，帮助调试
            print(f"准备发送的注册数据: username={username}, full_name={real_name}")

            # 设置主要角色 - 确保与additional_roles一致
            if self.identity and self.identity in ["健康顾问", "单位管理员", "超级管理员"]:
                # 将中文角色名转换为后端期望的角色值
                role_map = {
                    "健康顾问": "consultant",
                    "单位管理员": "unit_admin",
                    "超级管理员": "super_admin"
                }
                user_data["role"] = role_map.get(self.identity, "personal")
            else:
                user_data["role"] = "personal"

            # 记录注册数据
            print(f"注册数据: 用户名={username}, 角色={user_data['role']}, 附加角色={user_data['additional_roles']}")

            # 显示等待提示
            self._show_loading_dialog("正在连接后端服务器...")

            try:
                # 获取API客户端实例
                from utils.api_factory import get_api_client
                # 修复Pyright错误：添加类型注释表明api_client是LocalApiClient类型
                api_client = get_api_client(client_type="local")  # type: ignore

                # 向后端服务器注册用户
                # 修复Pyright错误：添加类型注释表明register_user方法存在
                result = api_client.register_user(user_data)  # type: ignore
                self._dismiss_loading_dialog()

                if result:
                    # 注册成功
                    print(f"后端注册成功，用户ID: {result.get('user_id', '')}")

                    # 显示成功消息
                    self._show_message("注册成功！")

                    # 如果后端返回了访问令牌，可以保存到本地
                    if 'access_token' in result:
                        # 保存令牌到本地存储
                        from utils.storage import UserStorage
                        storage = UserStorage()
                        storage.save_token(result['access_token'])

                        # 保存用户信息到本地
                        if 'user' in result:
                            storage.save_user_info(result['user'])

                    # 延迟返回登录页面
                    Clock.schedule_once(self.switch_to_login, 2)
                else:
                    # 注册失败
                    # 修复Pyright错误：添加类型注释表明last_error属性存在
                    error_message = api_client.last_error or "未知错误"  # type: ignore
                    print(f"后端注册失败: {error_message}")

                    # 根据错误类型显示不同消息
                    if "身份证" in error_message and ("存在" in error_message or "已注册" in error_message or "已被" in error_message):
                        self._show_message(f"身份证号 {id_number} 已被注册")
                    elif "用户名" in error_message and ("存在" in error_message or "已注册" in error_message or "已被" in error_message):
                        self._show_message(f"用户名 {username} 已被注册")
                    elif "邮箱" in error_message and ("存在" in error_message or "已注册" in error_message or "已被" in error_message):
                        self._show_message(f"邮箱 {email} 已被注册")
                    else:
                        self._show_message(f"注册失败: {error_message}")

            except requests.exceptions.Timeout:
                self._dismiss_loading_dialog()
                self._show_message("连接后端服务器超时，请稍后重试")
                print("后端注册超时")

            except requests.exceptions.ConnectionError:
                self._dismiss_loading_dialog()
                self._show_message("无法连接到后端服务器，请检查网络连接")
                print("后端注册连接错误")

            except Exception as e:
                self._dismiss_loading_dialog()
                self._show_message(f"注册过程中发生错误: {str(e)}")
                print(f"后端注册异常: {str(e)}")
                import traceback
                traceback.print_exc()

        except Exception as e:
            print(f"注册失败: {e}")
            import traceback
            traceback.print_exc()
            self._show_message(f"注册失败: {str(e)}")

    # 已删除 _save_to_register_queue 方法，因为所有注册都直接通过后端服务器进行

    def _show_loading_dialog(self, message="加载中..."):
        """显示加载对话框

        Args:
            message: 显示的消息
        """
        try:
            from kivy.uix.progressbar import ProgressBar
            from kivy.clock import Clock
            from kivymd.uix.dialog import MDDialog
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.label import MDLabel
            from kivy.metrics import dp

            # 创建一个简单的Snackbar作为替代
            try:
                from kivymd.uix.snackbar import MDSnackbar
                from kivymd.uix.snackbar.snackbar import MDSnackbarText

                # 使用Snackbar来显示加载消息
                snackbar = MDSnackbar(
                    MDSnackbarText(
                        text=f"{message}",
                    ),
                    y=dp(20),
                    pos_hint={"center_x": 0.5},
                    duration=10  # 设置一个较长的时间
                )
                snackbar.open()
                self.loading_dialog = snackbar  # 保存引用以便后续关闭

                # 不使用进度条动画，因为Snackbar不支持

                return  # 使用Snackbar后直接返回
            except Exception as snackbar_error:
                print(f"创建Snackbar失败，尝试使用替代方案: {snackbar_error}")

            # KivyMD 2.0.1兼容的对话框
            try:
                # 创建对话框
                dialog = MDDialog()

                # 创建内容区域
                content = MDBoxLayout(
                    orientation='vertical',
                    spacing=dp(10),
                    padding=dp(20)
                )

                # 添加消息标签
                title_label = MDLabel(
                    text=message,
                    halign='center',
                    size_hint_y=None,
                    height=dp(40)
                )
                content.add_widget(title_label)

                # 添加进度条
                progress = ProgressBar(
                    max=100,
                    value=40,
                    size_hint_y=None,
                    height=dp(15)
                )
                content.add_widget(progress)

                # 将内容添加到对话框
                dialog.ids.container.add_widget(content)

                # 保存对话框引用并显示
                self.loading_dialog = dialog
                dialog.open()

                # 启动进度条动画
                def update_progress(dt):
                    _ = dt  # 忽略未使用参数
                    progress.value = (progress.value + 5) % 100
                    return True

                self.loading_progress_event = Clock.schedule_interval(update_progress, 0.2)
                return
            except Exception as dialog_error:
                print(f"创建对话框失败: {dialog_error}")
                import traceback
                traceback.print_exc()

        except Exception as e:
            print(f"显示加载对话框失败: {str(e)}")
            import traceback
            traceback.print_exc()
            # 备选方案：使用简单消息
            self._show_message(f"正在处理...\n{message}")

    def _dismiss_loading_dialog(self):
        """关闭加载对话框"""
        try:
            if hasattr(self, 'loading_progress_event') and self.loading_progress_event:
                from kivy.clock import Clock
                Clock.unschedule(self.loading_progress_event)
                self.loading_progress_event = None

            if hasattr(self, 'loading_dialog') and self.loading_dialog:
                # 检查是否为Snackbar
                from kivymd.uix.snackbar import MDSnackbar
                if isinstance(self.loading_dialog, MDSnackbar):
                    self.loading_dialog.dismiss()
                else:
                    # 假设是对话框
                    self.loading_dialog.dismiss()
                self.loading_dialog = None
        except Exception as e:
            print(f"关闭加载对话框失败: {str(e)}")
            import traceback
            traceback.print_exc()

    # 已删除 validate_id_card_exists 方法，因为身份证验证由后端服务器处理

    # 已删除 check_username_exists 方法，因为用户名验证由后端服务器处理

    def show_success_dialog(self):
        """显示注册成功对话框"""
        # 获取应用实例和主题颜色
        app = MDApp.get_running_app()
        text_color = (0.2, 0.6, 1, 1)  # 默认蓝色
        if app and hasattr(app, 'theme') and app.theme:
            text_color = app.theme.PRIMARY_COLOR
        _ = text_color  # 忽略未使用变量

        # 使用KivyMD 2.0.1兼容的对话框实现方式
        dialog = MDDialog()
        dialog.ids.container.add_widget(MDLabel(text="注册成功", size_hint_y=None, height=dp(40), pos_hint={"center_x": 0.5}))
        dialog.ids.container.add_widget(MDLabel(text="您已成功注册，现在可以登录了", size_hint_y=None, height=dp(40)))

        confirm_button = MDButton(
            MDButtonText(text="确定"),
            style="text",
            on_release=lambda x: self.on_register_success(dialog)
        )
        # 忽略lambda中的未使用参数x

        dialog.ids.button_container.add_widget(confirm_button)
        dialog.open()

    def on_register_success(self, dialog):
        """注册成功后的处理"""
        dialog.dismiss()
        self.manager.current = 'login_screen'

    def back_to_login(self):
        """返回登录页面"""
        self.manager.current = "login_screen"

    def show_birth_date_picker(self):
        """显示出生日期选择器"""
        try:
            # 使用正确的日期选择器函数
            show_kivymd_date_picker(
                title="选择出生日期",
                callback=self.on_birth_date_selected
            )
        except Exception as e:
            self._show_message(f"显示日期选择器失败: {str(e)}")
    
    def on_birth_date_selected(self, selected_date):
        """处理出生日期选择回调
        
        Args:
            selected_date: 选择的日期对象
        """
        try:
            # 格式化日期为YYYY/MM/DD格式
            if hasattr(selected_date, 'strftime'):
                formatted_date = selected_date.strftime('%Y/%m/%d')
            else:
                # 如果是字符串，尝试解析后重新格式化
                from datetime import datetime
                if isinstance(selected_date, str):
                    # 尝试多种日期格式
                    for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y']:
                        try:
                            date_obj = datetime.strptime(selected_date, fmt)
                            formatted_date = date_obj.strftime('%Y/%m/%d')
                            break
                        except ValueError:
                            continue
                    else:
                        formatted_date = str(selected_date)
                else:
                    formatted_date = str(selected_date)
            
            # 更新按钮文本和内部属性
            self.birth_date = formatted_date
            if hasattr(self.ids, 'birth_date_input'):
                self.ids.birth_date_input.text = formatted_date
            
            print(f"选择的出生日期: {formatted_date}")
            
        except Exception as e:
            print(f"处理出生日期选择时出错: {e}")
            self._show_message(f"处理出生日期时出错: {str(e)}")

    def _show_message(self, message):
        """显示消息弹窗"""
        popup = MessagePopup(message)
        popup.open()

    def generate_user_id(self):
        """生成用户ID（已弃用）

        此方法已弃用，不应再使用。所有用户ID应由后端生成。
        此方法现在会记录警告并返回None，而不是生成临时ID。

        Returns:
            None: 不再生成ID，返回None
        """
        # 记录警告
        print("警告: generate_user_id方法已弃用，不应再使用。所有用户ID应由后端生成。")
        return None

    def _force_layout_update(self, dt=None):
        """强制刷新布局

        Args:
            dt: Clock调度触发的时间增量（可选）
        """
        _ = dt  # 忽略未使用参数
        if hasattr(self, 'ids') and hasattr(self.ids, 'relationship_container') and hasattr(self.ids.relationship_container, 'parent') and self.ids.relationship_container.parent:
            self.ids.relationship_container.parent.do_layout()

    def show_loading_dialog(self, message="加载中..."):
        """显示加载对话框

        Args:
            message: 显示的消息
        """
        from kivymd.uix.dialog import MDDialog
        try:
            # 修复Pyright错误：KivyMD 2.0.1 dev0中使用MDLinearProgressIndicator替代MDProgressBar
            from kivymd.uix.progressindicator import MDLinearProgressIndicator
        except ImportError:
            # 如果进度条不可用，使用简单的替代方案
            class MDLinearProgressIndicator:
                def __init__(self, **kwargs):
                    _ = kwargs  # 忽略未使用参数
                    self.value = 0

        # 创建进度条
        # 修复Pyright错误：使用MDLinearProgressIndicator替代MDProgressBar
        progress = MDLinearProgressIndicator(
            size_hint_y=None,
            height=dp(4),
            type="indeterminate"
            # 移除不存在的参数
            # running_duration=1,
            # catching_duration=1.5
        )
        # 修复Pyright错误：MDLinearProgressIndicator没有start方法，使用value属性
        # progress.start()

        # 创建对话框
        from kivymd.uix.dialog import MDDialogHeadlineText
        self._loading_dialog = MDDialog(
            MDDialogHeadlineText(
                text=message,
                halign="center",
            ),
            type="custom",
            content_cls=progress,
            size_hint=(0.8, None),
            height=dp(100),
            auto_dismiss=False
        )

        # 显示对话框
        self._loading_dialog.open()

    def dismiss_loading_dialog(self):
        """关闭加载对话框"""
        if hasattr(self, '_loading_dialog') and self._loading_dialog:
            self._loading_dialog.dismiss()
            self._loading_dialog = None

    def show_error(self, message):
        """显示错误消息

        Args:
            message: 错误消息
        """
        self._show_message(message)

    def upload_certificate(self, certificate_type):
        """上传资格证书 - 使用统一文件上传下载管理器
        
        Args:
            certificate_type: 证书类型 (medical_license 或 practice_license)
        """
        try:
            # 设置当前处理的证书类型
            self.current_certificate_type = certificate_type
            
            # 获取证书名称用于显示
            certificate_name = "医师资格证书" if certificate_type == "medical_license" else "医师执业证书"
            
            # 使用统一文件管理器选择文件
            self.file_manager.select_file(
                callback=self._on_certificate_selected,
                file_types=['image', 'document'],  # 支持图片和文档
                title=f"选择{certificate_name}"
            )
            
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"打开文件选择器失败: {e}")
            self.show_error(f"选择文件失败: {str(e)}")

    def _on_certificate_selected(self, file_path):
        """处理证书文件选择结果 - 使用统一文件管理器
        
        Args:
            file_path: 选择的文件路径
        """
        if not file_path:
            return
            
        try:
            # 立即更新UI预览
            if self.current_certificate_type == "medical_license":
                self.medical_license_path = file_path
                if hasattr(self.ids, "medical_license_photo"):
                    self.ids.medical_license_photo.source = file_path
            elif self.current_certificate_type == "practice_license":
                self.practice_license_path = file_path
                if hasattr(self.ids, "practice_license_photo"):
                    self.ids.practice_license_photo.source = file_path
            
            # 获取证书名称和描述
            certificate_name = "医师资格证书" if self.current_certificate_type == "medical_license" else "医师执业证书"
            name = self.ids.name_input.text if hasattr(self.ids, "name_input") else ""
            description = f"{certificate_name} for {name}"
            
            # 使用统一文件管理器上传
            # 修复Pyright错误：移除不存在的description和category参数
            self.file_manager.upload_file(
                file_path=file_path,
                callback=self._on_certificate_uploaded,
                metadata={"certificate_type": self.current_certificate_type, "description": description}
                # 移除不存在的参数
                # description=description,
                # category="certificate",
            )
            
            # 显示上传中提示
            self._show_message(f"正在上传{certificate_name}...")
            
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"处理证书选择时出错: {e}")
            self.show_error(f"处理文件时出错: {str(e)}")

    def _on_certificate_uploaded(self, success, result=None, error=None):
        """处理证书上传结果回调
        
        Args:
            success: 上传是否成功
            result: 上传成功时的结果数据
            error: 上传失败时的错误信息
        """
        try:
            certificate_name = "医师资格证书" if self.current_certificate_type == "medical_license" else "医师执业证书"
            
            if success and result:
                # 上传成功
                document_id = result.get('document_id') or result.get('id') or result.get('file_id')
                
                if document_id:
                    # 添加到证书文档ID列表
                    if document_id not in self.certificate_document_ids:
                        self.certificate_document_ids.append(document_id)
                    
                    # 更新状态标志
                    if self.current_certificate_type == "medical_license":
                        self.medical_license_taken = True
                    elif self.current_certificate_type == "practice_license":
                        self.practice_license_taken = True
                    
                    self._show_message(f"{certificate_name}上传成功")
                    
                    # 记录成功日志
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.info(f"证书上传成功: {certificate_name}, ID: {document_id}")
                else:
                    # 没有获取到文档ID
                    self._show_message(f"{certificate_name}上传成功，但未获取到文档ID")
                    
            else:
                # 上传失败
                error_msg = str(error) if error else "未知错误"
                self._show_message(f"{certificate_name}上传失败: {error_msg}")
                
                # 重置UI状态
                if self.current_certificate_type == "medical_license":
                    if hasattr(self.ids, "medical_license_photo"):
                        self.ids.medical_license_photo.source = ""
                    self.medical_license_path = ""
                elif self.current_certificate_type == "practice_license":
                    if hasattr(self.ids, "practice_license_photo"):
                        self.ids.practice_license_photo.source = ""
                    self.practice_license_path = ""
                
                # 记录错误日志
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"证书上传失败: {certificate_name}, 错误: {error_msg}")
                
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"处理证书上传结果时出错: {e}")
            self._show_message(f"处理上传结果时出错: {str(e)}")

    def _convert_roles_to_ids(self, roles):
        """将角色列表转换为角色ID列表（字符串格式）"""
        role_id_map = {
            "个人用户": "personal",
            "单位管理员": "unit_admin",
            "健康顾问": "consultant",
            "超级管理员": "super_admin"
        }

        # 返回字符串角色值，而不是数字ID
        return [role_id_map.get(role, "personal") for role in roles if role in role_id_map]