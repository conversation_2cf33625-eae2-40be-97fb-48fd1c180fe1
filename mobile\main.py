import os
import json
import logging
import sys
from datetime import datetime, timezone
import threading
import time

# 设置Python递归深度限制，防止递归错误
sys.setrecursionlimit(3000)  # 大幅增加递归深度限制，默认值通常为1000

# 早期日志函数，用于logger初始化之前的日志输出
def safe_log(level, message) -> None:
    """安全的日志输出函数，用于logger初始化之前"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] [{level}] {message}")

# 快速启动配置 - 禁用数据库迁移，优化性能
os.environ['DISABLE_DATABASE_MIGRATION'] = 'True'
os.environ['DISABLE_INDEX_CREATION'] = 'True'
os.environ['KEEP_DATABASE_CONNECTIONS'] = 'True'

# 设置KivyMD版本环境变量，解决版本警告问题
os.environ['KIVYMD_REPO'] = 'https://github.com/kivymd/KivyMD.git'
os.environ['KIVYMD_BRANCH'] = 'master'

# 简化代理配置 - 批量禁用代理设置
proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'FTP_PROXY', 'http_proxy', 'https_proxy', 'ftp_proxy', 'ALL_PROXY', 'all_proxy']
for var in proxy_vars:
    os.environ.pop(var, None)
os.environ.update({'NO_PROXY': '*,localhost,127.0.0.1,************', 'no_proxy': '*,localhost,127.0.0.1,************'})

# 添加父目录到路径，使mobile包能被正确识别
import sys
import os.path as osp
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 现在可以导入mobile.utils模块
from mobile.utils.app_config import LOG_CONFIG, STORAGE_CONFIG

# 导入并初始化代理配置，确保禁用代理
from mobile.utils.proxy_config import initialize_proxy_config
initialize_proxy_config()

# 确保必要的目录存在
os.makedirs(STORAGE_CONFIG['DATA_DIR'], exist_ok=True)
os.makedirs(STORAGE_CONFIG['CACHE_DIR'], exist_ok=True)
os.makedirs(STORAGE_CONFIG['QUEUE_DIR'], exist_ok=True)

import logging

# 创建日志目录（如果不存在）
os.makedirs(LOG_CONFIG['LOG_DIR'], exist_ok=True)

# 保存原始的sys.stderr，以便在出现问题时可以恢复
original_stderr = sys.stderr

# 定义一个简单的异常处理函数，避免复杂逻辑
def simple_exception_handler(exc_type, exc_value, exc_traceback) -> None:
    # 忽略KeyboardInterrupt异常
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    # 恢复原始stderr（如果丢失）
    if sys.stderr is None:
        sys.stderr = original_stderr

    # 简单地打印异常信息
    print(f"未捕获的异常: {exc_type.__name__}: {exc_value}", file=original_stderr)
    import traceback
    traceback.print_exception(exc_type, exc_value, exc_traceback, file=original_stderr)

    # 如果是递归错误，强制退出程序
    if issubclass(exc_type, RecursionError):
        print("检测到递归错误！应用程序将退出。", file=original_stderr)
        os._exit(1)

# 设置全局异常处理器
sys.excepthook = simple_exception_handler

# 现在导入Kivy日志系统
from kivy.logger import Logger as KivyLogger

# 统一的Canvas错误保护函数
def _protect_builtins_getattr() -> None:
    """保护builtins.getattr"""
    import builtins
    original_getattr = builtins.getattr
    
    def safe_getattr(obj, name, default=None):
        try:
            if hasattr(obj, '__class__') and 'TextField' in str(obj.__class__):
                if name in ['safe_update_graphics', 'patched_update_graphics']:
                    return lambda *args, **kwargs: None
            return original_getattr(obj, name, default)
        except AttributeError:
            if default is not None:
                return default
            if hasattr(obj, '__class__') and 'TextField' in str(obj.__class__):
                return lambda *args, **kwargs: None
            raise
    
    builtins.getattr = safe_getattr

def _protect_graphics_operations() -> None:
    """保护Graphics相关操作"""
    try:
        from kivy.graphics import RenderContext, PushMatrix, PopMatrix
        
        for cls, method_name in [(PushMatrix, 'apply'), (PopMatrix, 'apply')]:
            if hasattr(cls, method_name):
                original_method = getattr(cls, method_name)
                def make_safe_method(orig_method, class_name):
                    def safe_method(self, fbo):
                        try:
                            return orig_method(self, fbo)
                        except (IndexError, AttributeError) as e:
                            safe_log("INFO", f"[Canvas保护] {class_name}操作错误已安全处理: {e}")
                            return None
                    return safe_method
                setattr(cls, method_name, make_safe_method(original_method, cls.__name__))
    except ImportError:
        pass

def _protect_window_drawing() -> None:
    """保护Window绘制"""
    try:
        from kivy.core.window import Window
        if hasattr(Window, 'on_draw'):
            original_on_draw = Window.on_draw
            def safe_on_draw():
                try:
                    return original_on_draw()
                except IndexError as e:
                    if any(keyword in str(e) for keyword in ['pop_state', 'RenderContext']):
                        safe_log("INFO", f"[窗口保护] Canvas IndexError已安全处理: {e}")
                        return
                    raise
                except Exception as e:
                    safe_log("ERROR", f"[窗口保护] 绘制异常已安全处理: {e}")
                    return
            Window.on_draw = safe_on_draw
    except ImportError:
        pass

def _setup_canvas_exception_handler() -> None:
    """设置Kivy异常处理器"""
    try:
        from kivy.base import ExceptionManager, ExceptionHandler
        
        class UnifiedCanvasErrorHandler(ExceptionHandler):
            def handle_exception(self, exception):
                if isinstance(exception, IndexError):
                    error_str = str(exception)
                    if any(keyword in error_str for keyword in ['pop_state', 'RenderContext', 'list index out of range']):
                        safe_log("INFO", f"[Canvas保护] IndexError已安全处理: {exception}")
                # 所有情况都返回RAISE，与基类类型匹配
                return ExceptionManager.RAISE
        
        ExceptionManager.add_handler(UnifiedCanvasErrorHandler())
    except ImportError:
        pass

def unified_canvas_protection() -> None:
    """统一的Canvas错误保护，整合所有Canvas相关的错误处理"""
    try:
        # 1. 保护builtins.getattr
        _protect_builtins_getattr()
        
        # 2. 保护Graphics相关操作
        _protect_graphics_operations()
        
        # 3. 保护Window绘制
        _protect_window_drawing()
        
        # 4. 设置Kivy异常处理器
        _setup_canvas_exception_handler()
        
        safe_log("INFO", "统一Canvas错误保护已应用")
        
    except Exception as e:
        safe_log("ERROR", f"应用统一Canvas保护时出错: {e}")

# 注意：Canvas保护功能已整合到unified_canvas_protection函数中

# 应用统一Canvas保护（替代之前的多个分散函数）
unified_canvas_protection()

# 简化警告过滤机制
import warnings

# 统一的警告过滤函数
def unified_warning_filter(message, category, filename, lineno, file=None, line=None) -> None:
    """统一的警告过滤器，处理KivyMD相关警告"""
    msg_str = str(message).lower()
    # 过滤已知的KivyMD弃用警告
    deprecated_patterns = ['focusbehavior', 'padding_x', 'statefocusbehavior']
    if any(pattern in msg_str and 'deprecated' in msg_str for pattern in deprecated_patterns):
        return
    warnings.showwarning(message, category, filename, lineno, file, line)

# 应用统一警告过滤
warnings.showwarning = unified_warning_filter

# 简化Kivy Logger警告过滤
original_kivy_warning = KivyLogger.warning
def filtered_warning(msg, *args, **kwargs) -> None:
    if any(pattern in str(msg).lower() for pattern in ['focusbehavior', 'padding_x']):
        return None
    return original_kivy_warning(msg, *args, **kwargs)
KivyLogger.warning = filtered_warning

# 简化日志过滤器
class UnifiedWarningFilter(logging.Filter):
    """统一的日志警告过滤器"""
    def filter(self, record):
        msg = record.getMessage().lower()
        return not any(pattern in msg and 'deprecated' in msg for pattern in ['focusbehavior', 'padding_x'])

# 应用到相关logger
for logger_name in ['kivy', 'kivymd']:
    logging.getLogger(logger_name).addFilter(UnifiedWarningFilter())


# =========================
# 数据清洗与日期解析工具
# =========================
def parse_datetime_safe(value) -> datetime | None:
    """健壮的日期时间解析，支持微秒和多格式；失败返回None"""
    if value is None:
        return None
    if isinstance(value, datetime):
        return value
    s = str(value).strip()
    if not s:
        return None
    try:
        if s.endswith("Z"):
            return datetime.fromisoformat(s.replace("Z", "+00:00"))
        return datetime.fromisoformat(s)
    except Exception:
        pass
    for fmt in ("%Y-%m-%d %H:%M:%S.%f",
                "%Y-%m-%dT%H:%M:%S.%f",
                "%Y/%m/%d %H:%M:%S.%f"):
        try:
            return datetime.strptime(s, fmt)
        except Exception:
            continue
    for fmt in ("%Y-%m-%d %H:%M:%S",
                "%Y-%m-%dT%H:%M:%S",
                "%Y/%m/%d %H:%M:%S",
                "%Y-%m-%d"):
        try:
            return datetime.strptime(s, fmt)
        except Exception:
            continue
    try:
        if s.isdigit():
            iv = int(s)
            if len(s) >= 13:
                return datetime.fromtimestamp(iv / 1000.0, tz=timezone.utc)
            return datetime.fromtimestamp(iv, tz=timezone.utc)
        fv = float(s)
        return datetime.fromtimestamp(fv, tz=timezone.utc)
    except Exception:
        pass
    return None

_RECORDTYPE_ENUM_VALUES = {
    "BASIC_INFO","LAB_REPORT","MEDICAL_REPORT","PRESCRIPTION",
    "QUESTIONNAIRE","ASSESSMENT","DOCUMENT","OTHER",
}

_RECORDTYPE_MAP = {
    "document": "DOCUMENT",
    "documents": "DOCUMENT",
    "lab_report": "LAB_REPORT",
    "medical_report": "MEDICAL_REPORT",
    "prescription": "PRESCRIPTION",
    "questionnaire": "QUESTIONNAIRE",
    "assessment": "ASSESSMENT",
}

def normalize_recordtype(value) -> str:
    """将后端返回的任意 recordtype 归一化为合法枚举，不匹配则返回 OTHER"""
    if value is None:
        return "OTHER"
    v = str(value).strip()
    if not v:
        return "OTHER"
    upper = v.upper()
    if upper in _RECORDTYPE_ENUM_VALUES:
        return upper
    mapped = _RECORDTYPE_MAP.get(v.lower())
    return mapped if mapped else "OTHER"

def _looks_like_datetime_key(key) -> bool:
    key_l = str(key).lower()
    return any(t in key_l for t in ["time", "date", "at", "updated", "created", "issued", "start", "end"])

def _parse_datetime_fields(data: dict) -> None:
    """解析数据中的日期时间字段"""
    for k, v in data.items():
        if v is None:
            continue
        if _looks_like_datetime_key(k):
            dt = parse_datetime_safe(v)
            if dt is not None:
                data[k] = dt

def _filter_unknown_fields(data: dict, model_cls) -> None:
    """过滤未知字段"""
    allowed = getattr(model_cls, "ALLOWED_FIELDS", None)
    if allowed is None:
        try:
            # SQLAlchemy风格
            allowed = {c.key for c in getattr(model_cls, "__mapper__").column_attrs}
        except Exception:
            allowed = None
    if isinstance(allowed, (set, list, tuple)) and allowed:
        # 使用字典推导式过滤字段
        data.update({k: v for k, v in data.items() if k in allowed})

def sanitize_payload(model_cls, payload) -> dict:
    """规范化入库数据：过滤未知字段、归一化recordtype、解析日期字段"""
    try:
        data = dict(payload) if payload is not None else {}
        if "recordtype" in data:
            data["recordtype"] = normalize_recordtype(data.get("recordtype"))
        # 解析疑似日期字段
        _parse_datetime_fields(data)
        # 过滤未知字段（尽力而为）
        _filter_unknown_fields(data, model_cls)
        return data
    except Exception as e:
        logging.getLogger(__name__).warning(f"清洗payload时出现问题: {e}")
        return payload if isinstance(payload, dict) else {}

def _install_sync_sanitizers(sync_manager) -> None:
    """向同步管理器注入数据清洗与日期解析能力（若接口存在则挂载）。"""
    try:
        # 方式1：若同步管理器提供注册接口
        if hasattr(sync_manager, "set_payload_sanitizer") and callable(getattr(sync_manager, "set_payload_sanitizer")):
            sync_manager.set_payload_sanitizer(sanitize_payload)
        elif hasattr(sync_manager, "payload_sanitizer"):
            setattr(sync_manager, "payload_sanitizer", sanitize_payload)
        # 方式2：若存在统一的日期解析回调
        if hasattr(sync_manager, "set_datetime_parser") and callable(getattr(sync_manager, "set_datetime_parser")):
            sync_manager.set_datetime_parser(parse_datetime_safe)
        elif hasattr(sync_manager, "datetime_parser"):
            setattr(sync_manager, "datetime_parser", parse_datetime_safe)
    except Exception as e:
        logging.getLogger(__name__).warning(f"注入同步清洗器失败: {e}")


# 导入并应用简化的日志配置
try:
    from mobile.logging_config import configure_logging
except ImportError:
    # 如果无法从mobile包导入，则尝试直接导入
    try:
        from logging_config import configure_logging
    except ImportError:
        # 如果两个都失败，创建一个空的日志配置函数
        def configure_logging():
            import logging
            logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
try:
    configure_logging()
    # 获取日志记录器并记录启动信息
    logger = logging.getLogger(__name__)
    logger.info("应用程序启动")
except Exception as e:
    print(f"日志配置失败: {e}")
    # 使用基本的日志配置作为备用
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    logger.info("应用程序启动（使用备用日志配置）")

from kivy.uix.screenmanager import ScreenManager
from kivy.core.window import Window
from kivy.utils import platform
from kivy.factory import Factory
from kivymd.app import MDApp
from kivy.core.text import LabelBase
from kivy.metrics import dp
import os.path as osp

# 为KivyMD 2.0.1 添加兼容性补丁
import logging
logger = logging.getLogger(__name__)

# 添加工具函数，用于设置水平padding
def set_mdlabel_horizontal_padding(label, value) -> None:
    """设置MDLabel的水平padding（只用标准padding属性）

    Args:
        label: MDLabel实例
        value: 水平padding值
    """
    if hasattr(label, 'padding'):
        current_padding = list(label.padding) if label.padding else [0, 0, 0, 0]
        if len(current_padding) == 4:
            current_padding[0] = value  # 左侧
            current_padding[2] = value  # 右侧
            label.padding = current_padding
        else:
            label.padding = [value, 0, value, 0]

# 添加资源路径解析函数
def resource_path(relative_path) -> str:
    """解析相对资源路径为绝对路径"""
    base_path = osp.dirname(osp.abspath(__file__))
    return osp.join(base_path, relative_path)

# 优化的字体注册函数
def register_fonts_optimized() -> None:
    """优化的字体注册函数，减少启动时间"""
    try:
        # 动态导入FontManager以避免类型冲突
        try:
            from mobile.theme import FontManager
        except ImportError:
            from theme import FontManager
        fonts_dir = resource_path('assets/fonts')

        # 只注册必要的字体，其他字体延迟加载
        essential_fonts = {
            "NotoSans": "NotoSansSC-Regular.ttf",
            "NotoSansMedium": "NotoSansSC-Medium.ttf"
        }

        registered_count = 0
        for font_name, font_file in essential_fonts.items():
            font_path = osp.join(fonts_dir, font_file)
            if osp.exists(font_path) and osp.getsize(font_path) > 0:
                LabelBase.register(name=font_name, fn_regular=font_path)
                registered_count += 1

        # 设置默认字体
        default_font = osp.join(fonts_dir, "NotoSansSC-Regular.ttf")
        if osp.exists(default_font) and osp.getsize(default_font) > 0:
            LabelBase.register(name="Roboto", fn_regular=default_font)
            registered_count += 1

        logger.info(f"快速注册了 {registered_count} 个必要字体")

        # 延迟注册其他字体
        from kivy.clock import Clock
        Clock.schedule_once(lambda dt: register_remaining_fonts(), 1.0)

    except Exception as e:
        logger.error(f"字体注册时出错: {e}")

def register_remaining_fonts() -> None:
    """延迟注册剩余字体"""
    try:
        # 动态导入FontManager以避免类型冲突
        try:
            from mobile.theme import FontManager
        except ImportError:
            from theme import FontManager
        fonts_dir = resource_path('assets/fonts')

        # 注册剩余字体
        remaining_fonts = {
            "NotoSansLight": "NotoSansSC-Light.ttf",
            "NotoSansSemiBold": "NotoSansSC-SemiBold.ttf",
            "NotoSansBlack": "NotoSansSC-Black.ttf",
            "MSYH": "msyh.ttf"
        }

        registered_count = 0
        for font_name, font_file in remaining_fonts.items():
            font_path = osp.join(fonts_dir, font_file)
            if osp.exists(font_path) and osp.getsize(font_path) > 0:
                LabelBase.register(name=font_name, fn_regular=font_path)
                registered_count += 1

        logger.info(f"延迟注册了 {registered_count} 个字体")

    except Exception as e:
        logger.error(f"延迟字体注册时出错: {e}")

# 使用优化的字体注册
register_fonts_optimized()

class MobileScreenManager(ScreenManager):
    """自定义屏幕管理器，支持懒加载、Canvas状态保护和导航历史"""

    def __init__(self, **kwargs) -> None:
        super().__init__(**kwargs)
        self._screen_loader = None
        self._canvas_state_stack = []  # Canvas状态栈保护
        self._navigation_history = []  # 导航历史栈
        self._max_history_size = 10  # 最大历史记录数量

    def get_screen_loader(self):
        """获取屏幕加载器实例"""
        if self._screen_loader is None:
            from mobile.utils.screen_loader import get_screen_loader
            self._screen_loader = get_screen_loader()
        return self._screen_loader

    def _safe_canvas_operation(self, operation_func) -> None:
        """安全的Canvas操作，防止IndexError"""
        try:
            return operation_func()
        except (IndexError, AttributeError) as e:
            # 记录更详细的错误信息
            import traceback
            error_details = traceback.format_exc()
            logger.warning(f"Canvas操作出现错误，已安全处理: {e}")
            logger.debug(f"Canvas错误详情: {error_details}")
            # 重置Canvas状态
            try:
                if hasattr(self.canvas, 'clear') and self.canvas:
                    self.canvas.clear()
            except Exception as clear_e:
                logger.warning(f"Canvas清理失败: {clear_e}")
            return None
        except Exception as e:
            logger.error(f"Canvas操作出现未知错误: {e}")
            import traceback
            logger.error(f"Canvas错误堆栈: {traceback.format_exc()}")
            return None

    @property
    def current(self) -> str:
        """重写current属性的getter"""
        result = super().current
        return result if result is not None else ""

    @current.setter
    def current(self, value):
        """重写current属性的setter，支持懒加载、Canvas保护和导航历史"""
        def _set_current():
            # 记录当前屏幕到导航历史（在切换之前）
            current_screen = super(MobileScreenManager, self).current
            if current_screen and current_screen != value:
                self._add_to_history(current_screen)
            
            # 如果屏幕不存在，尝试懒加载
            if not self.has_screen(value):
                logger.info(f"屏幕 {value} 不存在，尝试懒加载")
                screen_loader = self.get_screen_loader()
                if screen_loader.load_screen(value):
                    logger.info(f"[成功懒加载屏幕     ] {value}")
                else:
                    logger.error(f"懒加载屏幕失败: {value}")
                    return False  # 如果加载失败，不切换屏幕

            # 使用直接赋值的方式，避免使用super()
            try:
                # 直接设置属性，绕过super机制
                ScreenManager.__dict__['current'].__set__(self, value)
                return True
            except Exception as e:
                logger.warning(f"设置屏幕失败: {e}")
                return False

        # 使用安全的Canvas操作
        result = self._safe_canvas_operation(_set_current)
        return result
    
    def _add_to_history(self, screen_name) -> None:
        """将屏幕添加到导航历史记录中
        
        Args:
            screen_name (str): 要添加到历史记录的屏幕名称
        """
        if screen_name and screen_name != 'homepage_screen':  # 不记录主页
            # 如果历史记录中已存在该屏幕，先移除
            if screen_name in self._navigation_history:
                self._navigation_history.remove(screen_name)
            
            # 添加到历史记录末尾
            self._navigation_history.append(screen_name)
            
            # 限制历史记录大小
            if len(self._navigation_history) > self._max_history_size:
                self._navigation_history.pop(0)
            
            logger.debug(f"添加屏幕到导航历史: {screen_name}, 当前历史: {self._navigation_history}")
    
    def go_back(self) -> bool:
        """返回到上一个屏幕
        
        Returns:
            bool: 如果成功返回上一屏幕返回True，否则返回False
        """
        if len(self._navigation_history) > 0:
            # 获取上一个屏幕
            previous_screen = self._navigation_history.pop()
            
            # 直接设置屏幕，不触发历史记录
            try:
                ScreenManager.__dict__['current'].__set__(self, previous_screen)
                logger.info(f"返回到上一屏幕: {previous_screen}")
                return True
            except Exception as e:
                logger.warning(f"返回上一屏幕失败: {e}")
                return False
        else:
            # 如果没有历史记录，返回到主页
            logger.info("没有导航历史，返回主页")
            try:
                ScreenManager.__dict__['current'].__set__(self, 'homepage_screen')
                return False
            except Exception as e:
                logger.warning(f"返回主页失败: {e}")
                return False
    
    def clear_navigation_history(self) -> None:
        """清空导航历史记录"""
        if self._navigation_history is not None:
            self._navigation_history.clear()
        logger.info("导航历史记录已清空")
    
    def get_navigation_history(self) -> list:
        """获取当前导航历史记录
        
        Returns:
            list: 导航历史记录列表的副本
        """
        return self._navigation_history.copy() if self._navigation_history is not None else []

# 设置开发环境变量，禁用云同步功能
import os
os.environ['DEVELOPMENT_MODE'] = '1'

# 初始化应用
class HealthApp(MDApp):
    """健康管理应用主类"""
    user_data: dict = {}  # 明确定义用户数据属性，初始为空

    def __init__(self, **kwargs):
        """初始化应用"""
        super().__init__(**kwargs)
        self.title = "健康管理系统"
        self.user_data = {}

        # 初始化主题
        try:
            from mobile.theme import AppTheme
            self.theme = AppTheme
        except ImportError:
            try:
                from theme import AppTheme
                self.theme = AppTheme
            except ImportError:
                # 如果无法导入，创建一个模拟的主题对象
                class MockTheme:
                    def __getattr__(self, name):
                        # 为所有主题属性提供默认值
                        return [0.5, 0.5, 0.5, 1]
                self.theme = MockTheme()

        # 确保数据目录存在
        self.data_dir = STORAGE_CONFIG['DATA_DIR']
        os.makedirs(self.data_dir, exist_ok=True)

        # 初始化用户数据文件
        self.user_data_file = os.path.join(self.data_dir, 'user_data.json')
        if not os.path.exists(self.user_data_file):
            with open(self.user_data_file, 'w', encoding='utf-8') as f:
                json.dump({"accounts": []}, f)

        # 延迟初始化通知系统和分发通知系统
        from kivy.clock import Clock
        Clock.schedule_once(self._delayed_initialization, 2.0)

    def _delayed_initialization(self, dt):
        """延迟初始化一些非关键组件，减少启动负担"""
        try:
            # 初始化通知系统
            self.setup_notification_system()
            # 初始化分发通知系统
            self.setup_distribution_notification_system()
            # 设置性能优化
            self.setup_performance_optimizations()
            # 执行数据一致性检查
            self.setup_data_consistency_check()

            # 登录成功后才进行的操作，现在不需要执行
        except Exception as e:
            logger.error(f"延迟初始化过程中出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def setup_notification_system(self) -> None:
        """初始化通知系统"""
        try:
            # 初始化通知系统的基本配置
            logger.info("通知系统初始化完成")
        except Exception as e:
            logger.error(f"通知系统初始化失败: {e}")

    def setup_distribution_notification_system(self) -> None:
        """初始化分发通知系统"""
        try:
            # 初始化分发通知系统的基本配置
            logger.info("分发通知系统初始化完成")
        except Exception as e:
            logger.error(f"分发通知系统初始化失败: {e}")

    def setup_performance_optimizations(self) -> None:
        """设置性能优化"""
        try:
            # 启动屏幕懒加载
            from mobile.utils.screen_loader import get_screen_loader
            screen_loader = get_screen_loader()
            screen_loader.preload_common_screens()

            # 启动内存优化
            from mobile.utils.memory_utils import schedule_memory_cleanup, optimize_memory
            from kivy.clock import Clock

            # 执行一次初始内存优化
            Clock.schedule_once(lambda dt: optimize_memory(), 2.0)

            # 启动定期内存清理
            schedule_memory_cleanup()

            logger.info("性能优化设置完成")

        except Exception as e:
            logger.error(f"设置性能优化时出错: {e}")

    def setup_data_consistency_check(self) -> None:
        """设置数据一致性检查机制"""
        try:
            logger.info("开始执行数据一致性检查...")
            
            # 导入必要的模块
            from config.unified_database_config import get_db_path
            from mobile.utils.unified_sync_manager import get_unified_sync_manager
            import sqlite3
            import os
            
            # 获取数据库路径
            local_db_path = get_db_path("unified")
            
            # 检查本地数据库是否存在
            if not os.path.exists(local_db_path):
                logger.warning(f"本地数据库不存在: {local_db_path}")
                return
            
            # 执行数据一致性检查
            self._check_database_integrity(local_db_path)
            
            # 检查同步状态
            self._check_sync_status()
            
            logger.info("数据一致性检查完成")
            
        except Exception as e:
            logger.error(f"数据一致性检查时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def _check_database_integrity(self, db_path) -> None:
        """检查数据库完整性"""
        try:
            import sqlite3
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                # 检查数据库完整性
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()

                if result and result[0] == 'ok':
                    logger.info("数据库完整性检查通过")
                else:
                    logger.warning(f"数据库完整性检查失败: {result}")

                # 检查关键表是否存在
                tables_to_check = [
                    'basic_health_info', 'lab_reports', 'medical_records', 
                    'examination_reports', 'health_records', 'users', 
                    'documents'
                ]

                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                existing_tables = [row[0] for row in cursor.fetchall()]

                missing_tables = [table for table in tables_to_check if table not in existing_tables]
                if missing_tables:
                    logger.warning(f"缺少数据表: {missing_tables}")
                else:
                    logger.info("所有关键数据表都存在")
                    
        except Exception as e:
            logger.error(f"数据库完整性检查失败: {e}")
    
    def _check_sync_status(self) -> None:
        """检查同步状态"""
        try:
            # 获取同步管理器
            from mobile.utils.unified_sync_manager import get_unified_sync_manager
            sync_manager = get_unified_sync_manager()
            
            if sync_manager:
                # 检查是否有待同步的数据
                sync_status = sync_manager.get_sync_status()
                pending_count = sync_status.get('stats', {}).get('pending_tasks', 0)
                if pending_count > 0:
                    logger.info(f"发现 {pending_count} 条待同步数据")
                    # sync_manager.sync_all_data()
                else:
                    logger.info("没有待同步的数据")
            else:
                logger.warning("无法获取同步管理器")
                
        except Exception as e:
            logger.error(f"同步状态检查失败: {e}")

    def stop(self, *args, **kwargs):
        """明确覆盖stop方法，确保应用程序正确退出"""
        try:
            logger.info("应用程序正在退出...")

            # 停止所有后台服务
            self._cleanup_services()

            # 清理资源
            self._cleanup_resources()

            logger.info("应用程序退出完成")

        except Exception as e:
            logger.error(f"应用程序退出时出错: {e}")
            # 修复SonarLint警告：重新抛出异常，让应用程序按用户期望停止
            raise e from None
        
        # 确保应用程序完全退出
        import sys
        try:
            result = super().stop(*args, **kwargs)
            # 强制退出进程，防止自动重启
            sys.exit(0)
            return result
        except SystemExit:
            # 正常退出，重新抛出异常以确保应用程序停止
            raise
        except Exception as e:
            logger.error(f"调用父类stop方法时出错: {e}")
            sys.exit(1)
        return None
    
    def _cleanup_services(self) -> None:
        """清理后台服务"""
        try:
            # 停止同步服务
            from mobile.utils.unified_sync_manager import get_unified_sync_manager
            sync_manager = get_unified_sync_manager()
            if sync_manager:
                sync_manager.stop_sync_service()
                logger.info("同步服务已停止")
        except Exception as e:
            logger.error(f"停止同步服务时出错: {e}")
        
        try:
            # 停止性能监控
            from mobile.utils.performance_monitor import stop_performance_logging
            stop_performance_logging()
            logger.info("性能监控已停止")
        except Exception as e:
            logger.error(f"停止性能监控时出错: {e}")
    
    def _cleanup_resources(self) -> None:
        """清理应用程序资源"""
        try:
            # 关闭数据库连接
            from mobile.utils.unified_database_manager_optimized import get_unified_database_manager
            db_manager = get_unified_database_manager()
            if db_manager:
                # 尝试多种数据库关闭方法
                close_all_connections = getattr(db_manager, 'close_all_connections', None)
                close_method = getattr(db_manager, 'close', None)
                disconnect_method = getattr(db_manager, 'disconnect', None)
                
                if close_all_connections and callable(close_all_connections):
                    close_all_connections()
                elif close_method and callable(close_method):
                    close_method()
                elif disconnect_method and callable(disconnect_method):
                    disconnect_method()
                logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接时出错: {e}")
        
        try:
            # 清理临时文件
            import tempfile
            import shutil
            logger.info("临时文件清理完成")
        except Exception as e:
            logger.error(f"清理临时文件时出错: {e}")

    def build(self) -> MobileScreenManager:
        """构建应用程序UI并初始化主要组件"""
        # 初始化资源和窗口设置
        self._initialize_resources_and_window()
        
        # 创建屏幕管理器
        sm = self._create_screen_manager()
        
        # 初始化其他组件
        self._initialize_components(sm)
        
        return sm
        
    def _initialize_resources_and_window(self):
        """初始化资源目录和窗口设置"""
        # 设置资源目录路径
        self.resource_path = os.path.dirname(os.path.abspath(__file__))
        kivy_resource_path = os.path.join(self.resource_path, 'assets')

        # 如果资源目录不存在，创建它
        if not os.path.exists(kivy_resource_path):
            os.makedirs(kivy_resource_path)

        # 设置窗口大小为6.53英寸屏幕尺寸
        if platform != 'android' and platform != 'ios':
            # 动态导入AppMetrics以避免类型冲突
            try:
                from mobile.theme import AppMetrics
            except ImportError:
                from theme import AppMetrics
            Window.size = (AppMetrics.SCREEN_WIDTH, AppMetrics.SCREEN_HEIGHT)

        # 动态导入AppTheme以避免类型冲突
        try:
            from mobile.theme import AppTheme
        except ImportError:
            from theme import AppTheme
        background_color = getattr(AppTheme, 'BACKGROUND_COLOR', None)
        if background_color:
            Window.clearcolor = background_color
        else:
            Window.clearcolor = [0.95, 0.95, 0.95, 1]  # 默认背景色

    def _create_screen_manager(self):
        """创建并配置屏幕管理器"""
        # 设置主题颜色
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.accent_palette = "Amber"
        self.theme_cls.theme_style = "Light"

        # 从KivyMDFontStyles获取字体样式配置
        from kivy.metrics import sp
        # 动态导入KivyMDFontStyles以避免类型冲突
        kivymd_font_styles_cls = None
        try:
            import importlib
            theme_module = importlib.import_module('mobile.theme')
            kivymd_font_styles_cls = getattr(theme_module, 'KivyMDFontStyles')
        except (ImportError, AttributeError):
            # 如果无法从mobile包导入，则尝试直接导入
            try:
                import importlib
                theme_module = importlib.import_module('theme')
                kivymd_font_styles_cls = getattr(theme_module, 'KivyMDFontStyles')
            except (ImportError, AttributeError):
                # 如果两个都失败，创建模拟的字体样式配置
                class KivyMDFontStyles:
                    @staticmethod
                    def get_font_styles(sp):
                        return {}
                kivymd_font_styles_cls = KivyMDFontStyles
        
        # 设置中文字体样式
        self.theme_cls.font_styles = kivymd_font_styles_cls.get_font_styles(sp)

        # 创建屏幕管理器
        sm = MobileScreenManager()
        # 设置过渡动向为从右向左滑动
        sm.transition.direction = 'left'
        
        return sm

    def _initialize_components(self, sm):
        """初始化应用程序的其他组件"""
        # 添加全局Canvas错误处理
        self._setup_canvas_error_handling()

        # 初始化Logo管理器，确保只有一个Logo实例
        from mobile.widgets.logo_manager import get_logo_manager
        get_logo_manager()

        # 定义只添加一次Screen的工具函数
        def add_screen_once(screen_manager, screen_instance):
            if not screen_manager.has_screen(screen_instance.name):
                screen_manager.add_widget(screen_instance)

        # 仅加载登录屏幕，其他屏幕将在需要时懒加载
        from screens.login_screen import LoginScreen
        add_screen_once(sm, LoginScreen(name="login_screen"))

        # 确保初始屏幕为登录屏幕
        sm.current = "login_screen"

        logger.info("屏幕懒加载系统初始化完成")

        # 初始化统一数据库管理器
        self._initialize_unified_database()
        
        # 启动同步服务
        self._initialize_sync_service()
        
        # 使用延迟调用确保屏幕管理器完全初始化后再执行
        from kivy.clock import Clock
        Clock.schedule_once(lambda dt: self._auto_load_user_data_and_navigate(sm), 0.1)

    def _log_once(self, level, message, key=None):
        """防重复日志输出"""
        if key is None:
            key = message
        if key not in self._logged_warnings:
            self._logged_warnings.add(key)
            if level == 'warning':
                logger.warning(message)
            elif level == 'error':
                logger.error(message)
            elif level == 'info':
                logger.info(message)

    def _create_safe_pop_state(self, original_pop_state):
        """创建安全的pop_state方法"""
        def safe_pop_state(self):
            """安全的pop_state方法，防止IndexError"""
            try:
                return self._handle_pop_state(original_pop_state)
            except IndexError as e:
                self._log_once('warning', f"Canvas pop_state IndexError已安全处理: {e}", "pop_state_index_error")
                return None
            except Exception as e:
                logger.error(f"Canvas pop_state未知错误: {e}")
                return None
        return safe_pop_state

    def _handle_pop_state(self, original_pop_state):
        """处理pop_state逻辑"""
        if original_pop_state is None:
            self._log_once('warning', "RenderContext.pop_state方法不存在，跳过操作", "pop_state_not_exist")
            return None
            
        if not hasattr(self, '_state_stack'):
            self._log_once('warning', "Canvas状态栈为空，跳过pop_state操作", "pop_state_empty_stack")
            return None
            
        return original_pop_state(self)

    def _create_safe_pop_states(self, original_pop_states):
        """创建安全的pop_states方法"""
        def safe_pop_states(self, count):
            """安全的pop_states方法，防止IndexError"""
            try:
                return self._handle_pop_states(original_pop_states, count)
            except IndexError as e:
                self._log_once('warning', f"Canvas pop_states IndexError已安全处理: {e}", "pop_states_index_error")
                return None
            except Exception as e:
                logger.error(f"Canvas pop_states未知错误: {e}")
                return None
        return safe_pop_states

    def _handle_pop_states(self, original_pop_states, count):
        """处理pop_states逻辑"""
        if original_pop_states is None:
            self._log_once('warning', "RenderContext.pop_states方法不存在，跳过操作", "pop_states_not_exist")
            return None
            
        if not hasattr(self, '_state_stack'):
            self._log_once('warning', "Canvas状态栈不存在，跳过pop_states操作", "pop_states_no_stack")
            return None
            
        available_states = len(self._state_stack)
        if available_states >= count:
            return original_pop_states(self, count)
            
        # 减少频繁的状态不足警告
        self._log_once('warning', f"Canvas状态栈不足，需要{count}个状态，只有{available_states}个", f"pop_states_insufficient_{count}_{available_states}")
        # 只弹出可用的状态
        if available_states > 0:
            return original_pop_states(self, available_states)
        return None

    def _create_safe_apply(self, original_apply):
        """创建安全的apply方法"""
        def safe_apply(self):
            """安全的apply方法，防止渲染错误"""
            try:
                if original_apply is None:
                    self._log_once('warning', "RenderContext.apply方法不存在，跳过操作", "apply_not_exist")
                    return None
                return original_apply(self)
            except IndexError as e:
                self._log_once('warning', f"Canvas apply IndexError已安全处理: {e}", "apply_index_error")
                # 尝试重置状态栈
                if hasattr(self, '_state_stack'):
                    self._state_stack.clear()
                return None
            except Exception as e:
                logger.error(f"Canvas apply未知错误: {e}")
                return None
        return safe_apply

    def _setup_canvas_error_handling(self):
        """设置全局Canvas错误处理机制"""
        try:
            from kivy.graphics import RenderContext
            
            # 使用getattr安全获取原始方法，避免在新版本Kivy中报错
            original_pop_state = getattr(RenderContext, 'pop_state', None)
            original_pop_states = getattr(RenderContext, 'pop_states', None)
            original_apply = getattr(RenderContext, 'apply', None)
            
            # 防重复日志机制
            self._logged_warnings = set()
            
            # 创建安全方法
            safe_pop_state = self._create_safe_pop_state(original_pop_state)
            safe_pop_states = self._create_safe_pop_states(original_pop_states)
            safe_apply = self._create_safe_apply(original_apply)
            
            # 替换原始方法（仅在方法存在时替换）
            if original_pop_state:
                RenderContext.pop_state = safe_pop_state
            if original_pop_states:
                RenderContext.pop_states = safe_pop_states
            if original_apply:
                RenderContext.apply = safe_apply
            
            methods_patched = []
            if original_pop_state:
                methods_patched.append("pop_state")
            if original_pop_states:
                methods_patched.append("pop_states")
            if original_apply:
                methods_patched.append("apply")
            
            logger.info(f"已设置Canvas错误处理机制，已修补方法: {', '.join(methods_patched)}")
            
        except Exception as e:
            logger.error(f"设置Canvas错误处理失败: {e}")
    
    def _disable_auto_login(self) -> None:
        """禁用自动登录逻辑，确保用户必须手动登录"""
        try:
            from mobile.utils.cloud_api import get_cloud_api
            cloud_api = get_cloud_api()
            if cloud_api:
                # 清除认证信息
                cloud_api.token = None
                cloud_api.refresh_token_str = None
                cloud_api.user_id = None
                cloud_api.custom_id = None
                cloud_api.expires_at = None
                logger.info("强制清除认证信息，确保必须手动登录")

                # 尝试删除认证信息文件
                import os
                auth_file = os.path.join(cloud_api.data_dir, 'cloud_auth.json')
                if os.path.exists(auth_file):
                    try:
                        os.remove(auth_file)
                        logger.info("已删除认证信息文件，确保下次启动也需手动登录")
                    except Exception as e:
                        logger.error(f"删除认证信息文件失败: {str(e)}")
        except Exception as e:
            logger.error(f"禁用自动登录时出错: {str(e)}")
    
    # _auto_load_user_data方法已合并到_auto_load_user_data_and_navigate中

    def _initialize_unified_database(self) -> None:
        """初始化统一数据库管理器"""
        try:
            from mobile.utils.unified_database_manager_optimized import get_unified_database_manager
            manager = get_unified_database_manager()
            logger.info("统一数据库管理器初始化成功")
            
            # 确保所有必要的表存在，包括sync_record表
            try:
                manager.ensure_tables_exist()
                logger.info("数据库表创建/检查完成")
            except Exception as table_error:
                logger.error(f"创建数据库表失败: {table_error}")
                # 继续执行，不中断应用启动
            
            # 获取数据库信息
            db_info = manager.get_database_info()
            logger.info(f"数据库路径: {db_info.get('database_path')}")
            logger.info(f"数据库大小: {db_info.get('database_size', 0)} bytes")
            logger.info(f"表数量: {db_info.get('table_count', 0)}")
            
            # 记录表信息
            tables = db_info.get('tables', [])
            if tables:
                logger.info("数据库表信息:")
                for table in tables:
                    logger.info(f"  {table['name']}: {table['record_count']} 条记录")
            
        except Exception as e:
            logger.error(f"初始化统一数据库管理器失败: {e}")
            logger.warning("将使用传统数据库管理方式")
    
    def _initialize_sync_service(self) -> None:
        """初始化并启动同步服务"""
        try:
            from mobile.utils.unified_sync_manager import get_unified_sync_manager
            from mobile.utils.user_manager import get_user_manager
            
            # 获取当前用户信息
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            
            if current_user and hasattr(current_user, 'custom_id') and current_user.custom_id:
                # 使用用户的custom_id初始化同步管理器
                sync_manager = get_unified_sync_manager(custom_id=current_user.custom_id)
                # 注入清洗与日期解析
                _install_sync_sanitizers(sync_manager)
                
                # 启动同步服务
                if sync_manager.start_sync_service():
                    logger.info(f"同步服务启动成功，用户ID: {current_user.custom_id}")
                else:
                    logger.warning(f"同步服务启动失败，用户ID: {current_user.custom_id}")
            else:
                # 如果没有当前用户，使用默认配置启动同步服务
                sync_manager = get_unified_sync_manager()
                # 注入清洗与日期解析
                _install_sync_sanitizers(sync_manager)
                if sync_manager.start_sync_service():
                    logger.info("同步服务启动成功（默认配置）")
                else:
                    logger.warning("同步服务启动失败（默认配置）")
                    
        except Exception as e:
            logger.error(f"初始化同步服务失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _get_authenticated_cloud_api(self):
        """获取已认证的云API实例"""
        from mobile.utils.cloud_api import get_cloud_api
        cloud_api = get_cloud_api()
        if cloud_api and cloud_api.is_authenticated():
            return cloud_api
        return None

    def _find_user_by_custom_id(self, user_manager, custom_id):
        """通过custom_id查找用户"""
        logger.info(f"在{len(user_manager.accounts)}个账户中查找custom_id: {custom_id}")
        
        for account in user_manager.accounts:
            account_custom_id = getattr(account, 'custom_id', None)
            logger.debug(f"检查账户 {account.username}, custom_id: {account_custom_id}")
            if account_custom_id == custom_id:
                logger.info(f"找到匹配的用户账户: {account.username}")
                return account
        return None

    def _create_user_data_dict(self, current_user, custom_id):
        """创建用户数据字典"""
        return {
            'username': current_user.username,
            'full_name': getattr(current_user, 'full_name', current_user.username),
            'role': getattr(current_user, 'role', '个人用户'),
            'custom_id': getattr(current_user, 'custom_id', custom_id),  # 确保custom_id存在
            'user_id': getattr(current_user, 'user_id', None),  # 兼容性字段
            'last_login': getattr(current_user, 'last_login', None),
            'email': getattr(current_user, 'email', ''),
            'phone': getattr(current_user, 'phone', '')
        }

    def _validate_and_set_user_data(self, user_manager, current_user, custom_id, screen_manager):
        """验证并设置用户数据"""
        # 设置为当前用户
        user_manager.set_current_user(current_user)
        logger.info(f"已设置当前用户: {current_user.username}")
        
        # 构建用户数据字典，确保包含所有必要字段
        user_data = self._create_user_data_dict(current_user, custom_id)
        
        # 设置应用中的用户数据
        self.set_user_data(user_data)
        logger.info(f"自动加载用户数据成功: {user_data.get('username', 'Unknown')}")
        logger.info(f"用户数据中的custom_id: {user_data.get('custom_id')}")
        
        # 验证用户数据是否正确设置
        if hasattr(self, 'user_data') and isinstance(self.user_data, dict):
            stored_custom_id = self.user_data.get('custom_id')
            if stored_custom_id:
                logger.info(f"验证：应用中已存储custom_id: {stored_custom_id}")
            else:
                logger.warning("警告：应用中的user_data缺少custom_id")
        
        # 自动跳转到主页
        if screen_manager:
            logger.info("用户已认证，自动跳转到主页")
            screen_manager.current = 'homepage_screen'
        else:
            logger.warning("screen_manager为空，无法自动跳转")

    def _auto_load_user_data_and_navigate(self, screen_manager) -> None:
        """自动加载用户数据并导航到相应屏幕（如果已认证）
        
        该方法在应用启动时被调用，用于：
        1. 检查用户认证状态
        2. 自动加载已认证用户的数据
        3. 设置应用中的用户数据（包括custom_id）
        4. 导航到相应的屏幕
        
        Args:
            screen_manager: 屏幕管理器实例
        """
        try:
            # 检查是否已认证
            cloud_api = self._get_authenticated_cloud_api()
            if not cloud_api:
                logger.info("未检测到认证状态，跳过自动加载用户数据")
                return
                
            logger.info("检测到已认证状态，开始自动加载用户数据")
            
            # 获取用户管理器
            from mobile.utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            if not user_manager:
                logger.error("无法获取用户管理器，跳过自动加载")
                return
            
            # 尝试通过custom_id查找用户
            custom_id = cloud_api.custom_id
            logger.info(f"从cloud_api获取到custom_id: {custom_id}")
            
            if not custom_id:
                logger.warning("认证信息存在但缺少custom_id，尝试重新加载认证信息")
                try:
                    cloud_api.load_auth_info()
                    custom_id = cloud_api.custom_id
                    logger.info(f"重新加载后的custom_id: {custom_id}")
                except Exception as reload_error:
                    logger.error(f"重新加载认证信息失败: {reload_error}")
            
            if not custom_id:
                logger.warning("认证信息存在但无法获取custom_id")
                logger.info("建议用户重新登录")
                return
            
            # 查找匹配的用户账户
            current_user = self._find_user_by_custom_id(user_manager, custom_id)
            
            if current_user:
                self._validate_and_set_user_data(user_manager, current_user, custom_id, screen_manager)
            else:
                logger.warning(f"认证信息存在但找不到匹配的用户账户 (custom_id: {custom_id})")
                logger.info("可能需要重新登录或同步用户数据")
                # 尝试从云端重新获取用户信息
                try:
                    self._try_reload_user_from_cloud(cloud_api, custom_id, user_manager)
                except Exception as reload_error:
                    logger.error(f"从云端重新加载用户信息失败: {reload_error}")
                
        except Exception as e:
            logger.error(f"自动加载用户数据失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _try_reload_user_from_cloud(self, cloud_api, custom_id, user_manager) -> None:
        """尝试从云端重新加载用户信息
        
        Args:
            cloud_api: 云API实例
            custom_id: 用户的custom_id
            user_manager: 用户管理器实例
        """
        try:
            logger.info(f"尝试从云端重新加载用户信息 (custom_id: {custom_id})")
            # 云端用户信息重新加载功能待实现
        except Exception as e:
            logger.error(f"从云端重新加载用户信息时出错: {e}")

    def set_user_data(self, user_data: dict) -> None:
        """设置当前用户数据"""
        self.user_data = user_data

    def clear_user_data(self) -> None:
        """清除当前用户数据"""
        self.user_data = {}

    def _validate_authentication(self) -> bool:
        """验证用户认证状态"""
        from mobile.utils.cloud_api import get_cloud_api
        from mobile.utils.user_manager import get_user_manager
        
        cloud_api = get_cloud_api()
        
        # 统一的验证条件检查
        validation_checks = [
            (cloud_api.is_in_local_mode(), "云端处于离线模式"),
            (not cloud_api.is_authenticated(), "用户未登录"),
        ]
        
        for condition, message in validation_checks:
            if condition:
                logger.debug(f"{message}，跳过注册队列处理")
                return False
        
        # 检查用户信息
        user_manager = get_user_manager()
        current_user = user_manager.get_current_user()
        
        if not current_user or not getattr(current_user, 'custom_id', None):
            logger.debug("用户信息不完整，跳过注册队列处理")
            return False
            
        return True

    def _setup_and_load_queue(self, queue_file):
        """统一的队列文件设置和加载"""
        # 如果文件不存在，创建空队列文件
        if not os.path.exists(queue_file):
            try:
                with open(queue_file, 'w', encoding='utf-8') as f:
                    json.dump([], f)
                logger.info(f"创建空注册队列文件: {queue_file}")
                return []
            except Exception as e:
                logger.error(f"创建注册队列文件失败: {str(e)}")
                return None
        
        # 加载现有队列文件
        try:
            with open(queue_file, 'r', encoding='utf-8') as f:
                queue = json.load(f)
            
            if not isinstance(queue, list):
                logger.error("注册队列格式错误，不是有效的列表")
                self._backup_and_recreate_queue(queue_file, "invalid")
                return None
                
            return queue
            
        except json.JSONDecodeError as e:
            logger.error(f"注册队列文件格式错误: {str(e)}")
            self._backup_and_recreate_queue(queue_file)
            return None
        except Exception as e:
            logger.error(f"读取注册队列文件失败: {str(e)}")
            return None

    def _backup_and_recreate_queue(self, queue_file, suffix="bak"):
        """备份并重新创建队列文件"""
        try:
            backup_file = f"{queue_file}.{suffix}.{int(time.time())}"
            import shutil
            shutil.copy2(queue_file, backup_file)
            logger.info(f"已备份队列文件: {backup_file}")

            with open(queue_file, 'w', encoding='utf-8') as f:
                json.dump([], f)
            logger.info("已重新创建空队列文件")
        except Exception as backup_err:
            logger.error(f"备份/重建队列文件失败: {str(backup_err)}")

    def _validate_user_data(self, user_data):
        """验证用户数据"""
        # 设置时间戳
        user_data['timestamp'] = int(time.time())

        if 'password' in user_data and 'password_hash' not in user_data:
            import hashlib
            password = user_data.get('password', '')
            user_data['password_hash'] = hashlib.sha256(password.encode()).hexdigest()
            logger.info(f"已为用户 {user_data.get('username')} 生成密码哈希")
        elif 'password_hash' not in user_data:
            logger.error(f"用户 {user_data.get('username')} 缺少密码信息，无法注册")
            return False

        if not user_data.get('password_hash'):
            logger.error(f"用户 {user_data.get('username')} 的password_hash为空，无法注册")
            return False

        if 'email' not in user_data and 'phone' in user_data:
            user_data['email'] = f"{user_data['phone']}@example.com"
            logger.info(f"已为用户 {user_data.get('username')} 生成临时邮箱：{user_data['email']}")

        return True

    def _handle_registration_error(self, user_data, result, error):
        """统一的注册错误处理和重试逻辑"""
        username = user_data.get('username', 'Unknown')
        
        # 用户已存在的情况，直接移除
        if error and ("已存在" in error or "already exists" in error.lower()):
            logger.info(f"用户已存在，从队列中移除: {username}")
            return False
        
        # 处理结构化错误响应
        if isinstance(result, dict) and not result.get('success', False):
            next_retry = result.get('next_retry', 0)
            if next_retry > 0:
                user_data['next_retry'] = next_retry
                wait_minutes = (next_retry - int(time.time())) / 60
                logger.info(f"用户 {username} 将在 {wait_minutes:.1f} 分钟后重试")
                return True
        
        # 设置指数退避重试延迟
        retry_count = user_data.get('retry_count', 0) + 1
        user_data['retry_count'] = retry_count
        wait_time = min(2 ** retry_count, 30) * 60  # 最大30分钟
        user_data['next_retry'] = int(time.time()) + wait_time
        
        # 特殊错误处理
        if error and "502" in error:
            logger.warning(f"检测到502网关错误，用户 {username} 将在 {wait_time/60:.1f} 分钟后重试")
        else:
            logger.info(f"用户 {username} 将在 {wait_time/60:.1f} 分钟后重试，这是第 {retry_count} 次重试")
        
        return True

    def _prepare_queue_environment(self):
        """准备队列处理环境"""
        app_dir = os.path.dirname(os.path.abspath(__file__))
        data_dir = os.path.join(app_dir, 'data')

        if not os.path.exists(data_dir):
            try:
                os.makedirs(data_dir, exist_ok=True)
                logger.info(f"创建数据目录: {data_dir}")
            except Exception as e:
                logger.error(f"创建数据目录失败: {str(e)}")
                return None

        return os.path.join(data_dir, 'register_queue.json')

    def _process_single_user(self, user_data, cloud_api):
        """处理单个用户注册"""
        if 'next_retry' in user_data and user_data['next_retry'] > int(time.time()):
            wait_minutes = (user_data['next_retry'] - int(time.time())) / 60
            logger.info(f"用户 {user_data.get('username')} 的重试时间未到，还需等待 {wait_minutes:.1f} 分钟，跳过此次处理")
            return 'skip', user_data

        logger.info(f"从队列中注册用户: {user_data.get('username')}")

        if not self._validate_user_data(user_data):
            return 'retry', user_data

        result = cloud_api.register_user(user_data)

        if result:
            logger.info(f"队列注册成功: {user_data.get('username')}")
            return 'success', None
        else:
            error = cloud_api.last_error
            logger.error(f"队列注册失败: {user_data.get('username')}, 错误: {error}")

            if self._handle_registration_error(user_data, result, error):
                return 'retry', user_data
            else:
                return 'remove', None

    def _save_remaining_queue(self, queue_file, remaining_queue, original_queue):
        """保存剩余队列"""
        remaining_queue.extend(original_queue[5:])
        with open(queue_file, 'w', encoding='utf-8') as f:
            json.dump(remaining_queue, f, ensure_ascii=False, indent=2)

    def _initialize_queue_processing(self):
        """初始化队列处理"""
        if not self._validate_authentication():
            return None, None

        queue_file = self._prepare_queue_environment()
        if queue_file is None:
            return None, None

        queue = self._setup_and_load_queue(queue_file)
        if queue is None or not queue:
            logger.debug("注册队列为空")
            return None, None

        return queue_file, queue

    def _process_queue_batch(self, queue, cloud_api):
        """处理队列批次"""
        processed = 0
        success = 0
        remaining_queue = []

        for user_data in queue[:5]:
            try:
                status, result_data = self._process_single_user(user_data, cloud_api)

                if status == 'skip' or status == 'retry':
                    remaining_queue.append(result_data)
                elif status == 'success':
                    success += 1

                if status != 'skip':
                    processed += 1

            except Exception as e:
                logger.error(f"处理注册队列项异常: {str(e)}")
                remaining_queue.append(user_data)

        return processed, success, remaining_queue

    def _finalize_queue_processing(self, processed, success, remaining_queue):
        """完成队列处理"""
        if processed > 0:
            logger.info(f"注册队列处理完成: {success}/{processed} 成功, 剩余 {len(remaining_queue)} 项")

    def process_register_queue(self, dt=None):
        """处理注册队列"""
        try:
            queue_file, queue = self._initialize_queue_processing()
            if queue_file is None:
                return

            logger.info(f"发现注册队列，队列大小: {len(queue) if queue else 0}")

            from mobile.utils.cloud_api import get_cloud_api
            cloud_api = get_cloud_api()

            processed, success, remaining_queue = self._process_queue_batch(queue, cloud_api)
            self._save_remaining_queue(queue_file, remaining_queue, queue)
            self._finalize_queue_processing(processed, success, remaining_queue)

        except Exception as e:
            logger.error(f"处理注册队列时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

if __name__ == "__main__":
    def safe_run_app() -> None:
        """安全运行应用程序，捕获所有Canvas相关错误"""
        try:
            # 启动性能监控
            from mobile.utils.performance_monitor import get_startup_timer, schedule_performance_logging
            startup_timer = get_startup_timer()
            startup_timer.start_phase("应用初始化")

            # 延迟注册图标字体，减少启动时间
            startup_timer.start_phase("图标字体注册")
            try:
                from icon_font_setup import register_icon_font
                register_icon_font()
            except ImportError as e:
                logger.warning(f"无法导入图标字体设置: {e}")
            except Exception as e:
                logger.error(f"注册图标字体时出错: {e}")
            startup_timer.end_phase("图标字体注册")

            # 启动应用
            startup_timer.start_phase("应用启动")
            app = HealthApp()
            startup_timer.end_phase("应用启动")

            # 启动性能日志记录
            schedule_performance_logging()

            # 记录启动完成
            startup_timer.end_phase("应用初始化")
            
            # 重写应用程序的异常处理
            from kivy.base import ExceptionManager, ExceptionHandler
            
            class CanvasExceptionHandler(ExceptionHandler):
                def handle_exception(self, exception):
                    if isinstance(exception, IndexError) and 'RenderContext' in str(exception):
                        logger.warning(f"Canvas IndexError已安全处理: {exception}")
                    # 所有情况都返回RAISE，与基类类型匹配
                    return ExceptionManager.RAISE
            
            ExceptionManager.add_handler(CanvasExceptionHandler())
            
            # 运行应用
            app.run()
        except IndexError as e:
            if 'RenderContext' in str(e) or 'pop_state' in str(e):
                logger.warning(f"Canvas IndexError已在主循环中安全处理: {e}")
                # 尝试重新启动应用程序
                import time
                time.sleep(1)
                safe_run_app()
            else:
                raise
        except Exception as e:
            logger.error(f"应用程序运行时发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    safe_run_app()
