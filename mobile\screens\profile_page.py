from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty
from kivy.clock import Clock
from kivy.lang import Builder
import os
import json
from typing import Optional, Dict, Any, List, Callable
import logging

# 设置日志
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDIcon
from kivymd.uix.label import MDLabel
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.card import MDCard
from kivymd.uix.dialog import MDDialog, MDDialogHeadlineText, MDDialogContentContainer, MDDialogButtonContainer
from kivymd.uix.list import MDListItem, MDListItemLeadingIcon, MDListItemHeadlineText, MDListItemSupportingText, MDList
from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.textfield import MDTextField, MDTextFieldHintText, MDTextFieldHelperText

# 添加缺失的导入
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.divider import MDDivider

# 导入主题和字体样式
try:
    from mobile.theme import AppTheme, AppMetrics, FontStyles, FontManager
except ImportError:
    from theme import AppTheme, AppMetrics, FontStyles, FontManager

# 导入二维码工具
try:
    from mobile.utils.qrcode_utils import DynamicQRCode, QRCodeScanner
except ImportError:
    from utils.qrcode_utils import DynamicQRCode, QRCodeScanner

# 导入用户管理器
try:
    from mobile.utils.user_manager import get_user_manager
except ImportError:
    from utils.user_manager import get_user_manager

# 导入BaseScreen基类
try:
    from mobile.screens.base_screen import BaseScreen
except ImportError:
    from screens.base_screen import BaseScreen

# 定义KV语言字符串
KV = '''
<ProfileSettingItem>:
    md_bg_color: app.theme.CARD_BACKGROUND if app and hasattr(app, 'theme') else [0.95, 0.95, 0.95, 1]
    radius: [dp(10)]
    elevation: 0
    padding: [dp(15), dp(10), dp(10), dp(10)]
    on_release: root.on_item_press()

    MDListItemLeadingIcon:
        icon: root.icon
        theme_icon_color: "Custom"
        icon_color: app.theme.PRIMARY_COLOR if app and hasattr(app, 'theme') else [0.133, 0.46, 0.82, 1]

    MDListItemHeadlineText:
        text: root.title
        theme_text_color: "Primary"
        font_style: "Body"
        role: "small"

<ProfilePage>:
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_LIGHT if app and hasattr(app, 'theme') and app.theme is not None else [0.9, 0.95, 1.0, 1.0]
        Rectangle:
            pos: self.pos
            size: self.size

<ProfileSection>:
    orientation: 'vertical'
    size_hint_y: None
    height: self.minimum_height
    padding: [dp(16), dp(8), dp(16), dp(8)]
    spacing: dp(8)

    MDLabel:
        text: root.title
        font_style: "Title"
        role: "small"
        theme_text_color: "Primary"
        size_hint_y: None
        height: dp(32)

    # 设置区域容器
    MDBoxLayout:
        id: settings_container
        orientation: 'vertical'
        size_hint_y: None
        height: self.minimum_height
        spacing: dp(4)
        padding: [dp(8), dp(4), dp(8), dp(4)]
'''

# 加载KV
try:
    Builder.load_string(KV)
except Exception as e:
    print(f"KV加载失败: {e}")

class ProfileSettingItem(MDListItem):
    """个人资料设置项"""
    icon = StringProperty("account")
    title = StringProperty("设置项")

    def __init__(self, on_press_callback: Optional[Callable] = None, **kwargs):
        super(ProfileSettingItem, self).__init__(**kwargs)
        self.on_press_callback = on_press_callback

    def on_item_press(self) -> None:
        """处理项目点击事件"""
        if self.on_press_callback:
            self.on_press_callback()

class ProfileSection(MDBoxLayout):
    """个人资料设置分区"""
    title = StringProperty("设置分区")

    def __init__(self, **kwargs):
        super(ProfileSection, self).__init__(**kwargs)
        self.items_container: MDList = MDList()
        self.add_widget(self.items_container)

    def add_item(self, icon: str, title: str, on_press_callback: Optional[Callable] = None) -> ProfileSettingItem:
        """添加设置项"""
        item = ProfileSettingItem(
            icon=icon,
            title=title,
            on_press_callback=on_press_callback
        )
        self.items_container.add_widget(item)
        return item

class ProfilePage(BaseScreen):
    """个人资料页面 - 继承BaseScreen基类"""
    
    # 常量定义 - 替换重复的字符串字面量
    ERROR_CONTAINER_NOT_FOUND = "警告: 未找到可用的容器"
    ERROR_USER_INFO_NOT_FOUND = "未找到当前用户信息"
    ERROR_ADD_DOCTOR_FAILED = "添加医生失败"
    ERROR_DOCTOR_MANAGER_NOT_INITIALIZED = "医生管理器未初始化"
    
    username = StringProperty("未登录")
    custom_id = StringProperty("未注册")
    qr_code_texture = ObjectProperty(None)
    qr_manager = ObjectProperty(None)
    
    # 初始化标志，防止重复初始化
    _ui_initialized = BooleanProperty(False)
    _qrcode_initialized = BooleanProperty(False)

    def __init__(self, **kwargs):
        """初始化个人资料页面"""
        # 设置导航栏属性
        kwargs['screen_title'] = '个人资料'
        kwargs['show_top_bar'] = True
        kwargs['top_bar_action_icon'] = 'refresh'
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        # 添加初始化状态标记
        self._ui_initialized = False
        self._content_setup = False
        
        # 初始化属性
        self.qr_manager: Optional[DynamicQRCode] = None
        self.qrcode_image: Optional[Any] = None
        self.dialog: Optional[MDDialog] = None
        self.scanner: Optional[QRCodeScanner] = None

    def on_enter(self, *args):
        """页面进入时调用"""
        super().on_enter(*args)
        # 延迟初始化二维码
        Clock.schedule_once(self.init_qrcode_manager, 0.5)
        # 加载用户信息
        self.load_user_info()

    def do_content_setup(self):
        """在content_container中添加内容"""
        try:
            # 安全地获取content_container
            content_container = None
            if hasattr(self, 'ids') and isinstance(self.ids, dict):
                content_container = self.ids.get('content_container')
            
            if not content_container:
                print("[ProfilePage] ERROR: 无法找到content_container")
                return
            
            # 清空content_container中的现有内容
            content_container.clear_widgets()
            
            # 创建主内容区域
            from kivymd.uix.card import MDCard
            from kivymd.uix.label import MDLabel
            from kivymd.uix.boxlayout import MDBoxLayout
            
            # 主内容区域 - 直接添加到content_container
            main_layout = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=int(self.height if self.height > 0 else dp(800)),
                padding=[int(dp(16)), int(dp(16)), int(dp(16)), int(dp(100))],
                spacing=int(dp(20))
            )
            main_layout.bind(minimum_height=main_layout.setter('height'))
            
            # 直接将main_layout添加到content_container
            content_container.add_widget(main_layout)
            
            # 延迟加载用户信息和设置区域
            Clock.schedule_once(lambda dt: self.load_user_info(), 0.2)
            Clock.schedule_once(lambda dt: self.add_settings_sections(main_layout), 0.3)
            Clock.schedule_once(self.init_qrcode_manager, 1.0)
            
            print("[ProfilePage] 成功添加内容到content_container")
        except Exception as e:
            print(f"[ProfilePage] 添加内容到content_container失败: {e}")
            import traceback
            traceback.print_exc()

    def add_settings_sections(self, main_layout) -> None:
        """添加设置区域到主布局"""
        try:
            # 添加各个设置区域到主布局
            self.add_personal_info_section(main_layout)
            self.add_account_security_section(main_layout)
            self.add_legal_support_section(main_layout)
            self.add_system_section(main_layout)
        except Exception as e:
            print(f"添加设置区域失败: {e}")
            import traceback
            traceback.print_exc()

    def init_qrcode_manager(self, dt: int = 0) -> bool:
        """初始化二维码管理器"""
        # 检查是否已经初始化，避免重复执行
        if self._qrcode_initialized:
            return True
            
        # 确保UI已经初始化
        if not hasattr(self, 'qrcode_container') or not self.qrcode_container:
            Clock.schedule_once(self.init_qrcode_manager, 0.5)
            return True

        try:
            # 获取用户管理器和当前用户
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            
            if current_user and hasattr(current_user, 'custom_id') and current_user.custom_id:
                custom_id = current_user.custom_id
                self.qr_manager = DynamicQRCode(custom_id, update_interval=60)

                # 设置二维码更新回调
                user_data = {
                    "name": self.username,
                    "custom_id": str(custom_id)
                }
                health_data = {
                    "blood_pressure": {"systolic": 120, "diastolic": 80},
                    "blood_glucose": {"value": 5.5},
                    "temperature": 36.5
                }
                logo_path = "assets/icons/health-Logo.png"
                
                if self.qr_manager:
                    self.qr_manager.start(
                        user_data=user_data,
                        health_data=health_data,
                        logo_path=logo_path,
                        callback=self.update_qrcode_display
                    )
                
                # 标记为已初始化
                self._qrcode_initialized = True
                return True
            else:
                # 创建占位符二维码
                self.create_placeholder_qrcode()
                return False
                
        except Exception as e:
            print(f"二维码初始化失败: {e}")
            import traceback
            traceback.print_exc()
            # 创建一个占位的二维码区域
            self.create_placeholder_qrcode()
            return True


    
    def create_placeholder_qrcode(self):
        """创建占位符二维码"""
        try:
            if hasattr(self, 'qrcode_container') and self.qrcode_container:
                self.qrcode_container.clear_widgets()
                placeholder = MDLabel(
                    text="健康码\n生成中...",
                    halign='center',
                    theme_text_color="Secondary",
                    font_style="Label"
                )
                self.qrcode_container.add_widget(placeholder)
        except Exception as e:
            print(f"创建占位符二维码失败: {e}")

    def update_qrcode_display(self, texture) -> None:
        """更新二维码显示"""
        print(f"update_qrcode_display 被调用，接收到纹理: {texture}")
        
        try:
            # 获取二维码容器（使用实例属性）
            if not hasattr(self, 'qrcode_container') or not self.qrcode_container:
                print("无法找到二维码容器")
                return
            
            # 清空现有内容
            self.qrcode_container.clear_widgets()
            
            if not texture:
                print("纹理为空，显示占位符")
                # 显示占位符文本
                from kivymd.uix.label import MDLabel
                placeholder = MDLabel(
                    text="二维码生成失败",
                    halign='center',
                    theme_text_color="Error",
                    font_style="Label"
                )
                self.qrcode_container.add_widget(placeholder)
                
                # 显示错误提示
                try:
                    from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
                    snackbar = MDSnackbar(
                        MDSnackbarText(text="二维码生成失败，请重试"),
                        duration=3
                    )
                    snackbar.open()
                except Exception as snackbar_error:
                    print(f"显示Snackbar失败: {snackbar_error}")
                return
            
            # 创建新的二维码图像组件
            from kivy.uix.image import Image
            qr_image = Image(
                texture=texture,
                size_hint=(1, 1),
                fit_mode="contain",
                pos_hint={"center_x": 0.5, "center_y": 0.5}
            )
            print("创建新的二维码图像组件")
            
            # 添加到容器
            self.qrcode_container.add_widget(qr_image)
            print("二维码图像组件已添加到容器")
            
            # 验证组件是否正确添加
            if len(self.qrcode_container.children) > 0:
                print("二维码图像组件添加成功")
            else:
                print("警告：二维码图像组件可能未正确添加")
                
        except Exception as e:
            print(f"更新二维码显示失败: {e}")
            import traceback
            traceback.print_exc()
            # 显示错误信息
            try:
                from kivymd.uix.label import MDLabel
                error_label = MDLabel(
                    text="二维码显示失败",
                    halign='center',
                    theme_text_color="Error",
                    font_style="Label"
                )
                if hasattr(self, 'qrcode_container') and self.qrcode_container:
                    self.qrcode_container.clear_widgets()
                    self.qrcode_container.add_widget(error_label)
                
                # 显示错误提示
                try:
                    from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
                    snackbar = MDSnackbar(
                        MDSnackbarText(text="二维码显示失败"),
                        duration=3
                    )
                    snackbar.open()
                except Exception as snackbar_error:
                    print(f"显示Snackbar失败: {snackbar_error}")
            except Exception as ui_error:
                print(f"显示错误信息也失败: {ui_error}")

    def load_user_info(self) -> None:
        """加载用户信息"""
        # 防止重复加载用户信息
        if hasattr(self, '_user_info_loaded') and self._user_info_loaded:
            return
            
        try:
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            
            if current_user:
                self._process_user_data(current_user, user_manager)
                self._user_info_loaded = True
        except Exception as e:
            print(f"加载用户信息失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _process_user_data(self, current_user, user_manager) -> None:
        """处理用户数据"""
        # 设置用户名
        self.username = self._get_user_display_name(current_user)
        
        # 设置custom_id
        self.custom_id = self._get_user_custom_id(current_user, user_manager)
        
        # 获取并设置用户身份
        user_identity = self._get_user_identity(current_user)
        
        # 延迟更新UI标签
        Clock.schedule_once(lambda dt: self._update_ui_labels(user_identity), 0.1)
    
    def _get_user_display_name(self, current_user) -> str:
        """获取用户显示名称"""
        return (getattr(current_user, 'full_name', None) or 
                getattr(current_user, 'real_name', None) or 
                current_user.username)
    
    def _get_user_custom_id(self, current_user, user_manager) -> str | None:
        """获取用户custom_id"""
        app = MDApp.get_running_app()
        
        # 优先使用后端返回的custom_id
        backend_custom_id = self._get_backend_custom_id(app)
        
        if backend_custom_id:
            self._sync_backend_custom_id(current_user, backend_custom_id, user_manager)
            return backend_custom_id
        
        # 使用本地存储的custom_id
        return getattr(current_user, 'custom_id', None)
    
    def _get_backend_custom_id(self, app) -> str | None:
        """从应用获取后端custom_id"""
        if (app and hasattr(app, 'user_data') and 
            isinstance(app.user_data, dict) and 
            'custom_id' in app.user_data):
            return app.user_data.get('custom_id')
        return None
    
    def _sync_backend_custom_id(self, current_user, backend_custom_id, user_manager) -> None:
        """同步后端custom_id到本地"""
        if hasattr(current_user, 'custom_id'):
            current_user.custom_id = backend_custom_id
            user_manager.save_accounts()
    
    def _get_user_identity(self, current_user) -> str:
        """获取用户身份"""
        user_identity = getattr(current_user, 'role', 
                               getattr(current_user, 'identity', '个人用户'))
        
        # 将英文角色转换为中文显示
        if user_identity.lower() == "personal":
            user_identity = "个人用户"
            
        return user_identity
    
    def _get_current_user_id(self) -> str:
        """获取当前登录用户ID (实际返回custom_id)"""
        try:
            # 尝试从用户管理器获取用户ID
            custom_id = self._try_get_user_id_from_manager()
            if custom_id:
                return custom_id
                
            # 尝试从应用获取用户ID
            custom_id = self._try_get_user_id_from_app()
            if custom_id:
                return custom_id
            
            print("[ProfilePage] 无法获取有效的用户ID，使用默认值")
            return 'default_user'
        except Exception as e:
            print(f"[ProfilePage] 获取用户ID失败: {e}")
            return 'default_user'
    
    def _try_get_user_id_from_manager(self) -> Optional[str]:
        """尝试从用户管理器获取用户ID"""
        try:
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            if current_user and hasattr(current_user, 'custom_id') and current_user.custom_id:
                return current_user.custom_id
        except Exception as e:
            print(f"[ProfilePage] 从用户管理器获取用户ID失败: {e}")
        return None
    
    def _try_get_user_id_from_app(self) -> Optional[str]:
        """尝试从应用获取用户ID"""
        try:
            app = MDApp.get_running_app()
            if app is not None and hasattr(app, 'user_data') and app.user_data:
                user_data = app.user_data
                if isinstance(user_data, dict) and 'custom_id' in user_data:
                    custom_id = user_data.get('custom_id')
                    if custom_id:
                        return custom_id
        except Exception as e:
            print(f"[ProfilePage] 从应用获取用户ID失败: {e}")
        return None

    def _update_ui_labels(self, user_identity: str) -> None:
        """更新UI标签"""
        try:
            # 通过ids访问KV中定义的组件
            username_label = self.ids.get('username_label')
            if username_label:
                username_label.text = self.username
            
            user_id_label = self.ids.get('user_id_label')
            if user_id_label:
                user_id_label.text = self.custom_id or "未注册"
            
            identity_label = self.ids.get('identity_label')
            if identity_label:
                identity_label.text = user_identity
                
        except Exception as e:
            print(f"更新UI标签失败: {e}")
            import traceback
            traceback.print_exc()

    def add_personal_info_section(self, main_layout) -> bool:
        """添加个人信息区域 - 包含用户信息卡片和设置项"""
        try:
            # 创建用户信息卡片（参考profile_page_old.py的布局）
            user_info_card = MDCard(
                orientation='vertical',
                size_hint=(None, None),
                size=(dp(340), dp(240)),
                pos_hint={"center_x": 0.5},
                padding=[dp(15), dp(15), dp(15), dp(15)],
                spacing=dp(10),
                elevation=3,
                radius=[dp(15)],
                md_bg_color=(1, 1, 1, 1)  # 使用白色背景
            )
            
            # 添加标题
            title_label = MDLabel(
                text="个人健康码",
                font_style="Body",
                role="medium",
                bold=True,
                theme_text_color="Primary",
                halign='center',
                size_hint_y=None,
                height=dp(30)
            )
            user_info_card.add_widget(title_label)
            
            # 主要内容区域 - 水平布局
            main_content = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=1,
                spacing=dp(8)
            )
            
            # 左侧：用户信息
            user_info_layout = MDBoxLayout(
                orientation='vertical',
                size_hint_x=0.5,
                spacing=dp(8),
                padding=[dp(5), dp(0), dp(0), dp(0)],
                pos_hint={"center_y": 0.5}
            )
            
            # 用户名区域
            name_box = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(25),
                spacing=dp(2)
            )
            name_label = MDLabel(
                text='真实姓名',
                font_style="Label",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(18)
            )
            username_label = MDLabel(
                text=self.username,
                font_style="Label",
                bold=True,
                theme_text_color="Primary",
                size_hint_y=None,
                height=dp(25)
            )
            name_box.add_widget(name_label)
            name_box.add_widget(username_label)
            
            # 身份区域
            identity_box = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(25),
                spacing=dp(2),
                padding=[0, dp(5), 0, 0]
            )
            identity_title = MDLabel(
                text='身份',
                font_style="Label",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(18)
            )
            identity_label = MDLabel(
                text="个人用户",
                font_style="Label",
                theme_text_color="Primary",
                size_hint_y=None,
                height=dp(40)
            )
            identity_box.add_widget(identity_title)
            identity_box.add_widget(identity_label)
            
            # 用户ID区域
            id_box = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(25),
                spacing=dp(2),
                padding=[0, dp(5), 0, 0]
            )
            id_title = MDLabel(
                text='用户ID',
                font_style="Label",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(18)
            )
            id_label = MDLabel(
                text=self.custom_id,
                font_style="Label",
                theme_text_color="Primary",
                size_hint_y=None,
                height=dp(25)
            )
            id_box.add_widget(id_title)
            id_box.add_widget(id_label)
            
            # 组装左侧用户信息
            user_info_layout.add_widget(name_box)
            user_info_layout.add_widget(identity_box)
            user_info_layout.add_widget(id_box)
            
            # 右侧：二维码容器
            self.qrcode_container = MDBoxLayout(
                orientation='vertical',
                size_hint_x=0.5,
                size_hint_y=1,
                height=dp(180),
                pos_hint={"center_x": 0.5, "center_y": 0.5},
                md_bg_color=(0.9, 0.9, 0.9, 1)  # 添加浅灰色背景以便查看
            )
            
            # 组装主要内容
            main_content.add_widget(user_info_layout)
            main_content.add_widget(self.qrcode_container)
            user_info_card.add_widget(main_content)
            
            # 底部注意事项
            notice_label = MDLabel(
                text="请保持健康码更新，以便及时获取健康状态",
                font_style="Label",
                theme_text_color="Secondary",
                halign='center',
                size_hint_y=None,
                height=dp(25)
            )
            user_info_card.add_widget(notice_label)
            
            # 添加用户信息卡片到主布局
            main_layout.add_widget(user_info_card)
            
            # 初始化占位符二维码
            self.create_placeholder_qrcode()
            
            # 创建个人信息设置区域
            section = ProfileSection(title="个人信息")
            
            # 直接添加到传入的main_layout
            if main_layout:
                main_layout.add_widget(section)
            else:
                print(self.ERROR_CONTAINER_NOT_FOUND)
                return False
            
            # 添加设置项
            section.add_item("account-edit", "个人信息修改", self.on_edit_personal_info)
            section.add_item("account-details", "健康档案", self.on_health_records)
            section.add_item("doctor", "医生管理", self.on_doctor_management)
            return True
        except Exception as e:
            print(f"添加个人信息设置区域失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def add_account_security_section(self, main_layout) -> bool:
        """添加账户与安全设置区域到主布局"""
        try:
            section = ProfileSection(title="账户与安全")

            # 直接添加到传入的main_layout
            if main_layout:
                main_layout.add_widget(section)
            else:
                print(self.ERROR_CONTAINER_NOT_FOUND)
                return False

            # 添加设置项
            section.add_item("key", "密码修改", self.on_change_password)
            section.add_item("shield-account", "隐私设置", self.on_privacy_settings)
            section.add_item("cellphone", "手机绑定", self.on_bind_phone)
            section.add_item("message-badge", "消息通知", self.on_notification_settings)
            section.add_item("account-switch", "切换用户", self.on_switch_user)
            section.add_item("account-convert", "身份切换", self.on_switch_identity)
            return True
        except Exception:
            return False

    def add_legal_support_section(self, main_layout) -> bool:
        """添加法律与支持设置区域到主布局"""
        try:
            section = ProfileSection(title="法律与支持")

            # 直接添加到传入的main_layout
            if main_layout:
                main_layout.add_widget(section)
            else:
                print(self.ERROR_CONTAINER_NOT_FOUND)
                return False

            # 添加设置项
            section.add_item("file-document", "用户协议", self.on_user_agreement)
            section.add_item("shield-lock", "隐私政策", self.on_privacy_policy)
            section.add_item("help-circle", "帮助中心", self.on_help_center)
            section.add_item("headset", "联系客服", self.on_contact_support)
            return True
        except Exception:
            return False

    def add_system_section(self, main_layout) -> bool:
        """添加系统设置区域到主布局"""
        try:
            section = ProfileSection(title="系统设置")

            # 直接添加到传入的main_layout
            if main_layout:
                main_layout.add_widget(section)
            else:
                print(self.ERROR_CONTAINER_NOT_FOUND)
                return False

            # 添加设置项
            section.add_item("theme-light-dark", "主题设置", self.on_theme_settings)
            section.add_item("alert", "关于我们", self.on_about)
            section.add_item("logout", "退出登录", self.on_logout)
            return True
        except Exception as e:
            print(f"添加系统设置区域失败: {e}")
            return False

    # 事件处理方法
    def on_edit_personal_info(self):
        """编辑个人信息"""
        try:
            from kivymd.uix.scrollview import MDScrollView
            from kivymd.uix.divider import MDDivider
            from kivymd.uix.textfield import MDTextField
            from kivymd.uix.segmentedbutton import MDSegmentedButton, MDSegmentedButtonItem
            
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            
            if not current_user:
                self.show_error(self.ERROR_USER_INFO_NOT_FOUND)
                return

            # 创建滚动视图
            scroll = MDScrollView(
                size_hint=(1, 1),
                do_scroll_x=False,
                do_scroll_y=True
            )

            # 创建内容容器
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                size_hint_y=None,
                height=dp(600)
            )

            # 添加不可修改信息部分
            content.add_widget(MDLabel(
                text="基本信息（不可修改）",
                font_style="Body",
                role="medium",
                bold=True,
                size_hint_y=None,
                height=dp(32),
                padding=[0, dp(10), 0, dp(5)]
            ))

            # 获取用户ID，优先使用custom_id
            user_id = current_user.custom_id if hasattr(current_user, 'custom_id') and current_user.custom_id else current_user.user_id

            # 显示不可修改的信息
            fixed_info = [
                ("用户名", current_user.username),
                ("用户ID", user_id),
                ("姓名", current_user.real_name or current_user.username)
            ]

            for label, value in fixed_info:
                item_layout = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(32),
                    spacing=dp(10)
                )
                item_layout.add_widget(MDLabel(
                    text=f"{label}:",
                    font_style="Body",
                    role="small",
                    theme_text_color="Secondary",
                    size_hint_y=None,
                    height=dp(24),
                    size_hint_x=0.3
                ))
                item_layout.add_widget(MDLabel(
                    text=str(value),
                    font_style="Body",
                    role="small",
                    size_hint_y=None,
                    height=dp(24),
                    size_hint_x=0.7
                ))
                content.add_widget(item_layout)

            # 添加可修改信息部分
            content.add_widget(MDLabel(
                text="可修改信息",
                font_style="Body",
                role="medium",
                bold=True,
                size_hint_y=None,
                height=dp(32),
                padding=[0, dp(10), 0, dp(5)]
            ))

            # 姓名输入
            self.name_input = MDTextField(
                hint_text="姓名",
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                text=str(getattr(current_user, 'real_name', '') or getattr(current_user, 'full_name', '') or getattr(current_user, 'username', ''))
            )
            content.add_widget(self.name_input)

            # 性别选择
            gender_layout = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=dp(80),
                spacing=dp(5)
            )

            gender_label = MDLabel(
                text="性别",
                theme_text_color="Secondary",
                font_style="Body",
                size_hint_y=None,
                height=dp(24)
            )
            gender_layout.add_widget(gender_label)

            # 创建分段按钮作为性别选择器
            self.gender_segment = MDSegmentedButton(
                size_hint_y=None,
                height=dp(48)
            )

            # 添加性别选项
            genders = ["男", "女", "其他"]
            current_gender = getattr(current_user, 'gender', '男')

            for gender in genders:
                button_item = MDSegmentedButtonItem()
                button_item.add_widget(MDButtonText(text=gender))
                self.gender_segment.add_widget(button_item)
                if gender == current_gender:
                    button_item.active = True

            gender_layout.add_widget(self.gender_segment)
            content.add_widget(gender_layout)

            # 年龄输入
            self.age_input = MDTextField(
                hint_text="年龄",
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                input_filter="int",
                text=str(getattr(current_user, 'age', ''))
            )
            content.add_widget(self.age_input)

            # 手机号输入
            self.phone_input = MDTextField(
                hint_text="手机号",
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                input_filter="int",
                text=str(getattr(current_user, 'phone', ''))
            )
            content.add_widget(self.phone_input)

            # 身份证号输入
            self.id_card_input = MDTextField(
                hint_text="身份证号（选填）",
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                text=str(getattr(current_user, 'id_number', ''))
            )
            content.add_widget(self.id_card_input)

            # 添加内容到滚动视图
            scroll.add_widget(content)

            # 显示对话框 - 符合KivyMD 2.0.1规范
            self.personal_info_dialog = MDDialog(
                size_hint=(0.9, None),
                height=dp(500),
                radius=[dp(16)]
            )
            
            # 添加标题
            self.personal_info_dialog.add_widget(
                MDDialogHeadlineText(
                    text="编辑个人信息",
                    halign="center"
                )
            )
            
            # 添加内容容器
            content_container = MDDialogContentContainer()
            content_container.add_widget(scroll)
            self.personal_info_dialog.add_widget(content_container)
            
            # 添加按钮容器
            button_container = MDDialogButtonContainer(
                spacing=dp(8),
                adaptive_width=True,
                pos_hint={"right": 1}
            )
            
            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(self.personal_info_dialog)
            )
            
            save_button = MDButton(
                MDButtonText(text="保存"),
                style="text",
                on_release=lambda x: self.save_personal_info()
            )
            
            button_container.add_widget(cancel_button)
            button_container.add_widget(save_button)
            self.personal_info_dialog.add_widget(button_container)
            self.personal_info_dialog.open()
        except Exception as e:
            print(f"显示个人信息编辑对话框失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("无法显示个人信息编辑对话框")

    def save_personal_info(self):
        """保存个人信息"""
        try:
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            
            if not current_user:
                self.show_error(self.ERROR_USER_INFO_NOT_FOUND)
                return

            # 获取并验证输入数据
            user_data = self._collect_personal_info_input()
            if not self._validate_personal_info(user_data):
                return

            # 更新用户信息
            self._update_user_attributes(current_user, user_data)
            
            # 保存并完成
            self._complete_personal_info_save(user_manager)
            
        except Exception as e:
            print(f"保存个人信息失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("保存个人信息失败")
    
    def _collect_personal_info_input(self) -> dict:
        """收集个人信息输入数据"""
        return {
            'name': self.name_input.text.strip(),
            'age': self.age_input.text.strip(),
            'phone': self.phone_input.text.strip(),
            'id_card': self.id_card_input.text.strip(),
            'gender': self._get_selected_gender()
        }
    
    def _get_selected_gender(self) -> str:
        """获取选中的性别"""
        for item in self.gender_segment.children:
            if hasattr(item, 'active') and item.active:
                for child in item.children:
                    if hasattr(child, 'text'):
                        return child.text
        return "男"  # 默认值
    
    def _validate_personal_info(self, user_data: dict) -> bool:
        """验证个人信息输入"""
        if not user_data['name']:
            self.show_error("姓名不能为空")
            return False

        if user_data['phone'] and len(user_data['phone']) != 11:
            self.show_error("手机号格式不正确")
            return False

        if user_data['age'] and not self._is_valid_age(user_data['age']):
            self.show_error("年龄格式不正确")
            return False
            
        return True
    
    def _is_valid_age(self, age_str: str) -> bool:
        """验证年龄格式"""
        return age_str.isdigit() and 0 <= int(age_str) <= 150
    
    def _update_user_attributes(self, current_user, user_data: dict) -> None:
        """更新用户属性"""
        # 更新姓名
        if hasattr(current_user, 'real_name'):
            current_user.real_name = user_data['name']
        elif hasattr(current_user, 'full_name'):
            current_user.full_name = user_data['name']
        
        # 更新其他属性
        self._set_user_attribute(current_user, 'gender', user_data['gender'])
        
        if user_data['age']:
            self._set_user_attribute(current_user, 'age', int(user_data['age']))
        
        if user_data['phone']:
            self._set_user_attribute(current_user, 'phone', user_data['phone'])
        
        if user_data['id_card']:
            self._set_user_attribute(current_user, 'id_number', user_data['id_card'])
    
    def _set_user_attribute(self, user, attr_name: str, value) -> None:
        """安全设置用户属性"""
        if hasattr(user, attr_name):
            setattr(user, attr_name, value)
    
    def _complete_personal_info_save(self, user_manager) -> None:
        """完成个人信息保存流程"""
        user_manager.save_accounts()
        self.load_user_info()
        self.dismiss_dialog(self.personal_info_dialog)
        self.show_success("个人信息保存成功")

    def on_health_records(self):
        """健康档案"""
        try:
            # 创建健康档案对话框
            dialog = MDDialog(
                title="我的健康档案",
                type="custom",
                content_cls=MDBoxLayout(
                    orientation="vertical",
                    spacing=dp(10),
                    size_hint_y=None,
                    height=dp(500)
                ),
                buttons=[
                    MDButton(
                        MDButtonText(text="关闭"),
                        style="text",
                        on_release=lambda x: dialog.dismiss()
                    )
                ],
                size_hint=(0.9, 0.8)
            )
            
            # 创建滚动视图
            scroll = MDScrollView()
            content_container = MDBoxLayout(
                orientation="vertical",
                spacing=dp(10),
                padding=[dp(20), dp(10), dp(20), dp(10)],
                size_hint_y=None
            )
            content_container.bind(minimum_height=content_container.setter('height'))
            
            # 添加说明文本
            info_label = MDLabel(
                text="这里显示您的健康档案信息，包括健康记录、体检报告、医疗记录等。",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(40)
            )
            content_container.add_widget(info_label)
            
            # 健康记录类型列表
            record_types = [
                {"type": "health_diary", "title": "健康日记", "icon": "book-open", "desc": "日常健康记录"},
                {"type": "medical", "title": "医疗记录", "icon": "hospital-box", "desc": "门诊、住院记录"},
                {"type": "exam", "title": "检查报告", "icon": "file-document", "desc": "各类检查报告"},
                {"type": "physical", "title": "体检报告", "icon": "clipboard-pulse", "desc": "体检结果报告"},
                {"type": "medication", "title": "用药记录", "icon": "pill", "desc": "药物使用记录"},
                {"type": "questionnaire", "title": "问卷调查", "icon": "clipboard-text", "desc": "健康问卷记录"},
                {"type": "assessment", "title": "量表评估", "icon": "chart-line", "desc": "各类评估量表"},
                {"type": "other", "title": "其他记录", "icon": "folder", "desc": "其他健康信息"}
            ]
            
            # 为每种记录类型创建卡片
            for record_type in record_types:
                card = MDCard(
                    size_hint_y=None,
                    height=dp(80),
                    elevation=2,
                    padding=dp(10),
                    on_release=lambda x, rt=record_type["type"]: self.show_health_records_by_type(rt, dialog)
                )
                
                card_layout = MDBoxLayout(
                    orientation="horizontal",
                    spacing=dp(15),
                    adaptive_width=True
                )
                
                # 图标
                icon = MDIcon(
                    icon=record_type["icon"],
                    size_hint=(None, None),
                    size=(dp(40), dp(40)),
                    theme_icon_color="Primary"
                )
                card_layout.add_widget(icon)
                
                # 文本信息
                text_layout = MDBoxLayout(
                    orientation="vertical",
                    spacing=dp(5)
                )
                
                title_label = MDLabel(
                    text=record_type["title"],
                    font_style="Body",
                    role="medium",
                    bold=True,
                    size_hint_y=None,
                    height=dp(25)
                )
                text_layout.add_widget(title_label)
                
                desc_label = MDLabel(
                    text=record_type["desc"],
                    theme_text_color="Secondary",
                    font_style="Body",
                    role="small",
                    size_hint_y=None,
                    height=dp(20)
                )
                text_layout.add_widget(desc_label)
                
                card_layout.add_widget(text_layout)
                card.add_widget(card_layout)
                content_container.add_widget(card)
            
            scroll.add_widget(content_container)
            dialog.content_cls.add_widget(scroll)
            dialog.open()
            
        except Exception as e:
            self.show_info(f"打开健康档案失败: {str(e)}")
    
    def show_health_records_by_type(self, record_type, parent_dialog):
        """显示指定类型的健康记录"""
        try:
            # 关闭父对话框
            parent_dialog.dismiss()
            
            # 创建记录列表对话框
            dialog = MDDialog(
                title=f"健康记录 - {self.get_record_type_name(record_type)}",
                type="custom",
                content_cls=MDBoxLayout(
                    orientation="vertical",
                    spacing=dp(10),
                    size_hint_y=None,
                    height=dp(500)
                ),
                buttons=[
                    MDButton(
                        MDButtonText(text="返回"),
                        style="text",
                        on_release=lambda x: (dialog.dismiss(), self.on_health_records())
                    ),
                    MDButton(
                        MDButtonText(text="关闭"),
                        style="text",
                        on_release=lambda x: dialog.dismiss()
                    )
                ],
                size_hint=(0.9, 0.8)
            )
            
            # 创建滚动视图
            scroll = MDScrollView()
            content_container = MDBoxLayout(
                orientation="vertical",
                spacing=dp(10),
                padding=[dp(20), dp(10), dp(20), dp(10)],
                size_hint_y=None
            )
            content_container.bind(minimum_height=content_container.setter('height'))
            
            # 加载健康记录数据
            records = self.load_health_records_by_type(record_type)
            
            if not records:
                # 没有记录时显示提示
                no_data_label = MDLabel(
                    text=f"暂无{self.get_record_type_name(record_type)}记录",
                    theme_text_color="Secondary",
                    halign="center",
                    size_hint_y=None,
                    height=dp(100)
                )
                content_container.add_widget(no_data_label)
            else:
                # 显示记录列表
                for record in records:
                    record_card = self.create_health_record_card(record)
                    content_container.add_widget(record_card)
            
            scroll.add_widget(content_container)
            dialog.content_cls.add_widget(scroll)
            dialog.open()
            
        except Exception as e:
            self.show_info(f"加载健康记录失败: {str(e)}")
    
    def get_record_type_name(self, record_type):
        """获取记录类型的中文名称"""
        type_names = {
            "health_diary": "健康日记",
            "medical": "医疗记录",
            "exam": "检查报告",
            "physical": "体检报告",
            "medication": "用药记录",
            "questionnaire": "问卷调查",
            "assessment": "量表评估",
            "other": "其他记录"
        }
        return type_names.get(record_type, "未知类型")
    
    def load_health_records_by_type(self, record_type):
        """根据类型加载健康记录"""
        try:
            # 暂时返回模拟数据
            mock_records = [
                {
                    "id": 1,
                    "title": f"示例{self.get_record_type_name(record_type)}1",
                    "date": "2024-01-15",
                    "description": "这是一条示例记录描述",
                    "type": record_type
                },
                {
                    "id": 2,
                    "title": f"示例{self.get_record_type_name(record_type)}2",
                    "date": "2024-01-10",
                    "description": "这是另一条示例记录描述",
                    "type": record_type
                }
            ]
            return mock_records
        except Exception as e:
            print(f"加载健康记录失败: {e}")
            return []
    
    def create_health_record_card(self, record):
        """创建健康记录卡片"""
        card = MDCard(
            size_hint_y=None,
            height=dp(100),
            elevation=2,
            padding=dp(15),
            on_release=lambda x: self.show_health_record_detail(record)
        )
        
        card_layout = MDBoxLayout(
            orientation="vertical",
            spacing=dp(8)
        )
        
        # 标题和日期行
        title_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(30)
        )
        
        title_label = MDLabel(
            text=record.get("title", "未知标题"),
            font_style="Body",
            role="medium",
            bold=True,
            size_hint_x=0.7
        )
        title_layout.add_widget(title_label)
        
        date_label = MDLabel(
            text=record.get("date", "未知日期"),
            theme_text_color="Secondary",
            font_style="Body",
            role="small",
            halign="right",
            size_hint_x=0.3
        )
        title_layout.add_widget(date_label)
        
        card_layout.add_widget(title_layout)
        
        # 描述
        desc_label = MDLabel(
            text=record.get("description", "无描述"),
            theme_text_color="Secondary",
            font_style="Body",
            role="small",
            size_hint_y=None,
            height=dp(40)
        )
        card_layout.add_widget(desc_label)
        
        card.add_widget(card_layout)
        return card
    
    def show_health_record_detail(self, record):
        """显示健康记录详情"""
        try:
            detail_dialog = MDDialog(
                title=record.get("title", "记录详情"),
                text=f"日期: {record.get('date', '未知')}\n\n描述: {record.get('description', '无描述')}\n\n这里可以显示更详细的记录内容。",
                buttons=[
                    MDButton(
                        MDButtonText(text="关闭"),
                        style="text",
                        on_release=lambda x: detail_dialog.dismiss()
                    )
                ]
            )
            detail_dialog.open()
        except Exception as e:
            self.show_info(f"显示记录详情失败: {str(e)}")
        
    def on_doctor_management(self):
        """医生管理"""
        try:
            # 初始化医生管理器（如果尚未初始化）
            if not hasattr(self, 'doctor_manager') or not self.doctor_manager:
                from utils.doctor_manager import DoctorManager
                self.doctor_manager = DoctorManager("user123")  # 在实际应用中应使用当前用户ID

            # 创建对话框内容
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(10),
                padding=[dp(20), dp(10), dp(20), dp(10)],
                size_hint_y=None,
                height=dp(32)
            )

            # 添加标题
            title_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(32)
            )

            title_label = MDLabel(
                text="我的医生",
                font_style="Body",
                role="medium",
                bold=True,
                size_hint_y=None,
                height=dp(32),
                size_hint_x=0.7
            )
            title_layout.add_widget(title_label)

            # 添加医生按钮
            add_btn = MDIconButton(
                icon="account-plus",
                on_release=lambda x: self.show_add_doctor_dialog(),
                pos_hint={"center_y": 0.5}
            )
            title_layout.add_widget(add_btn)

            content.add_widget(title_layout)

            # 创建医生列表滚动视图
            scroll = MDScrollView(
                size_hint=(1, None),
                height=dp(250)
            )

            # 创建医生列表容器
            self.doctors_list = MDList()

            # 更新医生列表
            self.update_doctors_list()

            scroll.add_widget(self.doctors_list)
            content.add_widget(scroll)

            # 添加按钮
            btn_layout = MDBoxLayout(
                orientation='horizontal',
                spacing=dp(10),
                size_hint_y=None,
                height=dp(32),
                pos_hint={"center_x": 0.5},
                padding=[0, dp(10), 0, 0]
            )

            # 扫码添加按钮
            scan_btn = MDButton(
                style="filled",
                on_release=lambda x: self.on_add_doctor_by_scan(),
                size_hint=(None, None),
                size=(dp(130), dp(45))
            )
            scan_btn.add_widget(MDButtonText(text="扫码添加"))
            btn_layout.add_widget(scan_btn)

            # 搜索医生按钮
            search_btn = MDButton(
                style="outlined",
                on_release=lambda x: self.on_search_doctor(),
                size_hint=(None, None),
                size=(dp(130), dp(45))
            )
            search_btn.add_widget(MDButtonText(text="搜索医生"))
            btn_layout.add_widget(search_btn)

            content.add_widget(btn_layout)

            # 显示对话框
            self.doctor_dialog = MDDialog()
            self.doctor_dialog.ids.container.add_widget(content)
            close_button = MDButton(
                MDButtonText(text="关闭"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(self.doctor_dialog)
            )
            self.doctor_dialog.ids.button_container.add_widget(close_button)
            self.doctor_dialog.open()
        except Exception as e:
            print(f"显示医生管理失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("无法显示医生管理")

    def update_doctors_list(self):
        """更新医生列表"""
        if not hasattr(self, 'doctors_list') or not self.doctors_list:
            return

        if not hasattr(self, 'doctor_manager') or not self.doctor_manager:
            from utils.doctor_manager import DoctorManager
            self.doctor_manager = DoctorManager("user123")  # 在实际应用中应使用当前用户ID

        # 清空列表
        self.doctors_list.clear_widgets()

        # 获取所有医生
        doctors = self.doctor_manager.get_all_doctors()

        if not doctors:
            # 如果没有医生，显示提示
            empty_label = MDLabel(
                text="暂无关联医生，请添加",
                halign="center",
                theme_text_color="Secondary",
                font_style="Body",
                size_hint_y=None,
                height=dp(32)
            )
            self.doctors_list.add_widget(empty_label)
            return

        # 添加医生项
        for doctor in doctors:
            item = MDListItem()

            # 添加医生图标
            item.add_widget(MDListItemLeadingIcon(
                icon="doctor",
                theme_icon_color="Custom",
                icon_color=self.get_safe_primary_color()
            ))

            # 添加医生信息
            item.add_widget(MDListItemHeadlineText(
                text=f"{doctor['name']} ({doctor['department']})"
            ))

            # 添加医院信息
            item.add_widget(MDListItemSupportingText(
                text=f"{doctor['hospital']}"
            ))

            # 创建操作按钮容器 - 使用正确的KivyMD 2.0.1组件
            from kivymd.uix.button import MDIconButton
            actions_container = MDIconButton(
                icon="dots-vertical",
                theme_icon_color="Custom",
                on_release=lambda x, doc=doctor: self.show_doctor_actions(doc)
            )

            item.add_widget(actions_container)
            self.doctors_list.add_widget(item)

    def show_doctor_actions(self, doctor):
        """显示医生操作菜单"""
        try:
            # 调用已有的医生操作菜单方法
            self.show_doctor_actions_menu(doctor)
        except Exception as e:
            print(f"Error showing doctor actions: {e}")

    def create_user_item(self, account_id, name, email, role):
        """创建用户列表项"""
        from kivymd.uix.list import MDListItem, MDListItemHeadlineText
        item = MDListItem(
            on_release=lambda x, uid=account_id: self.show_user_details(uid),
        )
        
        # 添加文本内容
        item.add_widget(MDListItemHeadlineText(
            text=f"{name} ({email}) - {role}"
        ))

        # 添加操作按钮
        from kivymd.uix.button import MDIconButton
        item.add_widget(MDIconButton(
            icon="dots-vertical",
            theme_icon_color="Custom",
            on_release=lambda x, uid=account_id: self.show_user_actions(uid)
        ))
        
        return item

    def show_user_details(self, user_id):
        """显示用户详情"""
        try:
            # 获取用户管理器
            user_manager = get_user_manager()
            
            # 查找用户
            user = None
            for account in user_manager.accounts:
                account_id = self._get_user_id(account)
                if account_id == user_id:
                    user = account
                    break
            
            if not user:
                self.show_error("未找到用户信息")
                return
            
            # 显示用户详情对话框
            self.show_info(f"用户详情:\n用户名: {getattr(user, 'username', 'N/A')}\n姓名: {getattr(user, 'real_name', getattr(user, 'full_name', 'N/A'))}\nID: {user_id}")
        except Exception as e:
            print(f"显示用户详情失败: {e}")
            self.show_error("无法显示用户详情")

    def show_doctor_actions_menu(self, doctor):
        """显示医生操作菜单"""
        try:
            # 创建菜单项
            menu_items = [
                {
                    "text": "电话联系",
                    "on_release": lambda x=None: self.on_contact_doctor(doctor['phone'])
                },
                {
                    "text": "删除医生",
                    "on_release": lambda x=None: self.confirm_delete_doctor(doctor['doctor_id'])
                }
            ]

            # 获取最近一次点击的控件作为caller
            from kivy.core.window import Window
            caller = Window.focus_widget or self

            # 创建下拉菜单
            dropdown_menu = MDDropdownMenu(
                caller=caller,
                items=menu_items,
                position="auto",
                width=dp(200)
            )

            dropdown_menu.open()
        except Exception as e:
            print(f"显示医生操作菜单失败: {e}")
            self.show_error("无法显示医生操作菜单")

    def show_add_doctor_dialog(self):
        """显示添加医生对话框"""
        if hasattr(self, 'doctor_dialog') and self.doctor_dialog:
            self.dismiss_dialog(self.doctor_dialog)

        # 创建内容容器
        content = MDBoxLayout(
            orientation='vertical',
            spacing=dp(15),
            padding=[dp(20), dp(20), dp(20), dp(0)],
            size_hint_y=None,
            height=dp(250)
        )

        # 添加输入框
        self.doctor_name_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(48)
        )
        self.doctor_name_input.add_widget(MDTextFieldHintText(text="医生姓名"))
        content.add_widget(self.doctor_name_input)

        self.doctor_department_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(48)
        )
        self.doctor_department_input.add_widget(MDTextFieldHintText(text="科室"))
        content.add_widget(self.doctor_department_input)

        self.doctor_hospital_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(48)
        )
        self.doctor_hospital_input.add_widget(MDTextFieldHintText(text="医院"))
        content.add_widget(self.doctor_hospital_input)

        self.doctor_phone_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(48),
            input_filter="int"
        )
        self.doctor_phone_input.add_widget(MDTextFieldHintText(text="联系电话"))
        self.doctor_phone_input.bind(text=self.limit_text_length)
        content.add_widget(self.doctor_phone_input)

        # 创建对话框
        self.add_doctor_dialog = MDDialog(
            size_hint=(0.9, None),
            height=dp(400),
            radius=[dp(20), dp(7), dp(20), dp(7)]
        )
        
        # 添加标题
        headline = MDDialogHeadlineText(text="添加医生")
        self.add_doctor_dialog.add_widget(headline)
        
        # 添加内容容器
        content_container = MDDialogContentContainer()
        content_container.add_widget(content)
        self.add_doctor_dialog.add_widget(content_container)
        
        # 添加按钮容器
        button_container = MDDialogButtonContainer(
            spacing=dp(8),
            adaptive_width=True,
            pos_hint={"right": 1}
        )
        
        cancel_button = MDButton(
            MDButtonText(text="取消"),
            style="text",
            on_release=lambda x: self.dismiss_dialog(self.add_doctor_dialog)
        )
        
        add_button = MDButton(
            MDButtonText(text="添加"),
            style="text",
            on_release=lambda x: self.confirm_add_doctor()
        )
        
        button_container.add_widget(cancel_button)
        button_container.add_widget(add_button)
        self.add_doctor_dialog.add_widget(button_container)
        
        self.add_doctor_dialog.open()

    def limit_text_length(self, instance, text):
        """限制文本输入长度"""
        max_length = 11  # 手机号最大长度
        if len(text) > max_length:
            instance.text = text[:max_length]

    def confirm_add_doctor(self):
        """确认添加医生"""
        # 获取输入的医生信息
        doctor_info = self._collect_doctor_input()
        
        # 验证输入
        if not self._validate_doctor_info(doctor_info):
            return

        # 添加医生
        self._process_add_doctor(doctor_info)
    
    def _collect_doctor_input(self) -> dict:
        """收集医生输入信息"""
        return {
            'name': self._get_input_text('doctor_name_input'),
            'department': self._get_input_text('doctor_department_input'),
            'hospital': self._get_input_text('doctor_hospital_input'),
            'phone': self._get_input_text('doctor_phone_input')
        }
    
    def _get_input_text(self, input_name: str) -> str:
        """安全获取输入框文本"""
        if hasattr(self, input_name):
            input_widget = getattr(self, input_name)
            if input_widget:
                return input_widget.text.strip()
        return ""
    
    def _validate_doctor_info(self, doctor_info: dict) -> bool:
        """验证医生信息"""
        if not doctor_info['name']:
            self.show_error("请输入医生姓名")
            return False

        if not doctor_info['department']:
            self.show_error("请输入科室")
            return False

        if not doctor_info['hospital']:
            self.show_error("请输入医院")
            return False

        if not doctor_info['phone'] or len(doctor_info['phone']) != 11:
            self.show_error("请输入有效的联系电话")
            return False
            
        return True
    
    def _process_add_doctor(self, doctor_info: dict) -> None:
        """处理添加医生操作"""
        if not hasattr(self, 'doctor_manager') or not self.doctor_manager:
            self.show_error(self.ERROR_DOCTOR_MANAGER_NOT_INITIALIZED)
            return
            
        doctor = self.doctor_manager.add_doctor(
            doctor_info['name'], 
            doctor_info['department'], 
            doctor_info['hospital'], 
            doctor_info['phone']
        )
        
        if doctor:
            self._handle_add_doctor_success(doctor_info['name'])
        else:
            self.show_error(self.ERROR_ADD_DOCTOR_FAILED)
    
    def _handle_add_doctor_success(self, doctor_name: str) -> None:
        """处理添加医生成功后的操作"""
        self.show_success(f"已添加医生：{doctor_name}")
        
        # 关闭添加对话框
        if hasattr(self, 'add_doctor_dialog') and self.add_doctor_dialog:
            self.dismiss_dialog(self.add_doctor_dialog)
            
        # 重新打开医生管理对话框
        self.on_doctor_management()

    def confirm_delete_doctor(self, doctor_id):
        """确认删除医生"""
        if not hasattr(self, 'doctor_manager') or not self.doctor_manager:
            self.show_error(self.ERROR_DOCTOR_MANAGER_NOT_INITIALIZED)
            return

        # 获取医生信息
        doctor = None
        for d in self.doctor_manager.get_all_doctors():
            if d['doctor_id'] == doctor_id:
                doctor = d
                break

        if not doctor:
            self.show_error("找不到该医生信息")
            return

        # 创建确认对话框
        confirm_dialog = MDDialog()
        confirm_dialog.ids.container.add_widget(MDLabel(text="删除医生", size_hint_y=None,
                height=dp(32), pos_hint={"center_x": 0.5}))
        confirm_dialog.ids.container.add_widget(MDLabel(text=f"确定要删除{doctor['name']}医生吗？此操作不可撤销。", size_hint_y=None,
                height=dp(32)))

        cancel_button = MDButton(
            MDButtonText(text="取消"),
            style="text",
            on_release=lambda x: self.dismiss_dialog(confirm_dialog)
        )

        delete_button = MDButton(
            MDButtonText(text="删除"),
            style="text",
            on_release=lambda x: self.do_delete_doctor(confirm_dialog, doctor_id)
        )

        confirm_dialog.ids.button_container.add_widget(cancel_button)
        confirm_dialog.ids.button_container.add_widget(delete_button)
        confirm_dialog.open()

    def do_delete_doctor(self, dialog, doctor_id):
        """执行删除医生操作"""
        if dialog:
            self.dismiss_dialog(dialog)

        if hasattr(self, 'doctor_manager') and self.doctor_manager:
            if self.doctor_manager.delete_doctor(doctor_id):
                self.show_success("医生已删除")
                # 重新打开医生管理对话框
                self.on_doctor_management()
            else:
                self.show_error(self.ERROR_ADD_DOCTOR_FAILED)
        else:
            self.show_error(self.ERROR_DOCTOR_MANAGER_NOT_INITIALIZED)

    def on_add_doctor_by_scan(self):
        """通过扫描二维码添加医生"""
        try:
            # 关闭医生管理对话框
            if hasattr(self, 'doctor_dialog') and self.doctor_dialog:
                self.dismiss_dialog(self.doctor_dialog)

            # 检查扫描器是否初始化
            if not hasattr(self, 'scanner') or not self.scanner:
                try:
                    from utils.qrcode_utils import QRCodeScanner
                    self.scanner = QRCodeScanner(callback=self.on_doctor_qr_scanned)
                except Exception as e:
                    print(f"初始化扫描器失败: {e}")
                    self.show_error("无法初始化扫描器")
                    return

            # 启动扫描
            self.show_info("请扫描医生的二维码")
            success = self.scanner.start_scanning()

            if not success:
                self.show_error("无法启动相机，请检查相机权限")
        except Exception as e:
            print(f"扫描二维码失败: {e}")
            self.show_error("扫描二维码功能暂时不可用")

    def on_doctor_qr_scanned(self, result):
        """处理医生二维码扫描结果"""
        import json
        
        try:
            if not result:
                self.show_error("未能识别二维码")
                return

            print(f"医生二维码扫描结果: {result}")

            # 尝试解析JSON格式
            try:
                data = json.loads(result)
                
                # 检查是否为医生二维码
                if isinstance(data, dict) and 'doctor_id' in data:
                    doctor_info = {
                        'name': data.get('name', '未知医生'),
                        'department': data.get('department', '未知科室'),
                        'hospital': data.get('hospital', '未知医院'),
                        'phone': data.get('phone', '')
                    }
                    
                    # 添加医生
                    self.add_scanned_doctor(doctor_info)
                    return
            except json.JSONDecodeError:
                pass

            # 如果不是标准格式，显示错误
            self.show_error("无效的医生二维码")
        except Exception as e:
            print(f"处理医生二维码失败: {e}")
            self.show_error("处理二维码失败")

    def add_scanned_doctor(self, doctor_info):
        """添加扫描到的医生"""
        try:
            # 初始化医生管理器（如果尚未初始化）
            if not hasattr(self, 'doctor_manager') or not self.doctor_manager:
                from utils.doctor_manager import DoctorManager
                self.doctor_manager = DoctorManager("user123")  # 在实际应用中应使用当前用户ID

            # 添加医生
            doctor = self.doctor_manager.add_doctor(
                name=doctor_info['name'],
                department=doctor_info['department'],
                hospital=doctor_info['hospital'],
                phone=doctor_info['phone']
            )

            if doctor:
                self.show_success(f"已添加医生: {doctor_info['name']}")
                # 重新打开医生管理对话框
                self.on_doctor_management()
            else:
                self.show_error(self.ERROR_ADD_DOCTOR_FAILED)
        except Exception as e:
            print(f"添加扫描医生失败: {e}")
            self.show_error(self.ERROR_ADD_DOCTOR_FAILED)

    def on_search_doctor(self):
        """搜索医生"""
        try:
            # 创建搜索对话框内容
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(10), dp(20), dp(10)],
                size_hint_y=None,
                height=dp(200)
            )

            # 搜索输入框
            search_field = MDTextField(
                hint_text="输入医生姓名或医院名称",
                size_hint_y=None,
                height=dp(48)
            )
            content.add_widget(search_field)

            # 搜索结果列表
            results_scroll = MDScrollView(
                size_hint=(1, None),
                height=dp(120)
            )
            
            results_list = MDList()
            results_scroll.add_widget(results_list)
            content.add_widget(results_scroll)

            # 创建对话框
            self.search_dialog = MDDialog(
                title="搜索医生",
                type="custom",
                content_cls=content,
                buttons=[
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: self.dismiss_dialog(self.search_dialog)
                    ),
                    MDButton(
                        MDButtonText(text="搜索"),
                        style="filled",
                        on_release=lambda x: self.perform_doctor_search(search_field.text, results_list)
                    )
                ]
            )

            # 手机绑定对话框
            content = MDBoxLayout(
                orientation="vertical",
                spacing=dp(10),
                padding=dp(20)
            )

            self.phone_field = MDTextField(
                hint_text="输入手机号",
                size_hint_y=None,
                height=dp(48)
            )
            content.add_widget(self.phone_field)

            self.code_field = MDTextField(
                hint_text="输入验证码",
                size_hint_y=None,
                height=dp(48)
            )
            content.add_widget(self.code_field)

            self.send_code_button = MDButton(
                text="发送验证码",
                on_release=self.send_verification_code
            )
            content.add_widget(self.send_code_button)

            # 显示对话框
            self.phone_bind_dialog = MDDialog(
                title="手机绑定",
                type="custom",
                content_cls=content,
                buttons=[
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: self.dismiss_dialog(self.phone_bind_dialog)
                    ),
                    MDButton(
                        MDButtonText(text="绑定"),
                        style="filled",
                        on_release=lambda x: self.confirm_bind_phone()
                    )
                ]
            )


            self.search_dialog.open()
        except Exception as e:
            print(f"打开搜索对话框失败: {e}")
            self.show_error("无法打开搜索功能")

    def perform_doctor_search(self, query, results_list):
        """执行医生搜索"""
        try:
            if not self._validate_search_query(query):
                return
                
            results_list.clear_widgets()
            
            mock_results = self._get_mock_doctor_data()
            filtered_results = self._filter_doctor_results(mock_results, query)
            
            if not filtered_results:
                self._show_no_results(results_list)
                return
                
            self._display_search_results(filtered_results, results_list)
            
        except Exception as e:
            print(f"搜索医生失败: {e}")
            self.show_error("搜索失败")
    
    def _validate_search_query(self, query):
        """验证搜索查询"""
        if not query.strip():
            self.show_error("请输入搜索关键词")
            return False
        return True
    
    def _get_mock_doctor_data(self):
        """获取模拟医生数据"""
        return [
            {
                'name': '张医生',
                'department': '内科',
                'hospital': '市人民医院',
                'phone': '138-0000-0001'
            },
            {
                'name': '李医生',
                'department': '外科',
                'hospital': '中心医院',
                'phone': '138-0000-0002'
            }
        ]
    
    def _filter_doctor_results(self, mock_results, query):
        """过滤医生搜索结果"""
        query_lower = query.lower()
        return [
            doctor for doctor in mock_results
            if query_lower in doctor['name'].lower() or 
               query_lower in doctor['hospital'].lower()
        ]
    
    def _show_no_results(self, results_list):
        """显示无搜索结果提示"""
        no_result_label = MDLabel(
            text="未找到相关医生",
            halign="center",
            theme_text_color="Secondary",
            size_hint_y=None,
            height=dp(32)
        )
        results_list.add_widget(no_result_label)
    
    def _display_search_results(self, filtered_results, results_list):
        """显示搜索结果列表"""
        for doctor in filtered_results:
            item = self._create_doctor_search_item(doctor)
            results_list.add_widget(item)
    
    def _create_doctor_search_item(self, doctor):
        """创建医生搜索结果项"""
        item = MDListItem(
            on_release=lambda x, doc=doctor: self.select_search_result(doc)
        )
        
        # 添加医生图标
        item.add_widget(MDListItemLeadingIcon(
            icon="doctor",
            theme_icon_color="Custom",
            icon_color=self.get_safe_primary_color()
        ))
        
        # 添加医生信息
        item.add_widget(MDListItemHeadlineText(
            text=f"{doctor['name']} ({doctor['department']})"
        ))
        
        # 添加医院信息
        item.add_widget(MDListItemSupportingText(
            text=f"{doctor['hospital']}"
        ))
        
        return item

    def select_search_result(self, doctor_info):
        """选择搜索结果"""
        try:
            # 关闭搜索对话框
            if hasattr(self, 'search_dialog') and self.search_dialog:
                self.dismiss_dialog(self.search_dialog)

            # 添加选中的医生
            self.add_scanned_doctor(doctor_info)
        except Exception as e:
            print(f"选择搜索结果失败: {e}")
            self.show_error(self.ERROR_ADD_DOCTOR_FAILED)

    def on_contact_doctor(self, phone):
        """联系医生"""
        try:
            import webbrowser
            webbrowser.open(f"tel:{phone}")
        except Exception as e:
            print(f"拨打电话失败: {e}")
            self.show_info(f"医生电话：{phone}")

    def on_change_password(self):
        """修改密码"""
        try:
            from kivymd.uix.scrollview import MDScrollView
            
            # 创建滚动视图
            scroll = MDScrollView(
                size_hint=(1, None),
                height=dp(300)
            )

            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                size_hint_y=None,
                height=dp(280)
            )

            # 添加密码要求说明标签
            password_req_label = MDLabel(
                text="密码要求：8-20位字符，包含字母和数字",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(32)
            )
            content.add_widget(password_req_label)

            # 原密码输入框
            self.old_password = MDTextField(
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                password=True
            )

            # 添加提示文本
            self.old_password.add_widget(MDTextFieldHintText(
                text="请输入原密码"
            ))

            # 添加辅助文本
            self.old_password.add_widget(MDTextFieldHelperText(
                text="请输入您的当前密码",
                mode="persistent"  # 设置为持久显示
            ))

            content.add_widget(self.old_password)

            # 新密码输入框
            self.new_password = MDTextField(
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                password=True
            )

            # 添加提示文本
            self.new_password.add_widget(MDTextFieldHintText(
                text="请输入新密码"
            ))

            # 添加辅助文本
            self.new_password.add_widget(MDTextFieldHelperText(
                text="8-20位字符，必须包含字母和数字",
                mode="persistent"  # 设置为持久显示
            ))

            content.add_widget(self.new_password)

            # 确认密码输入框
            self.confirm_password = MDTextField(
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                password=True
            )

            # 添加提示文本
            self.confirm_password.add_widget(MDTextFieldHintText(
                text="请再次输入新密码"
            ))

            # 添加辅助文本
            self.confirm_password.add_widget(MDTextFieldHelperText(
                text="请确保两次输入的密码一致",
                mode="persistent"  # 设置为持久显示
            ))

            content.add_widget(self.confirm_password)

            # 添加内容到滚动视图
            scroll.add_widget(content)

            # 显示对话框 - 符合KivyMD 2.0.1规范
            self.password_dialog = MDDialog(
                size_hint=(0.9, None),
                height=dp(400),
                radius=[dp(16)]
            )
            
            # 添加标题
            self.password_dialog.add_widget(
                MDDialogHeadlineText(
                    text="修改密码",
                    halign="center"
                )
            )
            
            # 添加内容容器
            content_container = MDDialogContentContainer()
            content_container.add_widget(scroll)
            self.password_dialog.add_widget(content_container)
            
            # 添加按钮容器
            button_container = MDDialogButtonContainer(
                spacing=dp(8),
                adaptive_width=True,
                pos_hint={"right": 1}
            )
            
            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(self.password_dialog)
            )
            
            confirm_button = MDButton(
                MDButtonText(text="确认"),
                style="text",
                on_release=lambda x: self.confirm_change_password()
            )
            
            button_container.add_widget(cancel_button)
            button_container.add_widget(confirm_button)
            self.password_dialog.add_widget(button_container)
            self.password_dialog.open()
        except Exception as e:
            print(f"显示密码修改对话框失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("无法显示密码修改对话框")

    def confirm_change_password(self):
        """确认修改密码"""
        try:
            import re
            
            old_pwd = self.old_password.text.strip()
            new_pwd = self.new_password.text.strip()
            confirm_pwd = self.confirm_password.text.strip()
            
            # 验证输入
            if not old_pwd:
                self.show_error("请输入原密码")
                return
                
            if not new_pwd:
                self.show_error("请输入新密码")
                return
                
            if not confirm_pwd:
                self.show_error("请确认新密码")
                return
                
            # 验证新密码格式
            if len(new_pwd) < 8 or len(new_pwd) > 20:
                self.show_error("密码长度必须在8-20位之间")
                return
                
            if not re.search(r'[a-zA-Z]', new_pwd) or not re.search(r'\d', new_pwd):
                self.show_error("密码必须包含字母和数字")
                return
                
            # 验证两次密码输入是否一致
            if new_pwd != confirm_pwd:
                self.show_error("两次输入的密码不一致")
                return
                
            # 验证原密码
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            
            if not current_user:
                self.show_error(self.ERROR_USER_INFO_NOT_FOUND)
                return
                
            if not user_manager.verify_password(current_user.username, old_pwd):
                self.show_error("原密码错误")
                return
                
            # 修改密码
            if user_manager.change_password(current_user.username, old_pwd, new_pwd):
                self.dismiss_dialog(self.password_dialog)
                self.show_success("密码修改成功")
            else:
                self.show_error("密码修改失败")
                
        except Exception as e:
            print(f"修改密码失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("修改密码失败")

    def on_privacy_settings(self):
        """隐私设置"""
        try:
            from kivymd.uix.scrollview import MDScrollView
            
            # 创建滚动视图
            scroll = MDScrollView(
                size_hint=(1, None),
                height=dp(400)
            )
            
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                size_hint_y=None,
                height=dp(450)
            )

            # 添加说明文本
            privacy_info = MDLabel(
                text="设置您的隐私选项和数据共享偏好",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(32)
            )
            content.add_widget(privacy_info)

            # 个人信息可见性设置
            content.add_widget(MDLabel(
                text="个人信息可见性",
                font_style="Body",
                role="medium",
                bold=True,
                size_hint_y=None,
                height=dp(32),
                padding=[0, dp(10), 0, dp(5)]
            ))

            # 可见性选项
            visibility_options = [
                {"title": "健康数据", "desc": "允许医生和健康顾问查看您的健康数据"},
                {"title": "个人资料", "desc": "允许其他用户查看您的基本资料"},
                {"title": "联系方式", "desc": "允许系统向您发送通知和提醒"}
            ]

            for i, option in enumerate(visibility_options):
                option_layout = MDBoxLayout(
                    orientation='vertical',
                    size_hint_y=None,
                    height=dp(70),
                    padding=[0, dp(5), 0, dp(5)]
                )
                
                header = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(40)
                )

                header.add_widget(MDLabel(
                    text=option["title"],
                    font_style="Body",
                    role="medium",
                    size_hint_y=None,
                    height=dp(32),
                    size_hint_x=0.7
                ))

                # 使用按钮替代开关
                toggle_button = MDButton(
                    MDButtonText(text="启用"),
                    style="text",
                    size_hint_x=0.3,
                    pos_hint={"center_y": 0.5}
                )
                header.add_widget(toggle_button)

                option_layout.add_widget(header)
                option_layout.add_widget(MDLabel(
                    text=option["desc"],
                    font_style="Body",
                    role="small",
                    theme_text_color="Secondary",
                    size_hint_y=None,
                    height=dp(24)
                ))

                content.add_widget(option_layout)

            # 数据共享设置
            content.add_widget(MDLabel(
                text="数据共享设置",
                font_style="Body",
                role="medium",
                bold=True,
                size_hint_y=None,
                height=dp(32),
                padding=[0, dp(15), 0, dp(5)]
            ))

            data_sharing = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40),
                spacing=dp(10)
            )

            # 使用按钮替代复选框
            check_button = MDButton(
                MDButtonText(text="□"),
                style="text",
                size_hint_x=0.2
            )
            data_sharing.add_widget(check_button)

            data_sharing.add_widget(MDLabel(
                text="允许匿名数据用于健康研究",
                font_style="Body",
                role="small",
                size_hint_y=None,
                height=dp(32),
                size_hint_x=0.8
            ))

            content.add_widget(data_sharing)
            
            # 添加内容到滚动视图
            scroll.add_widget(content)

            # 显示对话框 - 符合KivyMD 2.0.1规范
            privacy_dialog = MDDialog(
                size_hint=(0.9, None),
                height=dp(500),
                radius=[dp(16)]
            )
            
            # 添加标题
            privacy_dialog.add_widget(
                MDDialogHeadlineText(
                    text="隐私设置",
                    halign="center"
                )
            )
            
            # 添加内容容器
            content_container = MDDialogContentContainer()
            content_container.add_widget(scroll)
            privacy_dialog.add_widget(content_container)
            
            # 添加按钮容器
            button_container = MDDialogButtonContainer(
                spacing=dp(8),
                adaptive_width=True,
                pos_hint={"right": 1}
            )
            
            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(privacy_dialog)
            )
            
            save_button = MDButton(
                MDButtonText(text="保存"),
                style="text",
                on_release=lambda x: self.save_privacy_settings(privacy_dialog)
            )
            
            button_container.add_widget(cancel_button)
            button_container.add_widget(save_button)
            privacy_dialog.add_widget(button_container)
            privacy_dialog.open()
        except Exception as e:
            print(f"显示隐私设置失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("隐私设置功能暂时不可用")
            
    def save_privacy_settings(self, dialog):
        """保存隐私设置"""
        try:
            if dialog:
                self.dismiss_dialog(dialog)
            self.show_success("隐私设置已保存")
        except Exception as e:
            print(f"保存隐私设置失败: {e}")
            self.show_error("保存隐私设置失败")
        
    def on_bind_phone(self):
        """手机绑定"""
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                size_hint_y=None,
                height=dp(250)
            )

            # 添加说明文本
            bind_info = MDLabel(
                text="绑定手机号可以提高账号安全性，并接收重要通知。",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(32)
            )
            content.add_widget(bind_info)

            # 手机号输入框
            self.phone_bind_input = MDTextField(
                mode="outlined",
                size_hint_y=None,
                height=dp(48),
                input_filter="int"
            )

            # 添加提示文本
            self.phone_bind_input.add_widget(MDTextFieldHintText(
                text="请输入手机号"
            ))

            # 添加辅助文本
            self.phone_bind_input.add_widget(MDTextFieldHelperText(
                text="请输入11位手机号码",
                mode="persistent"
            ))

            content.add_widget(self.phone_bind_input)

            # 验证码输入区域
            code_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(48),
                spacing=dp(10)
            )

            # 验证码输入框
            self.verification_code_input = MDTextField(
                mode="outlined",
                size_hint_x=0.6,
                input_filter="int"
            )

            # 添加提示文本
            self.verification_code_input.add_widget(MDTextFieldHintText(
                text="验证码"
            ))

            # 添加辅助文本
            self.verification_code_input.add_widget(MDTextFieldHelperText(
                text="请输入6位验证码",
                mode="persistent"
            ))

            code_layout.add_widget(self.verification_code_input)

            # 获取验证码按钮
            self.get_code_button = MDButton(
                style="outlined",
                size_hint_x=0.4,
                on_release=lambda x: self.send_verification_code()
            )
            self.get_code_button.add_widget(MDButtonText(text="获取验证码"))

            code_layout.add_widget(self.get_code_button)

            content.add_widget(code_layout)

            # 显示对话框
            self.phone_bind_dialog = MDDialog(
                title="手机绑定",
                type="custom",
                content_cls=content,
                buttons=[
                    MDButton(
                        MDButtonText(text="取消"),
                        style="text",
                        on_release=lambda x: self.dismiss_dialog(self.phone_bind_dialog)
                    ),
                    MDButton(
                        MDButtonText(text="绑定"),
                        style="filled",
                        on_release=lambda x: self.confirm_bind_phone()
                    )
                ]
            )

            self.phone_bind_dialog.open()
        except Exception as e:
            print(f"手机绑定对话框创建失败: {e}")
            self.show_error("手机绑定功能暂时不可用")

    def send_verification_code(self):
        """发送手机验证码"""
        try:
            if not hasattr(self, 'phone_bind_input') or not self.phone_bind_input:
                self.show_error("手机号输入框未初始化")
                return

            phone = self.phone_bind_input.text.strip()

            # 验证手机号格式
            if not phone or len(phone) != 11:
                self.show_error("请输入有效的手机号")
                return

            # 这里只是模拟发送过程
            self.show_info("验证码已发送，请注意查收")

            # 禁用发送按钮60秒
            if hasattr(self, 'get_code_button') and self.get_code_button:
                self.get_code_button.disabled = True

                # 初始倒计时时间
                self.countdown_seconds = 60
                self.update_button_text()

                # 创建定时器
                from kivy.clock import Clock
                self.countdown_timer = Clock.schedule_interval(self.update_countdown, 1)
        except Exception as e:
            print(f"发送验证码失败: {e}")
            self.show_error("发送验证码失败")

    def update_button_text(self):
        """更新按钮文本"""
        try:
            if hasattr(self, 'get_code_button') and self.get_code_button:
                # 获取按钮中的文本组件
                for child in self.get_code_button.children:
                    if hasattr(child, 'text'):
                        child.text = f"重新发送({self.countdown_seconds}s)"
                        break
        except Exception as e:
            print(f"更新按钮文本失败: {e}")

    def update_countdown(self, dt):
        """更新倒计时"""
        try:
            self.countdown_seconds -= 1
            
            if self.countdown_seconds <= 0:
                self._handle_countdown_finished()
                return False  # 停止定时器
            else:
                self.update_button_text()
                return True  # 继续定时器
        except Exception as e:
            print(f"更新倒计时失败: {e}")
            return False
    
    def _handle_countdown_finished(self):
        """处理倒计时结束"""
        self._restore_get_code_button()
        self._stop_countdown_timer()
    
    def _restore_get_code_button(self):
        """恢复获取验证码按钮"""
        if hasattr(self, 'get_code_button') and self.get_code_button:
            self.get_code_button.disabled = False
            self._update_button_text_to_default()
    
    def _update_button_text_to_default(self):
        """更新按钮文本为默认值"""
        for child in self.get_code_button.children:
            if hasattr(child, 'text'):
                child.text = "获取验证码"
                break
    
    def _stop_countdown_timer(self):
        """停止倒计时定时器"""
        if hasattr(self, 'countdown_timer') and self.countdown_timer:
            from kivy.clock import Clock
            Clock.unschedule(self.countdown_timer)
            self.countdown_timer = None

    def confirm_bind_phone(self):
        """确认绑定手机号"""
        try:
            # 验证输入框初始化
            if not self._validate_bind_phone_inputs():
                return
            
            # 获取输入数据
            phone, code = self._get_bind_phone_data()
            
            # 验证输入数据
            if not self._validate_bind_phone_data(phone, code):
                return
            
            # 执行绑定操作
            self._execute_phone_binding(phone)
            
        except Exception as e:
            print(f"绑定手机号失败: {e}")
            self.show_error("绑定手机号失败")
    
    def _validate_bind_phone_inputs(self) -> bool:
        """验证绑定手机号输入框初始化"""
        if not hasattr(self, 'phone_bind_input') or not self.phone_bind_input:
            self.show_error("手机号输入框未初始化")
            return False
        
        if not hasattr(self, 'verification_code_input') or not self.verification_code_input:
            self.show_error("验证码输入框未初始化")
            return False
        
        return True
    
    def _get_bind_phone_data(self) -> tuple[str, str]:
        """获取绑定手机号数据"""
        phone = self.phone_bind_input.text.strip()
        code = self.verification_code_input.text.strip()
        return phone, code
    
    def _validate_bind_phone_data(self, phone: str, code: str) -> bool:
        """验证绑定手机号数据"""
        if not phone or len(phone) != 11:
            self.show_error("请输入有效的手机号")
            return False
        
        if not code:
            self.show_error("请输入验证码")
            return False
        
        return True
    
    def _execute_phone_binding(self, phone: str) -> None:
        """执行手机号绑定"""
        # 这里只是模拟验证过程
        self.show_success("手机号绑定成功")
        
        # 关闭对话框
        self._close_phone_bind_dialog()
        
        # 更新用户信息
        self._update_user_phone(phone)
    
    def _close_phone_bind_dialog(self) -> None:
        """关闭手机号绑定对话框"""
        if hasattr(self, 'phone_bind_dialog') and self.phone_bind_dialog:
            self.dismiss_dialog(self.phone_bind_dialog)
    
    def _update_user_phone(self, phone: str) -> None:
        """更新用户手机号信息"""
        from utils.user_manager import get_user_manager
        user_manager = get_user_manager()
        current_user = user_manager.get_current_user()
        
        if current_user:
            if isinstance(current_user, dict):
                current_user['phone'] = phone
            else:
                current_user.phone = phone
            user_manager.save_accounts()
        
    def on_notification_settings(self):
        """消息通知设置"""
        try:
            # 创建滚动视图
            scroll = MDScrollView(
                size_hint=(1, 1),
                do_scroll_x=False,
                do_scroll_y=True
            )
            
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                size_hint_y=None,
                adaptive_height=True
            )

            # 添加说明文本
            settings_info = MDLabel(
                text="设置通知提醒方式和频率",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(32)
            )
            content.add_widget(settings_info)

            # 通知开关
            switch_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(48),
                spacing=dp(10)
            )

            switch_layout.add_widget(MDLabel(
                text="接收通知",
                font_style="Body",
                role="medium",
                size_hint_y=None,
                height=dp(32),
                size_hint_x=0.7
            ))

            # 使用按钮替代开关
            self.notification_toggle = MDButton(
                style="text",
                size_hint_x=0.3,
                pos_hint={"center_y": 0.5},
                on_release=self.toggle_notification_status
            )
            self.notification_toggle.add_widget(MDButtonText(text="启用"))
            switch_layout.add_widget(self.notification_toggle)
            content.add_widget(switch_layout)

            # 通知类型选择
            content.add_widget(MDLabel(
                text="通知类型",
                font_style="Body",
                role="medium",
                size_hint_y=None,
                height=dp(32),
                padding=[0, dp(10), 0, dp(5)]
            ))

            # 通知类型复选框
            self.notification_types = {}
            types = ["健康提醒", "预约提醒", "报告通知", "系统消息"]
            for type_name in types:
                type_layout = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(40)
                )

                # 使用按钮替代复选框
                check_button = MDButton(
                    style="text",
                    size_hint_x=0.2,
                    on_release=lambda x, btn=type_name: self.toggle_notification_type(x, btn)
                )
                check_button.add_widget(MDButtonText(text="✓"))
                type_layout.add_widget(check_button)
                
                # 保存按钮引用
                self.notification_types[type_name] = check_button

                type_layout.add_widget(MDLabel(
                    text=type_name,
                    font_style="Body",
                    role="small",
                    size_hint_y=None,
                    height=dp(32),
                    size_hint_x=0.8
                ))

                content.add_widget(type_layout)
            
            scroll.add_widget(content)

            # 创建对话框
            self.notification_dialog = MDDialog(
                size_hint=(0.9, 0.8)
            )
            
            # 添加标题
            self.notification_dialog.add_widget(
                MDDialogHeadlineText(text="通知设置")
            )
            
            # 添加内容容器
            content_container = MDDialogContentContainer()
            content_container.add_widget(scroll)
            self.notification_dialog.add_widget(content_container)
            
            # 添加按钮容器
            button_container = MDDialogButtonContainer(
                spacing=dp(8),
                adaptive_width=True,
                pos_hint={"right": 1}
            )
            
            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(self.notification_dialog)
            )
            
            save_button = MDButton(
                MDButtonText(text="保存"),
                style="text",
                on_release=lambda x: self.save_notification_settings()
            )
            
            button_container.add_widget(cancel_button)
            button_container.add_widget(save_button)
            self.notification_dialog.add_widget(button_container)
            
            self.notification_dialog.open()
            
        except Exception as e:
            print(f"显示通知设置失败: {e}")
            self.show_error("通知设置功能暂时不可用")
    
    def toggle_notification_status(self, instance):
        """切换通知总开关状态"""
        try:
            # 查找文本组件并切换其文本
            for child in instance.children:
                if isinstance(child, MDButtonText):
                    if child.text == "启用":
                        child.text = "禁用"
                    else:
                        child.text = "启用"
                    break
        except Exception as e:
            print(f"切换通知状态失败: {e}")
    
    def toggle_notification_type(self, instance, type_name):
        """切换通知类型状态"""
        try:
            # 查找文本组件并切换其文本
            for child in instance.children:
                if isinstance(child, MDButtonText):
                    if child.text == "✓":
                        child.text = "□"
                    else:
                        child.text = "✓"
                    break
        except Exception as e:
            print(f"切换通知类型状态失败: {e}")
    
    def save_notification_settings(self):
        """保存通知设置"""
    
    def get_safe_primary_color(self):
        """获取安全的主色调"""
        try:
            app = MDApp.get_running_app()
            if app and hasattr(app, 'theme') and hasattr(app.theme, 'PRIMARY_COLOR'):
                return app.theme.PRIMARY_COLOR
            else:
                # 默认蓝色
                return [0.133, 0.46, 0.82, 1]
        except Exception as e:
            print(f"获取主色调失败: {e}")
            # 默认蓝色
            return [0.133, 0.46, 0.82, 1]

        try:
            # 获取通知总开关状态
            notification_enabled = False
            for child in self.notification_toggle.children:
                if isinstance(child, MDButtonText):
                    notification_enabled = child.text == "启用"
                    break
            
            # 获取各类型通知状态
            enabled_types = []
            for type_name, button in self.notification_types.items():
                for child in button.children:
                    if isinstance(child, MDButtonText) and child.text == "✓":
                        enabled_types.append(type_name)
                        break
            
            # 这里可以保存到数据库或配置文件
            print(f"通知设置已保存: 总开关={notification_enabled}, 启用类型={enabled_types}")
            
            self.dismiss_dialog(self.notification_dialog)
            self.show_success("通知设置已保存")
            
        except Exception as e:
            print(f"保存通知设置失败: {e}")
            self.show_error("保存设置失败")
        
    def on_switch_user(self):
        """显示用户管理对话框，允许切换、添加和删除用户"""
        try:
            user_manager, current_user = self._get_user_management_data()
            if not current_user:
                return
                
            dialog_content = self._create_user_management_content(user_manager, current_user)
            self._show_user_management_dialog(dialog_content)
            
        except Exception as e:
            print(f"显示用户管理对话框失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("用户管理功能暂时不可用")
    
    def _get_user_management_data(self):
        """获取用户管理所需的数据"""
        user_manager = get_user_manager()
        current_user = user_manager.get_current_user()
        
        if not current_user:
            self.show_error(self.ERROR_USER_INFO_NOT_FOUND)
            return None, None
            
        return user_manager, current_user
    
    def _create_user_management_content(self, user_manager, current_user):
        """创建用户管理对话框内容"""
        scroll = self._create_scroll_view()
        content = self._create_content_layout()
        
        # 添加标题布局
        title_layout = self._create_title_layout()
        content.add_widget(title_layout)
        
        # 创建用户列表
        users_list = self._create_users_list(user_manager.accounts, current_user)
        content.add_widget(users_list)
        
        scroll.add_widget(content)
        return scroll
    
    def _create_scroll_view(self):
        """创建滚动视图"""
        return MDScrollView(
            size_hint=(1, 1),
            do_scroll_x=False,
            do_scroll_y=True
        )
    
    def _create_content_layout(self):
        """创建内容布局"""
        return MDBoxLayout(
            orientation='vertical',
            spacing=dp(15),
            padding=[dp(20), dp(20), dp(20), dp(0)],
            size_hint_y=None,
            adaptive_height=True
        )
    
    def _create_title_layout(self):
        """创建标题布局"""
        title_layout = MDBoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(48)
        )
        
        title_label = MDLabel(
            text="用户管理",
            font_style="Body",
            role="medium",
            bold=True,
            size_hint_y=None,
            height=dp(32),
            size_hint_x=0.7
        )
        title_layout.add_widget(title_label)
        
        add_btn = MDIconButton(
            icon="account-plus",
            on_release=lambda x: self.show_add_user_dialog(),
            size_hint_x=0.3
        )
        title_layout.add_widget(add_btn)
        
        return title_layout
    
    def _create_users_list(self, accounts, current_user):
        """创建用户列表"""
        users_list = MDList()
        current_user_id = self._get_user_id(current_user)
        
        for account in accounts:
            account_id = self._get_user_id(account)
            if account_id:
                item = self._create_user_list_item(account, account_id, current_user_id)
                users_list.add_widget(item)
                
        return users_list
    
    def _get_user_id(self, user):
        """获取用户ID，优先使用custom_id"""
        if hasattr(user, 'custom_id') and user.custom_id:
            return user.custom_id
        elif hasattr(user, 'user_id') and user.user_id:
            return user.user_id
        return None
    
    def _create_user_list_item(self, account, account_id, current_user_id):
        """创建用户列表项"""
        item = MDListItem(
            on_release=lambda x, uid=account_id: self.confirm_switch_user(uid)
        )
        
        # 添加用户图标
        item.add_widget(MDListItemLeadingIcon(
            icon="account",
            theme_icon_color="Custom",
            icon_color=self.get_safe_primary_color()
        ))
        
        # 添加用户名信息
        display_name = account.real_name or account.username
        item.add_widget(MDListItemHeadlineText(text=display_name))
        
        # 添加用户ID信息和当前用户标记
        support_text = f"ID: {account_id}"
        if account_id == current_user_id:
            support_text += " (当前用户)"
        item.add_widget(MDListItemSupportingText(text=support_text))
        
        # 添加操作按钮
        item.add_widget(MDIconButton(
            icon="dots-vertical",
            theme_icon_color="Custom",
            on_release=lambda x, uid=account_id: self.show_user_actions(uid)
        ))
        
        return item
    
    def _show_user_management_dialog(self, content):
        """显示用户管理对话框"""
        self.user_dialog = MDDialog(size_hint=(0.9, 0.8))
        
        # 添加标题
        self.user_dialog.add_widget(
            MDDialogHeadlineText(text="用户管理")
        )
        
        # 添加内容容器
        content_container = MDDialogContentContainer()
        content_container.add_widget(content)
        self.user_dialog.add_widget(content_container)
        
        # 添加按钮容器
        button_container = MDDialogButtonContainer(
            spacing=dp(8),
            adaptive_width=True,
            pos_hint={"right": 1}
        )
        
        close_button = MDButton(
            MDButtonText(text="关闭"),
            style="text",
            on_release=lambda x: self.dismiss_dialog(self.user_dialog)
        )
        
        button_container.add_widget(close_button)
        self.user_dialog.add_widget(button_container)
        
        self.user_dialog.open()
    
    def confirm_switch_user(self, user_id):
        """确认切换用户"""
        try:
            self._close_user_dialog()
            user_manager = get_user_manager()
            
            if self._is_same_user(user_manager, user_id):
                return
            
            success = user_manager.switch_user(user_id)
            
            if success:
                self._handle_successful_switch(user_manager)
            else:
                self.show_error("用户切换失败")
        except Exception as e:
            self._handle_switch_error(e)
    
    def _close_user_dialog(self):
        """关闭用户对话框"""
        if hasattr(self, 'user_dialog') and self.user_dialog:
            self.dismiss_dialog(self.user_dialog)
    
    def _is_same_user(self, user_manager, user_id):
        """检查是否为同一用户"""
        current_user = user_manager.get_current_user()
        current_user_id = self._get_user_id(current_user)
        
        if current_user and current_user_id == user_id:
            self.show_info("您已经是该用户，无需切换")
            return True
        return False
    

    
    def _handle_successful_switch(self, user_manager):
        """处理成功切换用户"""
        self.show_success("用户已切换")
        self._reload_user_interface()
        self._update_global_user_info()
        self._navigate_to_user_screen(user_manager)
    
    def _reload_user_interface(self):
        """重新加载用户界面"""
        self.load_user_info()
        self.init_ui()
    
    def _update_global_user_info(self):
        """更新全局用户信息"""
        app = MDApp.get_running_app()
        if app is not None and hasattr(app, 'update_user_info'):
            app.update_user_info()
    
    def _navigate_to_user_screen(self, user_manager):
        """导航到用户对应的界面"""
        new_user = user_manager.get_current_user()
        if new_user and hasattr(new_user, 'identity'):
            app = MDApp.get_running_app()
            if app is not None and hasattr(app, 'root'):
                self.navigate_to_correct_screen(new_user.identity, app.root)
    
    def _handle_switch_error(self, error):
        """处理切换用户错误"""
        print(f"切换用户失败: {error}")
        import traceback
        traceback.print_exc()
        self.show_error("切换用户失败")
    
    def show_user_actions(self, user_id):
        """显示用户操作菜单"""
        try:
            # 创建菜单项
            menu_items = [
                {
                    "text": "切换到此用户",
                    "on_release": lambda x=user_id: self.confirm_switch_user(x)
                },
                {
                    "text": "删除用户",
                    "on_release": lambda x=user_id: self.confirm_delete_user(x)
                }
            ]

            # 创建并显示下拉菜单
            dropdown_menu = MDDropdownMenu(
                caller=None,  # 将在显示时设置
                items=menu_items,
                position="auto",
                width=dp(200)
            )

            dropdown_menu.open()
        except Exception as e:
            print(f"显示用户操作菜单失败: {e}")
            self.show_error("操作菜单显示失败")
    
    def show_add_user_dialog(self):
        """显示添加用户对话框"""
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                size_hint_y=None,
                adaptive_height=True
            )

            # 用户名输入框
            self.new_username_input = MDTextField(
                mode="outlined",
                size_hint_y=None,
                height=dp(48)
            )
            # 移除了MDTextFieldLeadingIcon，KivyMD 2.0.1中已不存在此组件
            self.new_username_input.add_widget(MDTextFieldHintText(text="用户名"))
            content.add_widget(self.new_username_input)

            # 密码输入框
            self.new_password_input = MDTextField(
                mode="outlined",
                password=True,
                size_hint_y=None,
                height=dp(48)
            )
            # 移除了MDTextFieldLeadingIcon，KivyMD 2.0.1中已不存在此组件
            self.new_password_input.add_widget(MDTextFieldHintText(text="密码"))
            content.add_widget(self.new_password_input)

            # 真实姓名输入框
            self.new_realname_input = MDTextField(
                mode="outlined",
                size_hint_y=None,
                height=dp(48)
            )
            # 移除了MDTextFieldLeadingIcon，KivyMD 2.0.1中已不存在此组件
            self.new_realname_input.add_widget(MDTextFieldHintText(text="真实姓名"))
            content.add_widget(self.new_realname_input)

            # 创建对话框
            self.add_user_dialog = MDDialog(
                size_hint=(0.9, None),
                height=dp(400)
            )
            
            # 添加标题
            self.add_user_dialog.add_widget(
                MDDialogHeadlineText(text="添加新用户")
            )
            
            # 添加内容容器
            content_container = MDDialogContentContainer()
            content_container.add_widget(content)
            self.add_user_dialog.add_widget(content_container)
            
            # 添加按钮容器
            button_container = MDDialogButtonContainer(
                spacing=dp(8),
                adaptive_width=True,
                pos_hint={"right": 1}
            )
            
            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(self.add_user_dialog)
            )
            
            add_button = MDButton(
                MDButtonText(text="添加"),
                style="text",
                on_release=lambda x: self.do_add_user()
            )
            
            button_container.add_widget(cancel_button)
            button_container.add_widget(add_button)
            self.add_user_dialog.add_widget(button_container)
            
            self.add_user_dialog.open()
            
        except Exception as e:
            print(f"显示添加用户对话框失败: {e}")
            self.show_error("添加用户功能暂时不可用")
    
    def do_add_user(self):
        """执行添加用户操作"""
        try:
            username = self.new_username_input.text.strip()
            password = self.new_password_input.text.strip()
            real_name = self.new_realname_input.text.strip()

            if not username or not password:
                self.show_error("用户名和密码不能为空")
                return

            # 获取用户管理器
            user_manager = get_user_manager()

            # 检查用户名是否已存在
            if user_manager.get_account_by_username(username):
                self.show_error("用户名已存在")
                return

            # 创建新用户
            success = user_manager.create_account(username, password, real_name or username)

            if success:
                self.show_success("用户添加成功")
                # 关闭添加用户对话框
                if hasattr(self, 'add_user_dialog') and self.add_user_dialog:
                    self.dismiss_dialog(self.add_user_dialog)
                # 重新打开用户管理对话框
                self.on_switch_user()
            else:
                self.show_error("用户添加失败")
        except Exception as e:
            print(f"添加用户失败: {e}")
            self.show_error(f"添加用户失败: {str(e)}")
    
    def confirm_delete_user(self, user_id):
        """确认删除用户"""
        try:
            # 获取用户管理器
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            
            # 获取当前用户ID
            current_user_id = None
            if current_user:
                if hasattr(current_user, 'custom_id') and current_user.custom_id:
                    current_user_id = current_user.custom_id
                elif hasattr(current_user, 'user_id') and current_user.user_id:
                    current_user_id = current_user.user_id
            
            # 不能删除当前用户
            if current_user_id == user_id:
                self.show_error("不能删除当前用户")
                return
            
            # 创建确认对话框
            content = MDLabel(
                text="确定要删除这个用户吗？此操作不可撤销。",
                font_style="Body",
                role="medium",
                size_hint_y=None,
                height=dp(64)
            )

            self.delete_confirm_dialog = MDDialog(
                size_hint=(0.8, None),
                height=dp(200)
            )
            
            # 添加标题
            self.delete_confirm_dialog.add_widget(
                MDDialogHeadlineText(text="确认删除")
            )
            
            # 添加内容容器
            content_container = MDDialogContentContainer()
            content_container.add_widget(content)
            self.delete_confirm_dialog.add_widget(content_container)
            
            # 添加按钮容器
            button_container = MDDialogButtonContainer(
                spacing=dp(8),
                adaptive_width=True,
                pos_hint={"right": 1}
            )
            
            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(self.delete_confirm_dialog)
            )
            
            delete_button = MDButton(
                MDButtonText(text="删除"),
                style="text",
                on_release=lambda x: self.do_delete_user(user_id)
            )
            
            button_container.add_widget(cancel_button)
            button_container.add_widget(delete_button)
            self.delete_confirm_dialog.add_widget(button_container)
            
            self.delete_confirm_dialog.open()
            
        except Exception as e:
            print(f"显示删除确认对话框失败: {e}")
            self.show_error("删除操作失败")
    
    def do_delete_user(self, user_id):
        """执行删除用户操作"""
        try:
            # 获取用户管理器
            user_manager = get_user_manager()
            
            # 删除用户
            success = user_manager.delete_account(user_id)
            
            if success:
                self.show_success("用户已删除")
                # 关闭确认对话框
                if hasattr(self, 'delete_confirm_dialog') and self.delete_confirm_dialog:
                    self.dismiss_dialog(self.delete_confirm_dialog)
                # 重新打开用户管理对话框
                self.on_switch_user()
            else:
                self.show_error("用户删除失败")
                
        except Exception as e:
            print(f"删除用户失败: {e}")
            self.show_error(f"删除用户失败: {str(e)}")
        
    def on_switch_identity(self):
        """显示身份切换菜单"""
        try:
            user_manager, current_user = self._get_switch_identity_data()
            if not current_user or not user_manager:
                return
                
            current_identity = self._get_current_identity(current_user)
            # 确保user_manager不为None再调用方法
            user_identities = user_manager.get_available_identities(current_user)
            
            if not self._validate_identity_options(user_identities):
                return
                
            menu_items = self._create_identity_menu_items(user_identities, current_identity)
            identity_button = self._find_identity_button()
            
            self._show_identity_dropdown_menu(identity_button, menu_items)
            
        except Exception as e:
            self.show_error(f"显示身份切换菜单失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def _get_switch_identity_data(self):
        """获取身份切换所需的数据"""
        user_manager = get_user_manager()
        if user_manager is None:
            self.show_error("用户管理器初始化失败")
            return None, None
            
        current_user = user_manager.get_current_user()
        
        if not current_user:
            self.show_error("无法获取当前用户信息")
            return None, None
            
        return user_manager, current_user
    
    def _validate_identity_options(self, user_identities):
        """验证身份选项是否有效"""
        if not user_identities or len(user_identities) <= 1:
            self.show_info("您只有一个身份，不能进行身份切换！")
            return False
        return True
    
    def _create_identity_menu_items(self, user_identities, current_identity):
        """创建身份菜单项"""
        menu_items = []
        
        for identity in user_identities:
            display_text = f"{identity} ✓" if identity == current_identity else identity
            menu_items.append({
                "text": display_text,
                "on_release": lambda x=identity: self.do_switch_identity(x)
            })
            
        return menu_items
    
    def _find_identity_button(self):
        """查找身份切换按钮"""
        identity_button = self._search_identity_button_in_settings()
        
        if not identity_button:
            from kivy.core.window import Window
            identity_button = Window.focus_widget
            
        return identity_button
    
    def _search_identity_button_in_settings(self):
        """在设置容器中搜索身份切换按钮"""
        settings_container = self.ids.get('settings_container')
        if not settings_container:
            return None
            
        for item in settings_container.children:
            if isinstance(item, ProfileSection):
                button = self._search_button_in_profile_section(item)
                if button:
                    return button
                    
        return None
    
    def _search_button_in_profile_section(self, profile_section):
        """在配置文件部分中搜索按钮"""
        for child in profile_section.children:
            if isinstance(child, MDList):
                for list_item in child.children:
                    if (isinstance(list_item, ProfileSettingItem) and 
                        list_item.title == "身份切换"):
                        return list_item
        return None
    
    def _show_identity_dropdown_menu(self, identity_button, menu_items):
        """显示身份下拉菜单"""
        dropdown_menu = MDDropdownMenu(
            caller=identity_button,
            items=menu_items,
            position="auto",
            width=dp(200)
        )
        dropdown_menu.open()

    def do_switch_identity(self, identity):
        """切换用户身份"""
        try:
            user_manager, current_user = self._get_switch_identity_data()
            if not current_user or not user_manager:
                return
                
            current_identity = self._get_current_identity(current_user)
            
            if not self._should_switch_identity(identity, current_identity):
                return
                
            self._perform_identity_switch(identity, current_user, user_manager)
            
        except Exception as e:
            self.show_error(f"切换身份失败: {str(e)}")
            import traceback
            traceback.print_exc()
    

    
    def _get_current_identity(self, current_user):
        """获取当前用户身份"""
        if hasattr(current_user, 'identity'):
            return current_user.identity
        elif isinstance(current_user, dict) and 'identity' in current_user:
            return current_user['identity']
        else:
            return '个人用户'
    
    def _should_switch_identity(self, new_identity, current_identity):
        """检查是否需要切换身份"""
        if new_identity == current_identity:
            self.show_info(f"您当前已经是 {new_identity} 身份")
            return False
        return True
    
    def _perform_identity_switch(self, identity, current_user, user_manager):
        """执行身份切换操作"""
        try:
            self._update_user_identity(current_user, identity)
            self._save_identity_changes(user_manager, current_user)
            self._update_app_user_data(identity, current_user)
            
            self.show_success(f"已成功切换为 {identity} 身份")
            self._navigate_after_identity_switch(identity)
            
        except Exception as e:
            self.show_error(f"更新用户身份失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def _update_user_identity(self, current_user, identity):
        """更新用户身份信息"""
        if isinstance(current_user, dict):
            current_user['identity'] = identity
        else:
            current_user.identity = identity
    
    def _save_identity_changes(self, user_manager, current_user):
        """保存身份变更"""
        user_manager.save_accounts()
        user_manager.set_current_user(current_user)
    
    def _update_app_user_data(self, identity, current_user):
        """更新应用用户数据"""
        app = MDApp.get_running_app()
        if app is None:
            return
            
        user_data = self._build_user_data(identity, current_user)
        
        if hasattr(app, 'set_user_data'):
            app.set_user_data(user_data)
        elif hasattr(app, 'user_data'):
            app.user_data.update({"identity": identity})
    
    def _build_user_data(self, identity, current_user):
        """构建用户数据字典"""
        user_data = {"identity": identity}
        
        if isinstance(current_user, dict):
            user_data.update({
                "username": current_user.get('username', ''),
                "real_name": current_user.get('real_name', ''),
                "custom_id": current_user.get('custom_id', '')
            })
        else:
            user_data.update({
                "username": getattr(current_user, 'username', ''),
                "real_name": getattr(current_user, 'real_name', ''),
                "custom_id": getattr(current_user, 'custom_id', '')
            })
            
        return user_data
    
    def _navigate_after_identity_switch(self, identity):
        """身份切换后的导航处理"""
        app = MDApp.get_running_app()
        
        if app is not None and hasattr(app, 'root'):
            self.navigate_to_correct_screen(identity, app.root)
        elif hasattr(self, 'manager'):
            self.navigate_to_correct_screen(identity, self.manager)
        else:
            self.show_error("无法导航到相应界面")

    def navigate_to_correct_screen(self, identity, screen_manager):
        """根据身份导航到正确的界面"""
        try:
            screen_config = self._get_identity_screen_config(identity)
            self._navigate_to_screen(screen_manager, screen_config)
        except Exception as e:
            self.show_error(f"界面导航失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def _get_identity_screen_config(self, identity):
        """获取身份对应的屏幕配置"""
        identity_screen_map = {
            '医生': {
                'screen_name': 'doctor_homepage',
                'unavailable_message': '医生界面暂未开放'
            },
            '护士': {
                'screen_name': 'nurse_homepage', 
                'unavailable_message': '护士界面暂未开放'
            },
            '管理员': {
                'screen_name': 'admin_homepage',
                'unavailable_message': '管理员界面暂未开放'
            }
        }
        
        # 默认配置（个人用户）
        default_config = {
            'screen_name': 'homepage',
            'unavailable_message': '个人用户界面暂未开放'
        }
        
        return identity_screen_map.get(identity, default_config)
    
    def _navigate_to_screen(self, screen_manager, screen_config):
        """导航到指定屏幕"""
        screen_name = screen_config['screen_name']
        unavailable_message = screen_config['unavailable_message']
        
        if screen_manager.has_screen(screen_name):
            screen_manager.current = screen_name
        else:
            self.show_info(unavailable_message)
        
    def on_user_agreement(self):
        """用户协议"""
        try:
            # 创建滚动视图
            scroll = MDScrollView()
            
            # 创建内容布局
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(20)],
                size_hint_y=None
            )
            content.bind(minimum_height=content.setter('height'))

            # 添加协议内容
            sections = [
                {
                    "title": "1. 服务条款",
                    "content": "欢迎使用健康管理应用。使用本应用即表示您同意遵守本协议的所有条款。本应用提供健康数据管理、医生咨询等服务。"
                },
                {
                    "title": "2. 隐私保护",
                    "content": "我们重视您的隐私保护。您的个人信息和健康数据将受到严格保密，仅用于提供必要的服务。未经您的同意，我们不会向第三方分享您的信息。"
                },
                {
                    "title": "3. 用户责任",
                    "content": "您需要对自己的账号安全负责，妥善保管登录信息。您在使用本应用时应遵守相关法律法规，不得从事任何违法或不当行为。"
                },
                {
                    "title": "4. 服务变更",
                    "content": "我们保留随时修改或终止服务的权利。如有重大变更，我们会提前通知用户。继续使用服务即表示您接受变更后的条款。"
                },
                {
                    "title": "5. 免责声明",
                    "content": "本应用提供的健康信息仅供参考，不能替代专业医疗建议。如有健康问题，请及时咨询专业医生。"
                },
                {
                    "title": "6. 联系我们",
                    "content": "如果您对本协议有任何疑问，请通过应用内的联系方式与我们取得联系。我们会及时为您解答。"
                }
            ]

            # 为每个章节创建内容
            for section in sections:
                # 章节标题
                title_label = MDLabel(
                    text=section["title"],
                    font_style="Body",
                    role="medium",
                    bold=True,
                    size_hint_y=None,
                    height=dp(40)
                )
                content.add_widget(title_label)

                # 章节内容
                content_label = MDLabel(
                    text=section["content"],
                    theme_text_color="Secondary",
                    font_style="Body",
                    role="small",
                    text_size=(None, None),
                    size_hint_y=None
                )
                content_label.bind(texture_size=content_label.setter('size'))
                content.add_widget(content_label)

                # 添加分隔线
                separator = MDDivider(
                    size_hint_y=None,
                    height=dp(1)
                )
                content.add_widget(separator)

            scroll.add_widget(content)

            # 创建对话框
            dialog = MDDialog(
                title="用户协议",
                type="custom",
                content_cls=scroll,
                buttons=[
                    MDButton(
                        text="关闭",
                        on_release=lambda x: dialog.dismiss()
                    )
                ],
                size_hint=(0.9, 0.8)
            )
            
            dialog.open()
        except Exception as e:
            print(f"显示用户协议失败: {e}")
            self.show_info("无法显示用户协议")
        
    def on_privacy_policy(self):
        """隐私政策"""
        try:
            # 创建滚动视图
            scroll = MDScrollView(
                size_hint=(1, None),
                height=dp(400),
                do_scroll_x=False,
                do_scroll_y=True
            )

            # 创建内容布局
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(20)],
                size_hint_y=None
            )
            content.bind(minimum_height=content.setter('height'))

            # 添加政策内容
            sections = [
                {
                    "title": "1. 信息收集",
                    "content": "我们收集的信息包括：\n- 基本信息：姓名、性别、年龄等\n- 健康数据：血压、血糖等\n- 设备信息：设备型号、操作系统等\n- 使用数据：使用记录、操作日志等"
                },
                {
                    "title": "2. 信息使用",
                    "content": "我们使用收集的信息用于：\n- 提供健康管理服务\n- 改善用户体验\n- 数据分析和研究\n- 安全保护和风险防范"
                },
                {
                    "title": "3. 信息共享",
                    "content": "我们不会向第三方出售您的个人信息。在以下情况下可能会共享信息：\n- 获得您的明确同意\n- 法律法规要求\n- 保护用户或公众安全\n- 与合作伙伴提供服务"
                },
                {
                    "title": "4. 信息安全",
                    "content": "我们采取多种安全措施保护您的信息：\n- 数据加密传输和存储\n- 访问权限控制\n- 定期安全审计\n- 员工安全培训"
                },
                {
                    "title": "5. 用户权利",
                    "content": "您享有以下权利：\n- 查看和更新个人信息\n- 删除个人数据\n- 限制信息处理\n- 数据可携带权"
                },
                {
                    "title": "6. 政策更新",
                    "content": "我们可能会不时更新本隐私政策。重大变更时会通过应用内通知或其他方式告知您。继续使用服务即表示您接受更新后的政策。"
                },
                {
                    "title": "7. 联系我们",
                    "content": "如果您对本隐私政策有任何疑问或建议，请通过以下方式联系我们：\n- 应用内反馈\n- 客服热线\n- 官方邮箱"
                }
            ]

            # 为每个章节创建内容
            for section in sections:
                # 章节标题
                title_label = MDLabel(
                    text=section["title"],
                    font_style="Body",
                    role="medium",
                    bold=True,
                    size_hint_y=None,
                    height=dp(40)
                )
                content.add_widget(title_label)

                # 章节内容
                content_label = MDLabel(
                    text=section["content"],
                    theme_text_color="Secondary",
                    font_style="Body",
                    role="small",
                    text_size=(None, None),
                    size_hint_y=None
                )
                content_label.bind(texture_size=content_label.setter('size'))
                content.add_widget(content_label)

                # 添加分隔线
                separator = MDDivider(
                    size_hint_y=None,
                    height=dp(1)
                )
                content.add_widget(separator)

            scroll.add_widget(content)

            # 创建对话框
            dialog = MDDialog(
                title="隐私政策",
                type="custom",
                content_cls=scroll,
                buttons=[
                    MDButton(
                        text="关闭",
                        on_release=lambda x: dialog.dismiss()
                    )
                ],
                size_hint=(0.9, 0.8)
            )
            
            dialog.open()
        except Exception as e:
            print(f"显示隐私政策失败: {e}")
            self.show_info("无法显示隐私政策")
        
    def on_help_center(self):
        """帮助中心"""
        try:
            # 创建滚动视图
            scroll = MDScrollView(
                size_hint=(1, None),
                height=dp(400)
            )
            
            # 创建内容容器
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(20)],
                size_hint_y=None
            )
            content.bind(minimum_height=content.setter('height'))

            # 添加帮助内容
            help_sections = [
                {
                    "title": "1. 常见问题",
                    "items": [
                        "• 如何修改密码？\n  进入个人设置 → 账户与安全 → 密码修改",
                        "• 如何绑定手机号？\n  进入个人设置 → 账户与安全 → 手机绑定",
                        "• 如何切换用户身份？\n  进入个人设置 → 账户与安全 → 身份切换",
                        "• 如何管理医生信息？\n  进入个人设置 → 个人信息 → 医生管理"
                    ]
                },
                {
                    "title": "2. 使用指南",
                    "items": [
                        "• 健康数据记录\n  在首页点击相应的健康指标进行记录",
                        "• 查看健康档案\n  进入个人设置 → 个人信息 → 健康档案",
                        "• 设置通知提醒\n  进入个人设置 → 账户与安全 → 消息通知",
                        "• 隐私设置管理\n  进入个人设置 → 账户与安全 → 隐私设置"
                    ]
                },
                {
                    "title": "3. 数据安全",
                    "items": [
                        "• 您的健康数据采用加密存储",
                        "• 个人信息严格保密，不会泄露给第三方",
                        "• 支持数据导出和删除功能",
                        "• 定期备份，确保数据安全"
                    ]
                },
                {
                    "title": "4. 联系支持",
                    "items": [
                        "• 客服热线：************",
                        "• 工作时间：周一至周五 9:00-18:00",
                        "• 在线客服：点击联系客服获取帮助",
                        "• 意见反馈：通过应用内反馈功能提交"
                    ]
                }
            ]

            # 为每个章节创建内容
            for section in help_sections:
                # 章节标题
                title_label = MDLabel(
                    text=section["title"],
                    font_style="Body",
                    role="medium",
                    bold=True,
                    size_hint_y=None,
                    height=dp(40)
                )
                content.add_widget(title_label)

                # 章节内容
                for item in section["items"]:
                    item_label = MDLabel(
                        text=item,
                        theme_text_color="Secondary",
                        font_style="Body",
                        role="small",
                        text_size=(None, None),
                        size_hint_y=None
                    )
                    item_label.bind(texture_size=item_label.setter('size'))
                    content.add_widget(item_label)

                # 添加分隔线
                separator = MDDivider(
                    size_hint_y=None,
                    height=dp(1)
                )
                content.add_widget(separator)

            scroll.add_widget(content)

            # 创建对话框
            dialog = MDDialog(
                title="帮助中心",
                type="custom",
                content_cls=scroll,
                buttons=[
                    MDButton(
                        text="联系客服",
                        on_release=lambda x: (dialog.dismiss(), self.on_contact_support())
                    ),
                    MDButton(
                        text="关闭",
                        on_release=lambda x: dialog.dismiss()
                    )
                ],
                size_hint=(0.9, 0.8)
            )
            
            dialog.open()
        except Exception as e:
            print(f"显示帮助中心失败: {e}")
            self.show_info("无法显示帮助中心")
        
    def on_contact_support(self):
        """联系客服"""
        try:
            # 创建内容容器
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(20),
                padding=[dp(20), dp(20), dp(20), dp(20)],
                size_hint_y=None
            )
            content.bind(minimum_height=content.setter('height'))

            # 添加客服信息标题
            title_label = MDLabel(
                text="客服支持",
                font_style="Body",
                role="large",
                bold=True,
                size_hint_y=None,
                height=dp(40),
                halign="center"
            )
            content.add_widget(title_label)

            # 工作时间信息
            work_time_label = MDLabel(
                text="工作时间：周一至周五 9:00-18:00",
                font_style="Body",
                role="medium",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(32),
                halign="center"
            )
            content.add_widget(work_time_label)

            # 分隔线
            separator1 = MDDivider(
                size_hint_y=None,
                height=dp(1)
            )
            content.add_widget(separator1)

            # 联系方式标题
            contact_title = MDLabel(
                text="联系方式",
                font_style="Body",
                role="medium",
                bold=True,
                size_hint_y=None,
                height=dp(32)
            )
            content.add_widget(contact_title)

            # 电话联系
            phone_layout = MDBoxLayout(
                orientation='horizontal',
                spacing=dp(10),
                size_hint_y=None,
                height=dp(40)
            )
            phone_icon = MDIcon(
                icon="phone",
                size_hint_x=None,
                width=dp(24)
            )
            phone_label = MDLabel(
                text="客服热线：************",
                font_style="Body",
                role="small"
            )
            phone_layout.add_widget(phone_icon)
            phone_layout.add_widget(phone_label)
            content.add_widget(phone_layout)

            # 邮箱联系
            email_layout = MDBoxLayout(
                orientation='horizontal',
                spacing=dp(10),
                size_hint_y=None,
                height=dp(40)
            )
            email_icon = MDIcon(
                icon="email",
                size_hint_x=None,
                width=dp(24)
            )
            email_label = MDLabel(
                text="邮箱：<EMAIL>",
                font_style="Body",
                role="small"
            )
            email_layout.add_widget(email_icon)
            email_layout.add_widget(email_label)
            content.add_widget(email_layout)

            # 分隔线
            separator2 = MDDivider(
                size_hint_y=None,
                height=dp(1)
            )
            content.add_widget(separator2)

            # 在线客服标题
            online_title = MDLabel(
                text="在线客服",
                font_style="Body",
                role="medium",
                bold=True,
                size_hint_y=None,
                height=dp(32)
            )
            content.add_widget(online_title)

            # 微信客服
            wechat_layout = MDBoxLayout(
                orientation='horizontal',
                spacing=dp(10),
                size_hint_y=None,
                height=dp(40)
            )
            wechat_icon = MDIcon(
                icon="wechat",
                size_hint_x=None,
                width=dp(24)
            )
            wechat_label = MDLabel(
                text="微信：health_support",
                font_style="Body",
                role="small"
            )
            wechat_layout.add_widget(wechat_icon)
            wechat_layout.add_widget(wechat_label)
            content.add_widget(wechat_layout)

            # QQ客服
            qq_layout = MDBoxLayout(
                orientation='horizontal',
                spacing=dp(10),
                size_hint_y=None,
                height=dp(40)
            )
            qq_icon = MDIcon(
                icon="qqchat",
                size_hint_x=None,
                width=dp(24)
            )
            qq_label = MDLabel(
                text="QQ：123456789",
                font_style="Body",
                role="small"
            )
            qq_layout.add_widget(qq_icon)
            qq_layout.add_widget(qq_label)
            content.add_widget(qq_layout)

            # 创建对话框
            dialog = MDDialog(
                title="联系客服",
                type="custom",
                content_cls=content,
                buttons=[
                    MDButton(
                        text="拨打电话",
                        on_release=lambda x: (dialog.dismiss(), self.call_support())
                    ),
                    MDButton(
                        text="关闭",
                        on_release=lambda x: dialog.dismiss()
                    )
                ],
                size_hint=(0.9, None),
                height=dp(500)
            )
            
            dialog.open()
        except Exception as e:
            print(f"显示客服信息失败: {e}")
            self.show_info("无法显示客服信息")
    
    def call_support(self):
        """拨打客服电话"""
        try:
            import webbrowser
            webbrowser.open("tel:************")
        except Exception as e:
            print(f"拨打电话失败: {e}")
            self.show_info("客服电话：************")

    def on_theme_settings(self):
        """主题设置"""
        try:
            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(15),
                padding=[dp(20), dp(20), dp(20), dp(0)],
                size_hint_y=None,
                height=dp(32)
            )

            # 添加主题说明
            theme_info = MDLabel(
                text="选择应用的主题样式",
                font_style="Body",
                role="small",
                theme_text_color="Secondary",
                size_hint_y=None,
                height=dp(32)
            )
            content.add_widget(theme_info)

            # 主题选择
            theme_layout = MDBoxLayout(
                orientation='vertical',
                spacing=dp(10),
                size_hint_y=None,
                height=dp(32)
            )

            # 浅色主题选项
            light_theme = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(48),
                spacing=dp(10)
            )

            light_theme.add_widget(MDLabel(
                text="浅色主题",
                font_style="Body",
                role="medium",
                size_hint_y=None,
                height=dp(32),
                size_hint_x=0.7
            ))

            light_button = MDButton(
                style="text",
                size_hint_x=0.3,
                pos_hint={"center_y": 0.5},
                on_release=lambda x: self.apply_theme("Light")
            )
            light_button.add_widget(MDButtonText(text="启用"))
            light_theme.add_widget(light_button)

            theme_layout.add_widget(light_theme)

            # 深色主题选项
            dark_theme = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(48),
                spacing=dp(10)
            )

            dark_theme.add_widget(MDLabel(
                text="深色主题",
                font_style="Body",
                role="medium",
                size_hint_y=None,
                height=dp(32),
                size_hint_x=0.7
            ))

            dark_button = MDButton(
                style="text",
                size_hint_x=0.3,
                pos_hint={"center_y": 0.5},
                on_release=lambda x: self.apply_theme("Dark")
            )
            dark_button.add_widget(MDButtonText(text="启用"))
            dark_theme.add_widget(dark_button)

            theme_layout.add_widget(dark_theme)

            content.add_widget(theme_layout)

            # 创建对话框
            theme_dialog = MDDialog()
            
            # 添加标题
            headline = MDDialogHeadlineText(text="主题设置")
            theme_dialog.add_widget(headline)
            
            # 添加内容容器
            content_container = MDDialogContentContainer()
            content_container.add_widget(content)
            theme_dialog.add_widget(content_container)
            
            # 添加按钮容器
            button_container = MDDialogButtonContainer(
                spacing=dp(8),
                adaptive_width=True,
                pos_hint={"right": 1}
            )
            
            # 添加关闭按钮
            close_button = MDButton(
                MDButtonText(text="关闭"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(theme_dialog)
            )
            button_container.add_widget(close_button)
            theme_dialog.add_widget(button_container)

            theme_dialog.open()
        except Exception as e:
            print(f"显示主题设置失败: {e}")
            self.show_error("无法显示主题设置")

    def apply_theme(self, theme_style):
        """应用主题"""
        try:
            app = MDApp.get_running_app()
            if app:
                app.theme_cls.theme_style = theme_style
                self.show_success(f"已切换到{theme_style}主题")
        except Exception as e:
            print(f"应用主题失败: {e}")
            self.show_error("应用主题失败")

    def on_about(self):
        """关于"""
        self.show_info("健康管理系统 v1.0")

    def on_logout(self):
        """退出登录"""
        try:
            # 创建确认对话框
            self.logout_dialog = MDDialog(
                size_hint=(0.8, None),
                height=dp(200),
                radius=[dp(16)]
            )
            
            # 添加标题
            self.logout_dialog.add_widget(
                MDDialogHeadlineText(
                    text="确认退出",
                    halign="center"
                )
            )
            
            # 添加内容
            content_container = MDDialogContentContainer()
            content_container.add_widget(
                MDLabel(
                    text="确定要退出登录吗？",
                    theme_text_color="Primary",
                    halign="center",
                    size_hint_y=None,
                    height=dp(40)
                )
            )
            self.logout_dialog.add_widget(content_container)
            
            # 添加按钮容器
            button_container = MDDialogButtonContainer(
                spacing=dp(8),
                adaptive_width=True,
                pos_hint={"right": 1}
            )
            
            cancel_button = MDButton(
                MDButtonText(text="取消"),
                style="text",
                on_release=lambda x: self.dismiss_dialog(self.logout_dialog)
            )
            
            confirm_button = MDButton(
                MDButtonText(text="确定"),
                style="text",
                on_release=lambda x: self.do_logout()
            )
            
            button_container.add_widget(cancel_button)
            button_container.add_widget(confirm_button)
            self.logout_dialog.add_widget(button_container)
            self.logout_dialog.open()
            
        except Exception as e:
            print(f"显示退出登录对话框失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("无法显示退出登录对话框")

    def do_logout(self):
        """执行退出登录"""
        try:
            # 关闭对话框
            if hasattr(self, 'logout_dialog'):
                self.dismiss_dialog(self.logout_dialog)
            
            # 清除用户数据
            user_manager = get_user_manager()
            user_manager.logout()
            
            # 跳转到登录页面
            app = MDApp.get_running_app()
            if app and app.root:
                app.root.current = 'login'
            
            self.show_success("已退出登录")
            
        except Exception as e:
            print(f"退出登录失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("退出登录失败")

    def dismiss_dialog(self, dialog):
        """关闭对话框"""
        if dialog:
            dialog.dismiss()

    # 消息提示方法
    def show_error(self, message: str):
        """显示错误消息"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
                md_bg_color=[0.8, 0.2, 0.2, 1]
            )
            snackbar.open()
        except Exception as e:
            print(f"显示错误消息失败: {e}")

    def show_success(self, message: str):
        """显示成功消息"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
                md_bg_color=[0.2, 0.8, 0.2, 1]
            )
            snackbar.open()
        except Exception as e:
            print(f"显示成功消息失败: {e}")

    def show_info(self, message: str):
        """显示信息消息"""
        try:
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8
            )
            snackbar.open()
        except Exception as e:
            print(f"显示信息消息失败: {e}")