"""
角色选择纯逻辑工具。
可独立于Kivy/KivyMD运行，便于单元测试。
"""
from typing import List, Tuple, Optional

RoleList = List[str]

ROLE_ORDER = ["超级管理员", "健康顾问", "单位管理员", "个人用户", "陪诊师"]


def _recompute_identity(roles: RoleList) -> str:
    """根据权限高低重新计算身份。"""
    for r in ROLE_ORDER:
        if r in roles:
            return r
    return ""


def compute_role_selection(
    selected_roles: RoleList,
    identity: str,
    registration_type: str,
    role_clicked: str,
) -> <PERSON><PERSON>[RoleList, str, bool, Optional[str]]:
    """
    计算点击角色后的新角色集合与身份。

    返回值: (new_roles, new_identity, health_advisor_changed, message)
    - message 非空表示需要向用户提示的信息（不一定是错误）。
    """
    roles_list: RoleList = list(selected_roles)
    was_health_advisor = "健康顾问" in roles_list
    msg: Optional[str] = None

    if registration_type == "替他人注册":
        # 仅允许个人用户，且不能取消
        if role_clicked != "个人用户":
            msg = "替他人注册只能选择'个人用户'角色"
        else:
            if "个人用户" not in roles_list:
                roles_list = ["个人用户"]
                identity = "个人用户"
            else:
                msg = "替他人注册必须选择'个人用户'角色"
        is_health_advisor = "健康顾问" in roles_list
        return roles_list, identity, (was_health_advisor != is_health_advisor), msg

    # 本人注册：支持多选+规则
    if role_clicked in roles_list:
        # 取消选择
        roles_list.remove(role_clicked)
        if role_clicked == identity:
            identity = _recompute_identity(roles_list)
    else:
        # 新增选择
        if role_clicked == "陪诊师":
            other_roles = [r for r in roles_list if r not in ["个人用户", "陪诊师"]]
            if other_roles:
                msg = "陪诊师只能与个人用户联合选择，不能与其他身份一起选择"
            else:
                if "个人用户" not in roles_list:
                    roles_list.append("个人用户")
                roles_list.append("陪诊师")
                if identity not in ["超级管理员", "健康顾问", "单位管理员"]:
                    identity = "陪诊师"
        elif role_clicked in ["单位管理员", "健康顾问", "超级管理员"]:
            if "陪诊师" in roles_list:
                msg = f"已选择陪诊师，不能再选择{role_clicked}"
            else:
                roles_list.append(role_clicked)
                # 按权限高低更新身份
                if identity == "" or (
                    role_clicked == "超级管理员" or
                    (role_clicked == "健康顾问" and identity not in ["超级管理员"]) or
                    (role_clicked == "单位管理员" and identity not in ["超级管理员", "健康顾问"]) 
                ):
                    identity = role_clicked
        else:
            # 个人用户
            roles_list.append(role_clicked)
            if not identity:
                identity = role_clicked

    is_health_advisor = "健康顾问" in roles_list
    return roles_list, identity, (was_health_advisor != is_health_advisor), msg

