# screens/register_screen_new.py
"""
用户注册界面 - 完全基于BaseScreen的实现
遵循UI页面改造规范，使用do_content_setup方法添加内容
"""

from mobile.screens.base_screen import BaseScreen
from kivy.properties import StringProperty, BooleanProperty, ObjectProperty, ListProperty
from kivy.metrics import dp
from kivy.clock import Clock
from kivy.factory import Factory
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.spinner import SpinnerOption, Spinner
from mobile.theme import AppTheme
from mobile.api.api_client import APIClient
from mobile.widgets.camera_view import CameraView
from mobile.utils.file_upload_download_manager import FileUploadDownloadManager
from mobile.utils.kivymd_date_picker import show_kivymd_date_picker
from mobile.utils.common_components import safe_bind_event
import os
import re
import datetime
from kivymd.uix.dialog import MDDialog
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.label import MDLabel
from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.card import MDCard
from kivymd.uix.textfield import MDTextField, MDTextFieldHintText
try:
    from kivymd.uix.button import MDIconButton
except ImportError:
    # 如果MDIconButton不可用，创建一个简单的替代类
    from kivy.uix.widget import Widget
    from kivy.uix.label import Label
    from kivy.properties import StringProperty, DictProperty, ListProperty, NumericProperty

    class MDIconButton(Widget):
        icon = StringProperty('')
        pos_hint = DictProperty({})
        size_hint = ListProperty([1, 1])
        size = ListProperty([100, 100])

        def __init__(self, **kwargs):
            # 提取图标参数
            self.icon = kwargs.pop('icon', '')
            self.pos_hint = kwargs.pop('pos_hint', {})
            self.size_hint = kwargs.pop('size_hint', [1, 1])
            self.size = kwargs.pop('size', [100, 100])
            super(MDIconButton, self).__init__(**kwargs)
            # 添加一个标签来显示图标
            self.label = Label(text=self.icon, size_hint=(None, None), size=self.size)
            self.add_widget(self.label)
import requests
from typing import Dict, Any, Optional, TYPE_CHECKING
import logging
logger = logging.getLogger(__name__)


# 为类型检查工具提供类型提示
if TYPE_CHECKING:
    from kivy.uix.widget import Widget
    from kivy.uix.spinner import SpinnerOption
    class RegisterScreenIds(Dict[str, Widget]):
        pass

    # 为AppTheme提供类型提示
    from mobile.theme import AppThemeType
    # AppTheme: AppThemeType  # 注释掉这行以避免类型检查错误

# 自定义SpinnerOption样式
class CustomSpinnerOption(SpinnerOption):
    def __init__(self, **kwargs):
        super(CustomSpinnerOption, self).__init__(**kwargs)
        # 使用Clock.schedule_once确保在对象完全初始化后再绑定事件
        Clock.schedule_once(self._bind_events, 0)

    def _bind_events(self, dt):
        # 使用type: ignore注释忽略类型检查错误
        try:
            self.bind(on_release=self._on_release)  # type: ignore
        except Exception as e:
            print(f"警告: CustomSpinnerOption无法绑定on_release事件: {e}")

    def _on_release(self, *args):
        spinner = self.parent.parent
        if spinner:
            spinner.text = self.text
            spinner.is_open = False
            # 使用hasattr检查是否有on_text_callback属性
            if hasattr(spinner, 'on_text_callback'):
                spinner.on_text_callback(spinner, self.text)

# 确保CustomSpinnerOption在kv字符串中可用
Factory.register('CustomSpinnerOption', cls=CustomSpinnerOption)

# 注册MDIcon别名
Factory.register('MDIcon', cls=MDIconButton)

class RegisterScreen(BaseScreen):
    # 属性定义
    selected_roles = ListProperty([])  # 使用ListProperty以支持UI自动更新
    gender = StringProperty("男")
    api_client = ObjectProperty(None)
    id_card_verified = BooleanProperty(False)  # 身份证是否已验证
    user_id = StringProperty("")  # 用户ID
    registration_type = StringProperty("本人注册")  # 注册类型: "本人注册" 或 "替他人注册"
    relationship = StringProperty("") # 与被注册人的关系
    identity = StringProperty("")  # 用户身份标识
    selected_role_color = [0.2, 0.6, 1, 0.8]  # 选中角色的背景色 - 更明显的蓝色背景
    _reg_trigger = BooleanProperty(False)  # 用于触发UI更新的内部属性

    # 证书上传相关属性
    medical_license_taken = BooleanProperty(False)  # 医师资格证书是否已上传
    medical_license_path = StringProperty("")  # 医师资格证书路径
    practice_license_taken = BooleanProperty(False)  # 医师执业证书是否已上传
    practice_license_path = StringProperty("")  # 医师执业证书路径
    certificate_document_ids = ListProperty([])  # 上传的证书文档ID列表

    # 出生日期相关属性
    birth_date = StringProperty("")  # 出生日期字符串

    def __init__(self, **kwargs):
        """初始化注册屏幕"""
        # 设置BaseScreen的导航栏属性
        kwargs.setdefault('screen_title', '用户注册')
        kwargs.setdefault('show_top_bar', True)  # 注册页面显示顶部导航栏
        kwargs.setdefault('skip_bottom_nav', False)  # 注册页面显示底部导航栏

        # 统一Logger
        self.logger = logger

        super(RegisterScreen, self).__init__(**kwargs)
        self.api_client = APIClient()
        self._reg_trigger = False
        # 初始化加载对话框
        self._loading_dialog = None
        self.current_certificate_type = None
        # 初始化用户管理器
        try:
            from utils.user_manager import get_user_manager
            self.user_manager = get_user_manager()
        except ImportError:
            self.user_manager = None

        # 初始化统一文件上传下载管理器
        self.file_manager = FileUploadDownloadManager()

        # 初始化主题
        from mobile.theme import AppTheme as AppThemeInstance
        self.theme = AppThemeInstance

    def do_content_setup(self):
        """在content_container中添加内容 - 遵循BaseScreen规范"""
        try:
            # 安全地获取content_container
            content_container = None
            if hasattr(self, 'ids') and isinstance(self.ids, dict):
                content_container = self.ids.get('content_container')

            if not content_container:
                self.logger.error("[RegisterScreen] 无法找到content_container")
                return

            # 清空content_container中的现有内容
            content_container.clear_widgets()

            # 创建主布局容器 - BaseScreen已有滚动，直接使用垂直布局
            main_container = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                padding=[int(dp(16)), int(dp(16)), int(dp(16)), int(dp(16))],
                spacing=int(dp(20))
            )
            main_container.bind(minimum_height=main_container.setter('height'))
            content_container.add_widget(main_container)

            # 添加注册界面组件（不包括底部按钮）
            self._add_registration_components_without_buttons(main_container)

            # 创建固定在底部的按钮区域
            bottom_container = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(80),
                spacing=dp(12),
                padding=[0, int(dp(12)), 0, int(dp(12))],
                pos_hint={"center_x": 0.5}
            )
            main_container.add_widget(bottom_container)

            # 添加底部按钮
            self._add_bottom_buttons(bottom_container)

            self.logger.info("[RegisterScreen] 成功添加内容到content_container")
        except Exception as e:
            self.logger.exception(f"[RegisterScreen] 添加内容到content_container失败: {e}")

    def _add_registration_components(self, parent_layout):
        """添加注册界面的所有组件"""
        # 用户注册标签
        title_label = MDLabel(
            text="用户注册",
            size_hint_y=None,
            height=dp(30),
            theme_text_color="Secondary",
            halign='left',
            font_style="Headline",  # 修复：使用存在的字体样式"Headline"替代"Subtitle1"
            role="small"
        )
        parent_layout.add_widget(title_label)

        # 注册类型选择区域
        self._add_registration_type_section(parent_layout)

        # 角色选择区域
        self._add_role_selection_section(parent_layout)

        # 与被注册人关系选择（仅当选择"替他人注册"时显示）
        self._add_relationship_section(parent_layout)

        # 资格证书上传区域 - 仅当选择健康顾问角色时显示
        self._add_certificate_section(parent_layout)

        # 基础信息部分
        self._add_basic_info_section(parent_layout)

        # 底部按钮
        self._add_bottom_buttons(parent_layout)

    def _add_registration_components_without_buttons(self, parent_layout):
        """添加注册界面的所有组件（不包括底部按钮）"""
        # 用户注册标签
        title_label = MDLabel(
            text="用户注册",
            size_hint_y=None,
            height=dp(30),
            theme_text_color="Secondary",
            halign='left',
            font_style="Headline",
            role="small"
        )
        parent_layout.add_widget(title_label)

        # 注册类型选择区域
        self._add_registration_type_section(parent_layout)

        # 角色选择区域
        self._add_role_selection_section(parent_layout)

        # 与被注册人关系选择（仅当选择"替他人注册"时显示）
        self._add_relationship_section(parent_layout)

        # 资格证书上传区域 - 仅当选择健康顾问角色时显示
        self._add_certificate_section(parent_layout)

        # 基础信息部分
        self._add_basic_info_section(parent_layout)

    def _add_registration_type_section(self, parent_layout):
        """添加注册类型选择区域"""
        # 注册类型选择卡片
        reg_type_card = MDCard(
            orientation='vertical',
            size_hint_y=None,
            height=dp(70),
            padding=[int(dp(16)), int(dp(12)), int(dp(16)), int(dp(12))],
            spacing=int(dp(8)),
            radius=[int(dp(16))],
            elevation=2,
            md_bg_color=getattr(AppTheme, 'CARD_BACKGROUND', [0.95, 0.95, 0.95, 1])
        )
        parent_layout.add_widget(reg_type_card)

        # 注册类型选择按钮布局 - 居中显示
        reg_btn_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(35),
            spacing=dp(8),
            pos_hint={"center_x": 0.5}  # 居中显示
        )
        reg_type_card.add_widget(reg_btn_layout)

        # 本人注册按钮
        def on_self_register_click(instance):
            """本人注册按钮点击处理"""
            self.set_registration_type("本人注册")

        self.self_register_btn = MDButton(
            MDButtonText(text="本人注册"),
            style="filled",
            theme_bg_color="Custom",
            md_bg_color=getattr(AppTheme, "PRIMARY_COLOR", [0.12, 0.58, 0.95, 1]) if self.registration_type == "本人注册" else getattr(AppTheme, "PRIMARY_LIGHT", [0.89, 0.95, 0.99, 1]),
            size_hint_y=None,
            height=dp(35)
        )
        self.self_register_btn.bind(on_release=on_self_register_click)
        reg_btn_layout.add_widget(self.self_register_btn)

        # 替他人注册按钮
        def on_other_register_click(instance):
            """替他人注册按钮点击处理"""
            self.set_registration_type("替他人注册")

        self.other_register_btn = MDButton(
            MDButtonText(text="替他人注册"),
            style="filled",
            theme_bg_color="Custom",
            md_bg_color=getattr(AppTheme, "PRIMARY_COLOR", [0.12, 0.58, 0.95, 1]) if self.registration_type == "替他人注册" else getattr(AppTheme, "PRIMARY_MEDIUM", [0.39, 0.71, 0.96, 1]),
            size_hint_y=None,
            height=dp(35)
        )
        self.other_register_btn.bind(on_release=on_other_register_click)
        reg_btn_layout.add_widget(self.other_register_btn)

    def _add_role_selection_section(self, parent_layout):
        """添加角色选择区域"""
        # 角色选择标签
        role_label = MDLabel(
            text="选择注册身份：",
            size_hint_y=None,
            height=dp(25),
            theme_text_color="Primary",
            halign='left',
            font_style="Label",
            role="medium",
            bold=True
        )
        parent_layout.add_widget(role_label)

        # 角色选择卡片
        role_card = MDCard(
            orientation='vertical',
            size_hint_y=None,
            padding=dp(10),
            spacing=dp(5),
            elevation=2,
            radius=[dp(16)],
        )
        role_card.bind(minimum_height=role_card.setter('height'))  # 添加高度绑定
        parent_layout.add_widget(role_card)

        # 角色网格布局 - 改为3列以容纳6个身份
        role_grid = GridLayout(
            cols=3,  # 3列布局，容纳6个身份
            spacing=dp(8),
            size_hint_y=None,
            height=dp(120),  # 调整高度适应2行布局
            col_default_width=dp(90),
            row_default_height=dp(55)
        )
        role_card.add_widget(role_grid)

        # 第一行：个人用户、单位管理员、健康顾问
        self.personal_user_card = self._create_role_card("个人用户", "account")
        role_grid.add_widget(self.personal_user_card)

        self.unit_admin_card = self._create_role_card("单位管理员", "medical-bag")
        role_grid.add_widget(self.unit_admin_card)

        self.health_advisor_card = self._create_role_card("健康顾问", "heart-pulse")
        role_grid.add_widget(self.health_advisor_card)

        # 第二行：超级管理员、陪诊师、关系按钮
        self.super_admin_card = self._create_role_card("超级管理员", "shield-account")
        role_grid.add_widget(self.super_admin_card)

        # 新增陪诊师角色卡片
        self.companion_card = self._create_role_card("陪诊师", "account-heart")
        role_grid.add_widget(self.companion_card)

        # 关系按钮（当选择替他人注册时显示）
        self.relationship_button_card = self._create_relationship_button_card()
        role_grid.add_widget(self.relationship_button_card)

        # 创建ids字典以兼容原有代码
        if not hasattr(self, 'ids'):
            self.ids = {}
        self.ids['personal_user_card'] = self.personal_user_card
        self.ids['unit_admin_card'] = self.unit_admin_card
        self.ids['health_advisor_card'] = self.health_advisor_card
        self.ids['super_admin_card'] = self.super_admin_card
        self.ids['companion_card'] = self.companion_card
        self.ids['relationship_button_card'] = self.relationship_button_card

    def _create_role_card(self, role_name, icon_name):
        """创建角色卡片"""
        card = MDCard(
            orientation="vertical",
            size_hint=(1, 1),
            elevation=1,
            radius=[dp(8)],
            md_bg_color=getattr(AppTheme, "CARD_BACKGROUND", [1, 1, 1, 1]),
            padding=dp(8),
            ripple_behavior=True,  # 启用涟漪效果提供视觉反馈
            theme_bg_color="Custom"
        )

        # 角色内容布局
        content_layout = MDBoxLayout(
            orientation="vertical",
            size_hint_y=None,
            height=dp(50),
            spacing=dp(2),
            pos_hint={"center_x": 0.5, "center_y": 0.5}
        )
        card.add_widget(content_layout)

        # 角色图标
        icon = MDIconButton(
            icon=icon_name,
            pos_hint={"center_x": 0.5, "center_y": 0.5},
            size_hint=(None, None),
            size=(dp(24), dp(24)),
            disabled=True  # 禁用图标按钮的点击事件，避免事件冲突
        )
        content_layout.add_widget(icon)

        # 角色标签
        label = MDLabel(
            text=role_name,
            size_hint_y=None,
            height=dp(20),
            theme_text_color="Primary",
            font_style="Label",
            role="medium",
            halign="center"
        )
        content_layout.add_widget(label)

        # 更可靠的点击事件绑定（兼容KivyMD 2.0.1 dev0）
        def _on_card_press(instance):
            """按下时立即给予视觉反馈，保证手感灵敏"""
            try:
                if role_name in self.selected_roles:
                    instance.md_bg_color = [0.9, 0.9, 0.9, 1.0]
                else:
                    instance.md_bg_color = self.selected_role_color
            except Exception as e:
                self.logger.error(f"设置视觉反馈时出错: {e}")

        def _on_card_release(instance):
            """释放时执行业务逻辑，避免滚动手势误触"""
            self.select_role(role_name)
            # 确保抬起后立即根据选择状态刷新为蓝色/取消
            self._update_role_cards_visual_state()

        # 使用safe_bind_event以兼容不同版本事件名
        safe_bind_event(card, 'on_press', _on_card_press)
        safe_bind_event(card, 'on_release', _on_card_release)

        return card

    def _create_relationship_button_card(self):
        """创建关系按钮卡片（当选择替他人注册时显示）"""
        card = MDCard(
            orientation="vertical",
            size_hint=(1, 1),
            elevation=1,
            radius=[dp(8)],
            md_bg_color=getattr(AppTheme, "CARD_BACKGROUND", [1, 1, 1, 1]),
            padding=dp(8),
            ripple_behavior=True,  # 启用涟漪效果提供视觉反馈
            theme_bg_color="Custom",
            opacity=0,  # 初始隐藏
            disabled=True,  # 初始禁用
            height=dp(55)  # 设置固定高度
        )

        # 关系按钮内容布局
        content_layout = MDBoxLayout(
            orientation="vertical",
            size_hint_y=None,
            height=dp(50),
            spacing=dp(2),
            pos_hint={"center_x": 0.5, "center_y": 0.5}
        )
        card.add_widget(content_layout)

        # 关系图标
        icon = MDIconButton(
            icon="account-group",
            pos_hint={"center_x": 0.5, "center_y": 0.5},
            size_hint=(None, None),
            size=(dp(24), dp(24))
        )
        content_layout.add_widget(icon)

        # 关系标签
        self.relationship_label = MDLabel(
            text="选择关系",
            size_hint_y=None,
            height=dp(20),
            theme_text_color="Primary",
            font_style="Label",
            role="medium",
            halign="center"
        )
        content_layout.add_widget(self.relationship_label)

        # 点击显示关系选择下拉
        def _on_relationship_release(instance):
            self._show_relationship_dialog(instance)

        safe_bind_event(card, 'on_release', _on_relationship_release)

        return card

    def _add_relationship_section(self, parent_layout):
        """添加与被注册人关系选择区域"""
        self.relationship_container = MDBoxLayout(
            orientation="vertical",
            size_hint_y=None,
            height=dp(160) if self.registration_type == "替他人注册" else 0,
            opacity=1 if self.registration_type == "替他人注册" else 0,
            disabled=False if self.registration_type == "替他人注册" else True,
            spacing=dp(5)
        )
        self.relationship_container.bind(minimum_height=self.relationship_container.setter('height'))  # 添加高度绑定
        parent_layout.add_widget(self.relationship_container)

        # 关系选择卡片
        relationship_card = MDCard(
            orientation='vertical',
            size_hint_y=None,
            height=dp(160),
            padding=dp(10),
            elevation=2,
            radius=[dp(16)],
            opacity=1 if self.registration_type == "替他人注册" else 0
        )
        relationship_card.bind(minimum_height=relationship_card.setter('height'))  # 添加高度绑定
        self.relationship_container.add_widget(relationship_card)

        # 居中容器
        center_layout = MDBoxLayout(
            orientation='vertical',
            padding=[dp(10), dp(40), dp(10), dp(40)]
        )
        relationship_card.add_widget(center_layout)

        # 关系选择下拉框
        self.relationship_spinner = Factory.Spinner(
            text="请选择关系" if not self.relationship else self.relationship,
            values=("父亲", "母亲", "岳父", "岳母", "公公", "婆婆", "丈夫", "妻子", "儿子", "女儿", "兄弟", "姐妹", "其他"),
            option_cls='CustomSpinnerOption',
            size_hint_y=None,
            height=dp(40),
            background_normal='',
            background_color=getattr(AppTheme, "PRIMARY_MEDIUM", [0.39, 0.71, 0.96, 1]),
        )
        self.relationship_spinner.bind(text=self._on_relationship_selected)
        center_layout.add_widget(self.relationship_spinner)

        # 添加到ids字典
        if not hasattr(self, 'ids'):
            self.ids = {}
        self.ids['relationship_container'] = self.relationship_container
        self.ids['relationship_spinner'] = self.relationship_spinner

    def _on_relationship_selected(self, instance, value):
        """处理关系选择事件"""
        self.relationship = value if value != "请选择关系" else ""

    def _show_relationship_dialog(self, instance):
        """显示关系选择对话框"""
        import logging
        logger = logging.getLogger(__name__)

        self.logger.info("关系选择按钮被点击")
        logger.info("用户点击关系选择按钮")

        # 确保关系容器可见
        if hasattr(self, 'relationship_container'):
            self.relationship_container.height = dp(160)
            self.relationship_container.opacity = 1
            self.relationship_container.disabled = False

            # 确保Spinner可以交互
            if hasattr(self, 'relationship_spinner'):
                self.relationship_spinner.disabled = False
                # 直接触发Spinner的下拉菜单显示
                try:
                    # 使用正确的方法触发Spinner下拉菜单
                    self.relationship_spinner._toggle_dropdown()
                except AttributeError:
                    # 如果_toggle_dropdown方法不存在，尝试其他方法
                    try:
                        # 模拟点击事件
                        self.relationship_spinner.is_open = True
                        self.relationship_spinner._on_dropdown_select()
                    except Exception as e:
                        self.logger.error(f"触发Spinner下拉菜单失败: {e}")
                        logger.error(f"触发Spinner下拉菜单失败: {e}")
                        # 确保Spinner至少是可点击的
                        self.relationship_spinner.disabled = False
                except Exception as e:
                    self.logger.error(f"触发Spinner下拉菜单失败: {e}")
                    logger.error(f"触发Spinner下拉菜单失败: {e}")

    def _trigger_spinner_dropdown(self):
        """触发Spinner下拉菜单显示 - 已弃用，使用直接触发方式"""
        try:
            if hasattr(self, 'relationship_spinner') and self.relationship_spinner:
                # 尝试多种方法触发下拉菜单
                if hasattr(self.relationship_spinner, '_toggle_dropdown'):
                    self.relationship_spinner._toggle_dropdown()
                elif hasattr(self.relationship_spinner, 'is_open'):
                    self.relationship_spinner.is_open = True
                else:
                    # 确保Spinner是可点击的
                    self.relationship_spinner.disabled = False
        except Exception as e:
            self.logger.error(f"触发Spinner下拉菜单失败: {e}")
            # 如果自动触发失败，至少确保Spinner是可点击的
            if hasattr(self, 'relationship_spinner'):
                self.relationship_spinner.disabled = False

    def _add_certificate_section(self, parent_layout):
        """添加资格证书上传区域"""
        self.certificate_container = MDBoxLayout(
            orientation="vertical",
            size_hint_y=None,
            height=0,  # 初始化时完全隐藏
            opacity=0,  # 初始化时完全透明
            disabled=True,  # 初始化时禁用
            spacing=dp(16),
            padding=[dp(16), 0, dp(16), 0]
        )
        parent_layout.add_widget(self.certificate_container)

        # 证书标签
        cert_label = MDLabel(
            text="资格证书上传（申请健康顾问必填）",
            size_hint_y=None,
            height=dp(30),
            theme_text_color="Primary",
            halign='left',
            font_style="Title",
            role="small",
            bold=True
        )
        self.certificate_container.add_widget(cert_label)

        # 创建水平布局容器放置两个证书卡片
        certificates_horizontal_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(220),
            spacing=dp(16)
        )
        self.certificate_container.add_widget(certificates_horizontal_layout)

        # 医师资格证书卡片
        medical_license_card = MDCard(
            orientation="vertical",
            size_hint_x=0.5,
            size_hint_y=None,
            height=dp(220),
            padding=dp(12),
            elevation=2,
            radius=[dp(12)],
            md_bg_color=getattr(AppTheme, "SURFACE_COLOR", [1, 1, 1, 1])
        )
        certificates_horizontal_layout.add_widget(medical_license_card)

        # 医师资格证书布局
        medical_layout = MDBoxLayout(
            orientation="vertical",
            spacing=dp(8)
        )
        medical_license_card.add_widget(medical_layout)

        # 医师资格证书标签
        medical_label = MDLabel(
            text="医师资格证书",
            size_hint_y=None,
            height=dp(25),
            theme_text_color="Primary",
            halign='center',
            font_style="Label",
            role="medium",
            bold=True
        )
        medical_layout.add_widget(medical_label)

        # 医师资格证书预览区域
        medical_preview_card = MDCard(
            size_hint_y=None,
            height=dp(120),
            md_bg_color=[0.95, 0.95, 0.95, 1],
            radius=[dp(8)],
            elevation=1
        )
        medical_layout.add_widget(medical_preview_card)

        # 医师资格证书预览
        self.medical_license_photo = Factory.Image(
            source="" if not self.medical_license_taken else self.medical_license_path,
            size_hint=(1, 1),
            fit_mode="contain"
        )
        medical_preview_card.add_widget(self.medical_license_photo)

        # 上传医师资格证书按钮
        medical_btn = MDButton(
            MDButtonText(text="上传证书"),
            style="filled",
            theme_bg_color="Custom",
            md_bg_color=getattr(AppTheme, "PRIMARY_COLOR", [0.12, 0.58, 0.95, 1]),
            size_hint_y=None,
            height=dp(36),
            on_release=lambda x: self.upload_certificate("medical_license")
        )
        medical_layout.add_widget(medical_btn)

        # 医师执业证书卡片
        practice_license_card = MDCard(
            orientation="vertical",
            size_hint_x=0.5,
            size_hint_y=None,
            height=dp(220),
            padding=dp(12),
            elevation=2,
            radius=[dp(12)],
            md_bg_color=getattr(AppTheme, "SURFACE_COLOR", [1, 1, 1, 1])
        )
        certificates_horizontal_layout.add_widget(practice_license_card)

        # 医师执业证书布局
        practice_layout = MDBoxLayout(
            orientation="vertical",
            spacing=dp(8)
        )
        practice_license_card.add_widget(practice_layout)

        # 医师执业证书标签
        practice_label = MDLabel(
            text="医师执业证书",
            size_hint_y=None,
            height=dp(25),
            theme_text_color="Primary",
            halign='center',
            font_style="Label",
            role="medium",
            bold=True
        )
        practice_layout.add_widget(practice_label)

        # 医师执业证书预览区域
        practice_preview_card = MDCard(
            size_hint_y=None,
            height=dp(120),
            md_bg_color=[0.95, 0.95, 0.95, 1],
            radius=[dp(8)],
            elevation=1
        )
        practice_layout.add_widget(practice_preview_card)

        # 医师执业证书预览
        self.practice_license_photo = Factory.Image(
            source="" if not self.practice_license_taken else self.practice_license_path,
            size_hint=(1, 1),
            fit_mode="contain"
        )
        practice_preview_card.add_widget(self.practice_license_photo)

        # 上传医师执业证书按钮
        practice_btn = MDButton(
            MDButtonText(text="上传证书"),
            style="filled",
            theme_bg_color="Custom",
            md_bg_color=getattr(AppTheme, "PRIMARY_COLOR", [0.12, 0.58, 0.95, 1]),
            size_hint_y=None,
            height=dp(36),
            on_release=lambda x: self.upload_certificate("practice_license")
        )
        practice_layout.add_widget(practice_btn)

        # 添加到ids字典
        if not hasattr(self, 'ids'):
            self.ids = {}
        self.ids['certificate_container'] = self.certificate_container
        self.ids['medical_license_photo'] = self.medical_license_photo
        self.ids['practice_license_photo'] = self.practice_license_photo

    def _add_basic_info_section(self, parent_layout):
        # 基础信息卡片
        basic_info_card = MDCard(
            orientation="vertical",
            size_hint_y=None,
            padding=dp(16),
            spacing=dp(20),  # 增大垂直间距
            md_bg_color=getattr(AppTheme, "SURFACE_COLOR", [1, 1, 1, 1]),
            elevation=2,
            radius=[dp(12)]
        )
        basic_info_card.bind(minimum_height=basic_info_card.setter('height'))
        parent_layout.add_widget(basic_info_card)

        # 基础信息标签
        basic_label = MDLabel(
            text="基础信息",
            size_hint_y=None,
            height=dp(30),
            theme_text_color="Primary",
            halign='left',
            font_style="Title",
            role="small",
            bold=True
        )
        basic_info_card.add_widget(basic_label)

        # 第一行：用户名、姓名
        row1_grid = GridLayout(
            cols=2,
            spacing=dp(16),
            size_hint_y=None,
            height=dp(48)
        )
        basic_info_card.add_widget(row1_grid)

        # 用户名
        self.username_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(40),
            multiline=False,
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.username_input.add_widget(MDTextFieldHintText(
            text="用户名 *",
            font_size=dp(14)
        ))
        row1_grid.add_widget(self.username_input)

        # 姓名
        self.name_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(40),
            multiline=False,
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.name_input.add_widget(MDTextFieldHintText(
            text="姓名 *",
            font_size=dp(14)
        ))
        row1_grid.add_widget(self.name_input)

        # 第二行：身份证号码（单独一行）- 使用MDTextFieldHintText，移除前置标签
        id_card_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(16),
            size_hint_y=None,
            height=dp(48)  # 增加容器高度以适应MDTextFieldHintText
        )

        # 根据KivyMD 2.0.1 dev0规范创建身份证输入框
        self.id_card_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(40),  # 调整输入框高度
            multiline=False,
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary",
            font_size=dp(14)  # 调整字体大小与标签保持一致
        )

        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.id_card_input.add_widget(MDTextFieldHintText(
            text="身份证号码 *",  # 将标签文本移到提示文本中
            font_size=dp(14)
        ))
        self.id_card_input.bind(text=self._on_id_card_input)
        id_card_container.add_widget(self.id_card_input)
        basic_info_card.add_widget(id_card_container)

        # 第三行：性别、出生日期 - 调整布局比例，增加垂直间距
        row3_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(16),
            size_hint_y=None,
            height=dp(56)  # 增加高度以适应新的布局
        )
        basic_info_card.add_widget(row3_container)

        # 性别 - 使用下拉按钮，不显示标签，减小占用空间
        self.gender_spinner = Factory.Spinner(
            text=self.gender,
            values=["男", "女"],
            size_hint_x=0.15,  # 进一步减小性别选择器的占用空间
            size_hint_y=None,
            height=dp(48),
            option_cls=CustomSpinnerOption,
            background_color=getattr(AppTheme, "SURFACE_COLOR", [1, 1, 1, 1]),
            color=getattr(AppTheme, "ON_SURFACE_COLOR", [0, 0, 0, 1])
        )
        self.gender_spinner.bind(text=self._on_gender_selected)  # type: ignore
        row3_container.add_widget(self.gender_spinner)

        # 出生日期 - 大幅扩大显示空间
        birth_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(8),
            size_hint_x=0.85,  # 增加出生日期的占用空间
            size_hint_y=None,
            height=dp(48)
        )
        birth_label = MDLabel(
            text="出生日期 *",
            size_hint_x=None,
            width=dp(60),  # 稍微增加标签宽度
            size_hint_y=None,
            height=dp(48),
            theme_text_color="Primary",
            halign='left',
            valign='center',
            font_style="Label",
            role="medium",
            bold=True,
            font_size=dp(16)  # 为标签添加字体大小
        )
        birth_container.add_widget(birth_label)

        # 出生日期输入框和日期选择器按钮的容器
        birth_input_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(4),
            size_hint_y=None,
            height=dp(40)
        )

        self.birth_date_input = MDTextField(
            hint_text="YYYY/MM/DD",
            size_hint_y=None,
            height=dp(48),  # 增加输入框高度
            mode="outlined",
            size_hint_x=0.80,  # 调整输入框占比
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary",
            font_size=dp(16)
        )
        birth_input_container.add_widget(self.birth_date_input)

        # 日期选择器按钮
        date_picker_btn = MDIconButton(
            icon="calendar",
            size_hint=(None, None),
            size=(dp(28), dp(28)),
            pos_hint={"center_y": 0.5},
            on_release=lambda x: self.show_birth_date_picker()
        )
        birth_input_container.add_widget(date_picker_btn)

        birth_container.add_widget(birth_input_container)
        row3_container.add_widget(birth_container)

        # 增加第一行与第二行之间的垂直间距
        spacing_widget = MDBoxLayout(
            size_hint_y=None,
            height=dp(10)  # 额外的垂直间距
        )
        basic_info_card.add_widget(spacing_widget)

        # 第四行：民族、教育程度 - 移除冗余标签，减小按钮高度
        row4_grid = GridLayout(
            cols=2,
            spacing=dp(5),
            size_hint_y=None,
            height=dp(48)  # 增加高度以保持一致性
        )
        basic_info_card.add_widget(row4_grid)

        # 民族 - 移除标签，按钮提示文本作为标识
        self.ethnicity_spinner = Factory.Spinner(
            text="民族 *",  # 直接在按钮上显示标签
            values=["汉族", "蒙古族", "回族", "藏族", "维吾尔族", "苗族", "彝族", "壮族", "布依族", "朝鲜族", "满族", "侗族", "瑶族", "白族", "土家族", "哈尼族", "哈萨克族", "傣族", "黎族", "傈僳族", "佤族", "畲族", "高山族", "拉祜族", "水族", "东乡族", "纳西族", "景颇族", "柯尔克孜族", "土族", "达斡尔族", "仫佬族", "羌族", "布朗族", "撒拉族", "毛南族", "仡佬族", "锡伯族", "阿昌族", "普米族", "塔吉克族", "怒族", "乌孜别克族", "俄罗斯族", "鄂温克族", "德昂族", "保安族", "裕固族", "京族", "塔塔尔族", "独龙族", "鄂伦春族", "赫哲族", "门巴族", "珞巴族", "基诺族", "其他"],
            option_cls='CustomSpinnerOption',
            size_hint_y=None,
            height=dp(40),  # 增加按钮高度以保持一致性
            background_normal='',
            background_color=getattr(AppTheme, "PRIMARY_MEDIUM", [0.39, 0.71, 0.96, 1])
        )
        row4_grid.add_widget(self.ethnicity_spinner)

        # 教育程度 - 移除标签，按钮提示文本作为标识
        self.education_spinner = Factory.Spinner(
            text="教育程度 *",  # 直接在按钮上显示标签
            values=["小学", "初中", "高中", "中专", "大专", "本科", "硕士", "博士", "其他"],
            option_cls='CustomSpinnerOption',
            size_hint_y=None,
            height=dp(40),  # 增加按钮高度以保持一致性
            background_normal='',
            background_color=getattr(AppTheme, "PRIMARY_MEDIUM", [0.39, 0.71, 0.96, 1])
        )
        row4_grid.add_widget(self.education_spinner)

        password_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(8),
            size_hint_y=None,
            height=dp(48)  # 增加容器高度
        )
        basic_info_card.add_widget(password_container)

        self.password_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(40),  # 调整输入框高度
            multiline=False,
            password=True,
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary",
            font_size=dp(14)
        )

        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.password_input.add_widget(MDTextFieldHintText(
            text="密码 * (需包含字母和数字)",
            font_size=dp(14)
        ))
        password_container.add_widget(self.password_input)

        # 确认密码
        confirm_password_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(8),
            size_hint_y=None,
            height=dp(48)  # 增加容器高度
        )
        basic_info_card.add_widget(confirm_password_container)

        self.confirm_password_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(40),  # 调整输入框高度
            multiline=False,
            password=True,
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary",
            font_size=dp(14)
        )

        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.confirm_password_input.add_widget(MDTextFieldHintText(
            text="确认密码 *",
            font_size=dp(14)
        ))
        confirm_password_container.add_widget(self.confirm_password_input)

        # 手机号码 - 移除前置标签
        phone_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(8),
            size_hint_y=None,
            height=dp(48)  # 调整容器高度与输入框匹配
        )
        basic_info_card.add_widget(phone_container)

        # 根据KivyMD 2.0.1 dev0规范创建手机号输入框
        self.phone_input = MDTextField(
            mode="outlined",
            size_hint_y=None,
            height=dp(40),  # 调整输入框高度
            multiline=False,
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary",
            font_size=dp(14)  # 调整字体大小与标签保持一致
        )

        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.phone_input.add_widget(MDTextFieldHintText(
            text="手机号码 *",  # 将标签文本移到提示文本中
            font_size=dp(14)
        ))
        phone_container.add_widget(self.phone_input)

        # 电子邮箱 - 移除前置标签
        email_container = MDBoxLayout(
            orientation="horizontal",
            spacing=dp(8),
            size_hint_y=None,
            height=dp(48)  # 调整容器高度与输入框匹配
        )
        basic_info_card.add_widget(email_container)

        # 根据KivyMD 2.0.1 dev0规范创建邮箱输入框
        self.email_input = MDTextField(
            mode="outlined",
            multiline=False,
            size_hint_y=None,
            height=dp(40),  # 调整输入框高度
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary",
            font_size=dp(14)  # 调整字体大小与标签保持一致
        )

        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.email_input.add_widget(MDTextFieldHintText(
            text="电子邮箱 *",  # 将标签文本移到提示文本中
            font_size=dp(14)
        ))
        email_container.add_widget(self.email_input)

        # 工作单位
        self.workplace_input = MDTextField(
            mode="outlined",
            multiline=False,
            size_hint_y=None,
            height=dp(40),
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.workplace_input.add_widget(MDTextFieldHintText(
            text="工作单位",
            font_size=dp(14)
        ))
        basic_info_card.add_widget(self.workplace_input)

        # 职业
        self.occupation_input = MDTextField(
            mode="outlined",
            multiline=False,
            size_hint_y=None,
            height=dp(40),
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.occupation_input.add_widget(MDTextFieldHintText(
            text="职业",
            font_size=dp(14)
        ))
        basic_info_card.add_widget(self.occupation_input)

        # 联系地址
        self.address_input = MDTextField(
            mode="outlined",
            multiline=False,
            size_hint_y=None,
            height=dp(40),
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.address_input.add_widget(MDTextFieldHintText(
            text="联系地址",
            font_size=dp(14)
        ))
        basic_info_card.add_widget(self.address_input)

        # 紧急联系人
        self.emergency_contact_input = MDTextField(
            mode="outlined",
            multiline=False,
            size_hint_y=None,
            height=dp(40),
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.emergency_contact_input.add_widget(MDTextFieldHintText(
            text="紧急联系人 *",
            font_size=dp(14)
        ))
        basic_info_card.add_widget(self.emergency_contact_input)

        # 紧急联系人电话
        self.emergency_phone_input = MDTextField(
            mode="outlined",
            multiline=False,
            size_hint_y=None,
            height=dp(40),
            md_bg_color=getattr(AppTheme, "SURFACE_VARIANT_COLOR", [0.95, 0.95, 0.95, 1]),
            theme_text_color="Primary"
        )
        # 添加提示文本 - KivyMD 2.0.1 dev0规范
        self.emergency_phone_input.add_widget(MDTextFieldHintText(
            text="紧急联系人电话 *",
            font_size=dp(14)
        ))
        basic_info_card.add_widget(self.emergency_phone_input)

        # 添加到ids字典
        if not hasattr(self, 'ids'):
            self.ids = {}
        self.ids['username_input'] = self.username_input
        self.ids['password_input'] = self.password_input
        self.ids['confirm_password_input'] = self.confirm_password_input
        self.ids['id_card_input'] = self.id_card_input
        self.ids['phone_input'] = self.phone_input
        self.ids['name_input'] = self.name_input
        self.ids['birth_date_input'] = self.birth_date_input
        self.ids['ethnicity_spinner'] = self.ethnicity_spinner
        self.ids['education_spinner'] = self.education_spinner
        self.ids['workplace_input'] = self.workplace_input
        self.ids['occupation_input'] = self.occupation_input
        self.ids['address_input'] = self.address_input
        self.ids['email_input'] = self.email_input
        self.ids['emergency_contact_input'] = self.emergency_contact_input
        self.ids['emergency_phone_input'] = self.emergency_phone_input

    def _add_bottom_buttons(self, parent_layout):
        """添加底部按钮"""
        # 底部按钮布局
        bottom_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(60),
            spacing=dp(20),
            padding=[dp(20), dp(12), dp(20), dp(20)],
            pos_hint={"center_x": 0.5}
        )
        parent_layout.add_widget(bottom_layout)

        # 返回登录按钮
        back_btn = MDButton(
            MDButtonText(text="返回登录"),
            style="filled",
            theme_bg_color="Custom",
            md_bg_color=getattr(AppTheme, "PRIMARY_DARK", [0.09, 0.46, 0.75, 1]),
            size_hint_x=0.4,
            height=dp(40),
            on_release=lambda x: self.back_to_login()
        )
        bottom_layout.add_widget(back_btn)

        # 提交注册按钮
        register_btn = MDButton(
            MDButtonText(text="提交注册"),
            style="filled",
            theme_bg_color="Custom",
            md_bg_color=getattr(AppTheme, "PRIMARY_COLOR", [0.12, 0.58, 0.95, 1]),
            size_hint_x=0.4,
            height=dp(40),
            on_release=lambda x: self.register()
        )
        bottom_layout.add_widget(register_btn)

    def set_registration_type(self, reg_type):
        """设置注册类型"""
        self.logger.info(f"设置注册类型: {reg_type}, 当前类型: {self.registration_type}")

        # 如果当前类型与新类型相同，不做任何操作
        if self.registration_type == reg_type:
            return

        # 直接应用注册类型，不再显示确认对话框
        self.registration_type = reg_type

        # 更新UI显示
        self._update_registration_ui()
        # 同时更新关系容器可见性
        self._update_relationship_container_visibility()

    def _update_relationship_container_visibility(self):
        """更新关系选择容器和关系按钮卡片的显示状态"""
        # 更新关系选择容器
        if hasattr(self, 'relationship_container'):
            if self.registration_type == "替他人注册":
                self.relationship_container.height = int(dp(160))
                self.relationship_container.opacity = 1
                self.relationship_container.disabled = False
            else:
                self.relationship_container.height = 0
                self.relationship_container.opacity = 0
                self.relationship_container.disabled = True

        # 更新关系按钮卡片的显示状态
        if hasattr(self, 'relationship_button_card'):
            if self.registration_type == "替他人注册":
                self.relationship_button_card.height = int(dp(55))  # 只显示按钮本身的高度
                self.relationship_button_card.opacity = 1
                self.relationship_button_card.disabled = False
            else:
                self.relationship_button_card.height = 0
                self.relationship_button_card.opacity = 0
                self.relationship_button_card.disabled = True

    def _show_loading_dialog(self, message="加载中..."):
        """显示加载对话框"""
        try:
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.label import MDLabel

            # 创建对话框内容布局
            dialog_content = MDBoxLayout(
                orientation="horizontal",
                padding=dp(20),
                spacing=dp(10),
                size_hint_y=None,
                height=dp(60)
            )

            # 消息文本
            message_label = MDLabel(
                text=message,
                theme_text_color="Primary",
                halign="center",
                valign="middle"
            )
            dialog_content.add_widget(message_label)

            # 创建对话框 - 修复API调用
            self._loading_dialog = MDDialog()
            self._loading_dialog.add_widget(dialog_content)
            self._loading_dialog.open()
        except Exception as e:
            self.logger.error(f"显示加载对话框失败: {str(e)}")

    def _update_registration_ui(self):
        """更新注册类型相关的UI组件"""
        # 更新按钮颜色
        if hasattr(self, 'self_register_btn') and hasattr(self, 'other_register_btn'):
            if self.registration_type == "替他人注册":
                self.self_register_btn.md_bg_color = getattr(AppTheme, "PRIMARY_LIGHT", [0.89, 0.95, 0.99, 1])
                self.other_register_btn.md_bg_color = getattr(AppTheme, "PRIMARY_COLOR", [0.12, 0.58, 0.95, 1])
            else:
                self.self_register_btn.md_bg_color = getattr(AppTheme, "PRIMARY_COLOR", [0.12, 0.58, 0.95, 1])
                self.other_register_btn.md_bg_color = getattr(AppTheme, "PRIMARY_MEDIUM", [0.39, 0.71, 0.96, 1])

        # 更新关系选择容器
        if hasattr(self, 'relationship_container'):
            if self.registration_type == "替他人注册":
                self.relationship_container.height = int(dp(160))
                self.relationship_container.opacity = 1
                self.relationship_container.disabled = False

                # 确保Spinner可以交互
                if hasattr(self, 'relationship_spinner'):
                    self.relationship_spinner.disabled = False

                # 如果是替他人注册，限制只能选择个人用户角色
                if self.selected_roles and "个人用户" not in self.selected_roles:
                    self.selected_roles = ["个人用户"]
                elif not self.selected_roles:
                    self.selected_roles = ["个人用户"]
            else:
                # 在本人注册模式下，隐藏关系选择框
                self.relationship_container.height = 0
                self.relationship_container.opacity = 0
                self.relationship_container.disabled = True

        # 更新关系按钮卡片的显示状态
        if hasattr(self, 'relationship_button_card'):
            if self.registration_type == "替他人注册":
                self.relationship_button_card.height = int(dp(55))  # 只显示按钮本身的高度
                self.relationship_button_card.opacity = 1
                self.relationship_button_card.disabled = False
            else:
                self.relationship_button_card.height = 0
                self.relationship_button_card.opacity = 0
                self.relationship_button_card.disabled = True

        # 强制刷新布局
        try:
            # 更新角色卡片的视觉状态
            self._update_role_cards_visual_state()
        except Exception as e:
            print(f"更新角色卡片视觉状态时出错: {e}")

        # 延迟进一步刷新布局，确保UI正确更新
        Clock.schedule_once(self._force_layout_update, 0.1)

    def _force_layout_update(self, dt):
        """强制更新布局"""
        pass  # 在BaseScreen中不需要特殊处理

    def select_role(self, role):
        """选择或取消选择角色"""
        # 统一日志与纯逻辑复用
        try:
            from mobile.utils.role_selection import compute_role_selection
        except Exception:
            compute_role_selection = None

        try:
            self.logger.info(f"[RegisterScreen] 角色按钮被点击: {role}")
            if compute_role_selection:
                new_roles, new_identity, health_changed, msg = compute_role_selection(
                    list(self.selected_roles), self.identity, self.registration_type, role
                )
                if msg:
                    self._show_message(msg)
                self.selected_roles = new_roles
                self.identity = new_identity
                # 确保抬起后立即根据选择状态刷新为蓝色/取消
                self._update_role_cards_visual_state()
                if health_changed:
                    self._update_certificate_container("健康顾问" in new_roles)
            else:
                self.logger.warning("[RegisterScreen] 角色选择工具未加载，忽略本次点击")
        except Exception as e:
            self.logger.error(f"[RegisterScreen] 选择角色时出错: {e}")

    def _force_ui_refresh(self):
        """强制刷新UI以确保视觉反馈立即生效"""
        try:
            # 强制更新角色卡片的视觉状态
            self._update_role_cards_visual_state()

            # 如果有父布局，强制重新布局
            if self.parent:
                self.parent.do_layout()
        except Exception as e:
            self.logger.error(f"强制刷新UI时出错: {e}")

    def _trigger_ui_update(self):
        """触发UI更新以提供即时的视觉反馈"""
        # 触发属性更新，强制UI重新渲染
        self._reg_trigger = not self._reg_trigger

        # 立即更新角色卡片的视觉状态，不使用延迟
        try:
            self._update_role_cards_visual_state()
        except Exception as e:
            print(f"更新角色卡片视觉状态时出错: {e}")

    def _update_role_cards_visual_state(self):
        """手动更新角色卡片的视觉状态 - 优化版本，移除延迟调用"""
        from kivymd.app import MDApp

        # 定义角色卡片的映射
        role_cards = {
            "个人用户": self.personal_user_card,
            "单位管理员": self.unit_admin_card,
            "健康顾问": self.health_advisor_card,
            "超级管理员": self.super_admin_card,
            "陪诊师": self.companion_card
        }

        # 更新每个角色卡片的视觉状态
        for role, card in role_cards.items():
            try:
                # 检查卡片是否存在
                if card is None:
                    self.logger.warning(f"角色卡片 {role} 不存在")
                    continue

                is_selected = role in self.selected_roles

                # 检查是否在替他人注册模式下，非个人用户角色应该被禁用
                is_disabled = (self.registration_type == "替他人注册" and role != "个人用户")

                # 确定背景色 - 使用多种方式获取主题色
                if is_selected and not is_disabled:
                    target_color = self.selected_role_color
                    target_elevation = 3
                else:
                    # 尝试多种方式获取卡片背景色
                    try:
                        app = MDApp.get_running_app()
                        if hasattr(app, 'theme') and hasattr(app.theme, 'CARD_BACKGROUND'):
                            target_color = app.theme.CARD_BACKGROUND
                        else:
                            target_color = getattr(AppTheme, "CARD_BACKGROUND", [1, 1, 1, 1])
                    except:
                        target_color = [1, 1, 1, 1]  # 默认白色背景
                    target_elevation = 1

                # 立即更新背景色和阴影 - 移除延迟调用以提高响应速度
                card.md_bg_color = target_color
                card.elevation = target_elevation

                # 更新禁用状态和透明度
                if is_disabled:
                    card.disabled = True
                    card.opacity = 0.5
                else:
                    card.disabled = False
                    card.opacity = 1.0

                self.logger.debug(f"更新角色卡片 {role}: 选中={is_selected}, 禁用={is_disabled}, 颜色={target_color}")
            except Exception as e:
                self.logger.exception(f"更新角色卡片 {role} 时出错: {e}")

    def _update_certificate_container(self, show_certificates):
        """更新证书容器的显示状态"""
        if hasattr(self, 'certificate_container'):
            from kivy.animation import Animation

            if show_certificates:
                # 显示证书容器 - 先启用再显示
                self.certificate_container.disabled = False
                anim = Animation(height=dp(280), opacity=1, duration=0.3)
                anim.start(self.certificate_container)
            else:
                # 隐藏证书容器 - 先隐藏再禁用
                anim = Animation(height=0, opacity=0, duration=0.3)
                anim.bind(on_complete=lambda *args: setattr(self.certificate_container, 'disabled', True))
                anim.start(self.certificate_container)

    def set_gender(self, gender):
        """设置性别"""
        self.gender = gender
        # 更新下拉菜单显示
        if hasattr(self, 'gender_spinner'):
            self.gender_spinner.text = gender
        # 兼容旧的按钮方式（如果存在）
        # 已移除旧的按钮方式，使用下拉菜单替代

    def _on_gender_selected(self, instance, value):
        """处理性别下拉菜单选择事件"""
        self.set_gender(value)

    def _on_id_card_input(self, instance, value):
        """身份证号码输入事件处理"""
        # 当输入长度达到18位时自动提取信息
        if len(value) == 18:
            self.extract_info_from_id_card(value)

    def extract_info_from_id_card(self, id_card_number):
        """从身份证号码中提取信息"""
        if not id_card_number or len(id_card_number) != 18:
            return

        try:
            # 提取出生日期
            birth_year = id_card_number[6:10]
            birth_month = id_card_number[10:12]
            birth_day = id_card_number[12:14]
            birth_date = f"{birth_year}/{birth_month}/{birth_day}"

            # 提取性别（倒数第二位，奇数为男，偶数为女）
            gender_code = int(id_card_number[-2])
            gender = "男" if gender_code % 2 == 1 else "女"

            # 提取地区代码（前6位）
            area_code = id_card_number[:6]
            _ = area_code  # 忽略未使用变量

            # 更新UI
            if hasattr(self, 'birth_date_input'):
                self.birth_date_input.text = birth_date
                self.birth_date = birth_date  # 同时更新属性
            self.set_gender(gender)

            # 标记身份证已验证
            self.id_card_verified = True

            # 显示提取成功消息
            self._show_message(f"已从身份证号码中提取信息：\n出生日期：{birth_date}\n性别：{gender}")

        except Exception as e:
            self._show_message(f"身份证号码格式错误：{str(e)}")

    def process_id_card_scan(self, scan_result):
        """处理身份证扫描结果"""
        try:
            # 更新身份证号
            if hasattr(self, 'id_card_input'):
                self.id_card_input.text = scan_result.get('id_number', '')

            # 更新姓名
            if hasattr(self, 'name_input'):
                self.name_input.text = scan_result.get('name', '')

            # 更新性别
            self.set_gender(scan_result.get('gender', '男'))

            # 更新出生日期
            if hasattr(self, 'birth_date_input'):
                self.birth_date_input.text = scan_result.get('birth_date', '')

            # 更新地址
            if hasattr(self, 'address_input'):
                self.address_input.text = scan_result.get('address', '')

            # 生成一个默认邮箱地址
            try:
                name = scan_result.get('name', '')
                id_number = scan_result.get('id_number', '')
                if name and id_number:
                    # 使用姓名拼音首字母和身份证后4位生成默认邮箱
                    try:
                        # 修复Pyright错误：添加类型注释表明这个导入是可选的
                        from utils.pinyin_utils import get_first_letters  # type: ignore
                        name_pinyin = get_first_letters(name)
                    except ImportError:
                        # 如果拼音工具不可用，使用简单的替代方案
                        name_pinyin = name[:2]  # 使用姓名前两个字符
                    email = f"{name_pinyin}{id_number[-4:]}@example.com".lower()
                    if hasattr(self, 'email_input'):
                        self.email_input.text = email
            except Exception as e:
                print(f"生成默认邮箱时出错: {e}")

            # 标记身份证已验证
            self.id_card_verified = True

            # 显示成功消息
            self._show_message("身份证信息已成功导入")

        except Exception as e:
            self._show_message(f"处理身份证信息时出错：{str(e)}")



    def show_birth_date_picker(self):
        """显示出生日期选择器"""
        try:
            show_kivymd_date_picker(
                title="选择出生日期",
                callback=self.on_birth_date_selected
            )
        except Exception as e:
            self._show_message(f"显示日期选择器失败: {str(e)}")

    def on_birth_date_selected(self, selected_date):
        """处理出生日期选择回调"""
        try:
            # 格式化日期为YYYY/MM/DD格式
            if hasattr(selected_date, 'strftime'):
                formatted_date = selected_date.strftime('%Y/%m/%d')
            else:
                # 如果是字符串，尝试解析后重新格式化
                from datetime import datetime
                if isinstance(selected_date, str):
                    # 尝试多种日期格式
                    for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y']:
                        try:
                            date_obj = datetime.strptime(selected_date, fmt)
                            formatted_date = date_obj.strftime('%Y/%m/%d')
                            break
                        except ValueError:
                            continue
                    else:
                        formatted_date = str(selected_date)
                else:
                    formatted_date = str(selected_date)

            # 更新文本字段和内部属性
            self.birth_date = formatted_date
            if hasattr(self, 'birth_date_input'):
                # 直接设置文本字段的值
                self.birth_date_input.text = formatted_date

            print(f"选择的出生日期: {formatted_date}")

        except Exception as e:
            print(f"处理出生日期选择时出错: {e}")
            self._show_message(f"处理出生日期时出错: {str(e)}")

    def upload_certificate(self, certificate_type):
        """上传资格证书 - 使用统一文件上传下载管理器"""
        try:
            # 设置当前处理的证书类型
            self.current_certificate_type = certificate_type

            # 获取证书名称用于显示
            certificate_name = "医师资格证书" if certificate_type == "medical_license" else "医师执业证书"

            # 使用统一文件管理器选择文件
            self.file_manager.select_file(
                callback=self._on_certificate_selected,
                file_types=['image', 'document'],  # 支持图片和文档
                title=f"选择{certificate_name}"
            )

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"打开文件选择器失败: {e}")
            self.show_error(f"选择文件失败: {str(e)}")

    def _on_certificate_selected(self, file_path):
        """处理证书文件选择结果 - 使用统一文件管理器"""
        if not file_path:
            return

        try:
            # 立即更新UI预览
            if self.current_certificate_type == "medical_license":
                self.medical_license_path = file_path
                if hasattr(self, 'medical_license_photo'):
                    self.medical_license_photo.source = file_path
                    self.medical_license_photo.reload()
            elif self.current_certificate_type == "practice_license":
                self.practice_license_path = file_path
                if hasattr(self, 'practice_license_photo'):
                    self.practice_license_photo.source = file_path
                    self.practice_license_photo.reload()

            # 获取证书名称和描述
            certificate_name = "医师资格证书" if self.current_certificate_type == "medical_license" else "医师执业证书"
            name = self.name_input.text if hasattr(self, 'name_input') else ""
            description = f"{certificate_name} for {name}"

            # 使用统一文件管理器上传
            self.file_manager.upload_file(
                file_path=file_path,
                callback=self._on_certificate_uploaded,
                metadata={"certificate_type": self.current_certificate_type, "description": description}
            )

            # 显示上传中提示
            self._show_message(f"正在上传{certificate_name}...")

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"处理证书选择时出错: {e}")
            self.show_error(f"处理文件时出错: {str(e)}")

    def _on_certificate_uploaded(self, success, result=None, error=None):
        """处理证书上传结果回调"""
        try:
            certificate_name = "医师资格证书" if self.current_certificate_type == "medical_license" else "医师执业证书"

            if success and result:
                # 上传成功
                document_id = result.get('document_id') or result.get('id') or result.get('file_id')

                if document_id:
                    # 添加到证书文档ID列表
                    if document_id not in self.certificate_document_ids:
                        self.certificate_document_ids.append(document_id)

                    # 更新状态标志
                    if self.current_certificate_type == "medical_license":
                        self.medical_license_taken = True
                    elif self.current_certificate_type == "practice_license":
                        self.practice_license_taken = True

                    self._show_message(f"{certificate_name}上传成功")

                    # 记录成功日志
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.info(f"证书上传成功: {certificate_name}, ID: {document_id}")
                else:
                    # 没有获取到文档ID
                    self._show_message(f"{certificate_name}上传成功，但未获取到文档ID")

            else:
                # 上传失败
                error_msg = str(error) if error else "未知错误"
                self._show_message(f"{certificate_name}上传失败: {error_msg}")

                # 重置UI状态
                if self.current_certificate_type == "medical_license":
                    if hasattr(self, 'medical_license_photo'):
                        self.medical_license_photo.source = ""
                        self.medical_license_photo.reload()
                    self.medical_license_path = ""
                elif self.current_certificate_type == "practice_license":
                    if hasattr(self, 'practice_license_photo'):
                        self.practice_license_photo.source = ""
                        self.practice_license_photo.reload()
                    self.practice_license_path = ""

                # 记录错误日志
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"证书上传失败: {certificate_name}, 错误: {error_msg}")

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"处理证书上传结果时出错: {e}")
            self._show_message(f"处理上传结果时出错: {str(e)}")

    def register(self):
        """处理用户注册"""
        try:
            # 验证输入
            if not self.validate_inputs():
                return

            # 获取输入信息
            username = self.username_input.text.strip()
            password = self.password_input.text.strip()
            confirm_password = self.confirm_password_input.text.strip()
            phone = self.phone_input.text.strip()
            real_name = self.name_input.text.strip()
            id_number = self.id_card_input.text.strip()
            gender = self.gender
            email = self.email_input.text.strip()
            birth_date = self.birth_date
            address = self.address_input.text.strip()
            emergency_contact = self.emergency_contact_input.text.strip()
            emergency_phone = self.emergency_phone_input.text.strip()

            # 准备后端注册数据
            user_data = {
                "username": username,
                "password": password,
                "phone": phone,
                "full_name": real_name,
                "id_number": id_number,
                "gender": gender,
                "email": email,
                "birth_date": birth_date,
                "address": address,
                "emergency_contact": emergency_contact,
                "emergency_phone": emergency_phone,
                "registration_type": self.registration_type,
                "relationship_type": self.relationship,
                "additional_roles": self._convert_roles_to_ids(self.selected_roles)
            }

            # 如果申请健康顾问角色，添加证书文档ID
            if "健康顾问" in self.selected_roles and self.certificate_document_ids:
                user_data["certificate_document_ids"] = self.certificate_document_ids

            # 设置主要角色
            if self.identity and self.identity in ["健康顾问", "单位管理员", "超级管理员"]:
                # 将中文角色名转换为后端期望的角色值
                role_map = {
                    "健康顾问": "consultant",
                    "单位管理员": "unit_admin",
                    "超级管理员": "super_admin"
                }
                user_data["role"] = role_map.get(self.identity, "personal")
            else:
                user_data["role"] = "personal"

            # 显示等待提示
            self._show_loading_dialog("正在连接后端服务器...")

            try:
                # 获取API客户端实例
                from utils.api_factory import get_api_client
                api_client = get_api_client(client_type="local")

                # 检查api_client是否为None
                if api_client is None:
                    self._dismiss_loading_dialog()
                    self._show_message("无法获取API客户端实例")
                    return

                # 向后端服务器注册用户
                result = api_client.register_user(user_data)
                self._dismiss_loading_dialog()

                if result and result.get('success', False):
                    # 注册成功
                    print(f"后端注册成功，用户ID: {result.get('user_id', '')}")

                    # 显示成功消息
                    self._show_message("注册成功！")

                    # 如果后端返回了访问令牌，可以保存到本地
                    if 'access_token' in result:
                        # 保存令牌到本地存储
                        from utils.storage import UserStorage
                        storage = UserStorage()
                        storage.save_token(result['access_token'])

                        # 保存用户信息到本地
                        if 'user' in result:
                            storage.save_user_info(result['user'])

                    # 延迟返回登录页面
                    Clock.schedule_once(self.switch_to_login, 2)
                else:
                    # 注册失败
                    error_message = api_client.last_error or "未知错误"
                    print(f"后端注册失败: {error_message}")

                    # 根据错误类型显示不同消息
                    if "身份证" in error_message and ("存在" in error_message or "已注册" in error_message or "已被" in error_message):
                        self._show_message(f"身份证号 {id_number} 已被注册")
                    elif "用户名" in error_message and ("存在" in error_message or "已注册" in error_message or "已被" in error_message):
                        self._show_message(f"用户名 {username} 已被注册")
                    elif "邮箱" in error_message and ("存在" in error_message or "已注册" in error_message or "已被" in error_message):
                        self._show_message(f"邮箱 {email} 已被注册")
                    else:
                        self._show_message(f"注册失败: {error_message}")

            except requests.exceptions.Timeout:
                self._dismiss_loading_dialog()
                self._show_message("连接后端服务器超时，请稍后重试")
                print("后端注册超时")

            except requests.exceptions.ConnectionError:
                self._dismiss_loading_dialog()
                self._show_message("无法连接到后端服务器，请检查网络连接")
                print("后端注册连接错误")

            except Exception as e:
                self._dismiss_loading_dialog()
                self._show_message(f"注册过程中发生错误: {str(e)}")
                print(f"后端注册异常: {str(e)}")
                import traceback
                traceback.print_exc()

        except Exception as e:
            print(f"注册失败: {e}")
            import traceback
            traceback.print_exc()
            self._show_message(f"注册失败: {str(e)}")

    def validate_inputs(self):
        """验证输入字段"""
        # 检查必填字段
        if not self.selected_roles:
            self._show_message("请选择至少一个用户角色")
            return False

        if not self.username_input.text:
            self._show_message("请输入用户名")
            return False

        if not self.password_input.text:
            self._show_message("请输入密码")
            return False

        if not self.confirm_password_input.text:
            self._show_message("请确认密码")
            return False

        if self.password_input.text != self.confirm_password_input.text:
            self._show_message("两次输入的密码不一致")
            return False

        # 验证注册类型和关系
        if self.registration_type == "替他人注册" and not self.relationship:
            self._show_message("请选择与被注册人的关系")
            return False

        if not self.id_card_input.text:
            self._show_message("请输入身份证号码")
            return False

        if not self._validate_id_card(self.id_card_input.text):
            self._show_message("请输入有效的身份证号码")
            return False

        if not self.name_input.text:
            self._show_message("请输入姓名")
            return False

        if not self.birth_date:
            self._show_message("请选择出生日期")
            return False

        if self.ethnicity_spinner.text == "请选择民族":
            self._show_message("请选择民族")
            return False

        if self.education_spinner.text == "请选择教育程度":
            self._show_message("请选择教育程度")
            return False

        if not self.phone_input.text:
            self._show_message("请输入联系电话")
            return False

        # 验证电子邮箱
        if not self.email_input.text:
            self._show_message("请输入电子邮箱")
            return False

        # 验证电子邮箱格式
        email = self.email_input.text
        if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            self._show_message("请输入有效的电子邮箱地址")
            return False

        # 验证紧急联系人（必填项）
        if not self.emergency_contact_input.text:
            self._show_message("请输入紧急联系人姓名")
            return False

        # 验证紧急联系人电话（必填项）
        if not self.emergency_phone_input.text:
            self._show_message("请输入紧急联系人电话")
            return False

        # 验证密码格式（字母+数字）
        password = self.password_input.text
        if not (re.search(r'[A-Za-z]', password) and re.search(r'[0-9]', password)):
            self._show_message("密码必须包含字母和数字")
            return False

        # 验证手机号格式
        phone = self.phone_input.text
        if not self._validate_phone(phone):
            self._show_message("请输入正确的手机号码")
            return False

        # 验证紧急联系人电话格式
        emergency_phone = self.emergency_phone_input.text
        if not self._validate_phone(emergency_phone):
            self._show_message("请输入正确的紧急联系人电话")
            return False

        # 验证出生日期格式
        birth_date = self.birth_date
        if not self._validate_date(birth_date):
            self._show_message("请输入正确的出生日期格式：YYYY/MM/DD")
            return False

        # 验证健康顾问角色的证书上传
        if "健康顾问" in self.selected_roles:
            if not self.medical_license_taken:
                self._show_message("申请健康顾问角色需要上传医师资格证书")
                return False
            if not self.practice_license_taken:
                self._show_message("申请健康顾问角色需要上传医师执业证书")
                return False

        return True

    def _validate_id_card(self, id_card):
        """验证身份证号码格式"""
        # 简单验证：18位，最后一位可能是X
        if len(id_card) != 18:
            return False

        # 前17位必须是数字
        if not id_card[0:17].isdigit():
            return False

        # 最后一位可以是数字或X
        if not (id_card[17].isdigit() or id_card[17].upper() == 'X'):
            return False

        # 验证出生日期部分是否合法
        try:
            birth_year = int(id_card[6:10])
            birth_month = int(id_card[10:12])
            birth_day = int(id_card[12:14])

            # 简单的日期验证
            if birth_year < 1900 or birth_year > datetime.datetime.now().year:
                return False
            if birth_month < 1 or birth_month > 12:
                return False
            if birth_day < 1 or birth_day > 31:
                return False
        except:
            return False

        return True

    def _validate_phone(self, phone):
        """验证手机号格式"""
        # 简单的手机号验证：11位数字，以1开头
        return bool(re.match(r'^1\d{10}$', phone))

    def _validate_date(self, date_str):
        """验证日期格式"""
        # 验证YYYY/MM/DD格式
        return bool(re.match(r'^\d{4}/\d{1,2}/\d{1,2}$', date_str))

    def switch_to_login(self, dt):
        """切换到登录页面"""
        try:
            app = MDApp.get_running_app()
            if app and app.root:
                app.root.current = 'login_screen'
        except Exception as e:
            self.logger.error(f"切换到登录页面失败: {e}")

    def back_to_login(self):
        """返回登录页面"""
        try:
            app = MDApp.get_running_app()
            if app and app.root:
                app.root.current = "login_screen"
        except Exception as e:
            self.logger.error(f"返回登录页面失败: {e}")

    def _dismiss_loading_dialog(self):
        """关闭加载对话框"""
        try:
            if hasattr(self, '_loading_dialog') and self._loading_dialog:
                self._loading_dialog.dismiss()
                self._loading_dialog = None
        except Exception as e:
            self.logger.error(f"关闭加载对话框失败: {str(e)}")

    def _show_message(self, message):
        """显示消息弹窗"""
        try:
            from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(
                MDSnackbarText(text=message),
                y=dp(24),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8
            )
            snackbar.open()
        except Exception as e:
            self.logger.error(f"显示消息时出错: {e}")

    def show_error(self, message):
        """显示错误消息"""
        self._show_message(message)

    def _convert_roles_to_ids(self, roles):
        """将角色列表转换为角色ID列表（字符串格式）"""
        role_id_map = {
            "个人用户": "personal",
            "单位管理员": "unit_admin",
            "健康顾问": "consultant",
            "超级管理员": "super_admin"
        }

        # 返回字符串角色值，而不是数字ID
        return [role_id_map.get(role, "personal") for role in roles if role in role_id_map]

    def on_enter(self, *args):
        """每次进入注册页面时调用"""
        # 调用父类的on_enter方法
        super().on_enter(*args)
        # 可以在这里添加其他初始化逻辑
        pass