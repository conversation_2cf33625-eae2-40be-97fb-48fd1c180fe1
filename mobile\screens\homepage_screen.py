from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty, ListProperty
from mobile.screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
from kivy.uix.image import Image
from kivy.uix.widget import Widget
import os
import json
import sys
from datetime import datetime
import threading
import tempfile
import traceback
from kivy.logger import Logger
from kivy.uix.progressbar import ProgressBar
from kivy.clock import Clock
from typing import Literal

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.list import MDList, MDListItem
from kivy.factory import Factory

# 导入主题和字体样式
from mobile.theme import AppTheme, AppMetrics, FontStyles, FontManager

# Logo由BaseScreen统一管理，无需单独导入HealthLogo或add_logo_to_layout

# 导入API客户端
try:
    from mobile.api.api_client import APIClient as MobileAPIClient
    APIClient: type = MobileAPIClient  # type: ignore
except ImportError:
    try:
        from api.api_client import APIClient as LocalAPIClient
        APIClient: type = LocalAPIClient  # type: ignore
    except ImportError:
        # 如果API客户端不存在，创建一个占位符
        class APIClient:
            pass

# 定义KV语言字符串
KV = '''
<ModuleCard>:
    orientation: 'vertical'
    size_hint: None, None
    size: dp(150), dp(120)  # 减小卡片大小
    md_bg_color: app.theme.CARD_BACKGROUND if app and hasattr(app, 'theme') and app.theme is not None else [0.9, 0.9, 0.9, 1]
    radius: [dp(12)]
    elevation: 4
    padding: [dp(10), dp(10), dp(10), dp(10)]  # 调整内边距
    pos_hint: {'center_x': 0.5}
    ripple_behavior: True

    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(6)  # 减小间距

        MDIconButton:
            icon: root.icon
            font_size: dp(28)  # 稍微减小图标大小
            pos_hint: {'center_x': 0.5}
            theme_icon_color: "Custom"
            icon_color: root.icon_color if root.icon_color else (app.theme.PRIMARY_DARK if app and hasattr(app, 'theme') and app.theme is not None else [0.2, 0.6, 1, 1])
            disabled: True

        MDLabel:
            text: root.title
            halign: 'center'
            font_style: "Body"
            role: "medium"
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_PRIMARY if app and hasattr(app, 'theme') and app.theme is not None else [0, 0, 0, 1]
            bold: True
            size_hint_y: None
            height: self.texture_size[1]

        MDLabel:
            text: root.description
            halign: 'center'
            font_style: "Label"
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_SECONDARY if app and hasattr(app, 'theme') and app.theme is not None else [0.5, 0.5, 0.5, 1]
            size_hint_y: None
            height: self.texture_size[1]
            shorten: True
            shorten_from: 'right'

<QuickActionCard>:
    orientation: 'horizontal'
    size_hint: 1, None
    height: dp(60)
    md_bg_color: app.theme.CARD_BACKGROUND if app and hasattr(app, 'theme') and app.theme is not None else [0.9, 0.9, 0.9, 1]
    radius: [dp(8)]
    elevation: 2
    padding: [dp(12), dp(8), dp(12), dp(8)]
    ripple_behavior: True

    MDIconButton:
        icon: root.icon
        font_size: dp(24)
        theme_icon_color: "Custom"
        icon_color: root.icon_color if root.icon_color else (app.theme.PRIMARY_DARK if app and hasattr(app, 'theme') and app.theme is not None else [0.2, 0.6, 1, 1])
        size_hint_x: None
        width: dp(40)
        disabled: True

    MDBoxLayout:
        orientation: 'vertical'
        spacing: dp(2)

        MDLabel:
            text: root.title
            font_style: "Body"
            role: "medium"
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_PRIMARY if app and hasattr(app, 'theme') and app.theme is not None else [0, 0, 0, 1]
            bold: True
            size_hint_y: None
            height: self.texture_size[1]

        MDLabel:
            text: root.subtitle
            font_style: "Label"
            theme_text_color: "Custom"
            text_color: app.theme.TEXT_SECONDARY if app and hasattr(app, 'theme') and app.theme is not None else [0.5, 0.5, 0.5, 1]
            size_hint_y: None
            height: self.texture_size[1]

    MDIconButton:
        icon: "chevron-right"
        font_size: dp(20)
        theme_icon_color: "Custom"
        icon_color: app.theme.TEXT_SECONDARY if app and hasattr(app, 'theme') and app.theme is not None else [0.5, 0.5, 0.5, 1]
        size_hint_x: None
        width: dp(32)
        disabled: True

<HomepageScreen>:
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_LIGHT if app and hasattr(app, 'theme') and app.theme is not None else [0.9, 0.95, 1.0, 1.0]
        Rectangle:
            pos: self.pos
            size: self.size

'''
# 只加载一次KV，确保ids绑定唯一
Builder.load_string(KV)

class ModuleCard(MDCard):
    """模块卡片组件"""
    icon = StringProperty("heart-pulse")
    title = StringProperty("功能")
    description = StringProperty("")
    bg_color = ListProperty(None)
    icon_color = ListProperty(None)
    on_release = ObjectProperty(None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 初始化时确保on_release有一个默认值
        if self.on_release is None:
            self.on_release = self._default_on_release
        # 绑定MDCard的on_release事件
        self.bind(on_release=self._on_release)

    def _default_on_release(self, *args):
        """默认的on_release处理函数"""
        Logger.info(f"[ModuleCard] 默认on_release处理函数被调用: {self.title}")

    def _on_release(self, instance):
        """处理MDCard的on_release事件"""
        # 确保on_release不为None
        if self.on_release is None:
            self.on_release = self._default_on_release
            
        # 更安全的事件处理方式
        if callable(self.on_release):
            try:
                self.on_release()
            except Exception as e:
                Logger.error(f"[ModuleCard] on_release 调用失败: {e}")
        else:
            Logger.warning(f"[ModuleCard] on_release 事件处理函数不可用: {self.title}")

class QuickActionCard(MDCard):
    """快速操作卡片组件"""
    icon = StringProperty("heart-pulse")
    title = StringProperty("操作")
    subtitle = StringProperty("")
    icon_color = ListProperty(None)
    on_release = ObjectProperty(None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 初始化时确保on_release有一个默认值
        if self.on_release is None:
            self.on_release = self._default_on_release
        # 绑定MDCard的on_release事件
        self.bind(on_release=self._on_release)

    def _default_on_release(self, *args):
        """默认的on_release处理函数"""
        Logger.info(f"[QuickActionCard] 默认on_release处理函数被调用: {self.title}")

    def _on_release(self, instance):
        """处理MDCard的on_release事件"""
        # 确保on_release不为None
        if self.on_release is None:
            self.on_release = self._default_on_release
            
        # 更安全的事件处理方式
        if callable(self.on_release):
            try:
                self.on_release()
            except Exception as e:
                Logger.error(f"[QuickActionCard] on_release 调用失败: {e}")
        else:
            Logger.warning(f"[QuickActionCard] on_release 事件处理函数不可用: {self.title}")

class SectionHeader(MDBoxLayout):
    """分区标题组件"""
    title = StringProperty("分区标题")
    title_color = ListProperty(None)
    __events__ = ('on_more',)  # 注册自定义事件

    def on_more(self, *args):
        pass  # 事件回调占位

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

class HomepageScreen(BaseScreen):
    """首页屏幕"""
    # 常量定义
    NAVIGATION_ERROR_MESSAGE = "页面跳转失败"

    user_name = StringProperty("XXX")
    user_gender = StringProperty("先生")  # 默认为先生，可以是"先生"或"女士"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = MDApp.get_running_app()
        # 延迟初始化UI，确保主题已加载
        Clock.schedule_once(self.init_ui)

    def init_ui(self, dt=0):
        """初始化UI"""
        # 首页不显示顶端导航栏，需在父类初始化前设置
        self.show_top_bar = False
        # 调用父类的init_ui方法（会统一添加Logo并清理重复）
        super().init_ui(dt)

        # 加载用户数据
        self.load_user_data()

        # 设置欢迎消息
        self.set_welcome_message()

        # 加载四大功能模块
        self.load_modules()

        # 加载快速操作
        self.load_quick_actions()

    def load_modules(self):
        """加载四大功能模块"""
        app = MDApp.get_running_app()
        modules_data = [
            {
                'title': '健康数据管理',
                'icon': 'heart-pulse',
                'description': '记录和管理您的健康数据',
                'color': getattr(app.theme, 'HEALTH_DATA_COLOR', [0.3, 0.8, 0.3, 1]) if app and hasattr(app, 'theme') else [0.3, 0.8, 0.3, 1],
                'action': self.on_health_data
            },
            {
                'title': '健康风险管理',
                'icon': 'shield-heart',
                'description': '评估和预防健康风险',
                'color': getattr(app.theme, 'HEALTH_RISK_COLOR', [1, 0.6, 0, 1]) if app and hasattr(app, 'theme') else [1, 0.6, 0, 1],
                'action': self.on_health_risk
            },
            {
                'title': '慢性病管理',
                'icon': 'medical-bag',
                'description': '慢性疾病的专业管理',
                'color': getattr(app.theme, 'WARNING_COLOR', [1, 0.8, 0, 1]) if app and hasattr(app, 'theme') else [1, 0.8, 0, 1],
                'action': self.on_chronic_disease
            },
            {
                'title': '医疗服务',
                'icon': 'hospital-box',
                'description': '在线医疗咨询服务',
                'color': getattr(app.theme, 'MEDICAL_SERVICE_COLOR', [0.2, 0.6, 1, 1]) if app and hasattr(app, 'theme') else [0.2, 0.6, 1, 1],
                'action': self.on_medical_service
            }
        ]

        # 使用实例变量而不是ids字典
        modules_grid = getattr(self, 'modules_grid', None)
        if modules_grid:
            modules_grid.clear_widgets()

            for module in modules_data:
                # 确保action是一个可调用的函数
                action = module['action']
                if action is not None and callable(action):
                    card = ModuleCard(
                        title=module['title'],
                        icon=module['icon'],
                        description=module['description'],
                        icon_color=module['color'],
                        on_release=action
                    )
                else:
                    # 如果action不可用，使用一个空的回调函数
                    def empty_callback(*args):
                        Logger.warning(f"[HomepageScreen] 模块 {module['title']} 的回调函数不可用")
                    
                    card = ModuleCard(
                        title=module['title'],
                        icon=module['icon'],
                        description=module['description'],
                        icon_color=module['color'],
                        on_release=empty_callback
                    )
                modules_grid.add_widget(card)
            
            # 确保绑定minimum_height以支持自适应内容
            modules_grid.bind(minimum_height=modules_grid.setter('height'))
            
            # 调度一次更新以确保高度正确计算
            Clock.schedule_once(lambda dt: self._update_modules_height(), 0)

    def _update_modules_height(self):
        """更新模块区域高度"""
        try:
            modules_grid = getattr(self, 'modules_grid', None)
            health_modules_card = getattr(self, 'health_modules_card', None)
            
            if modules_grid and health_modules_card:
                # 强制重新计算网格的最小高度
                modules_grid.do_layout()
                
                # 确保网格有正确的子组件数量
                children_count = len(modules_grid.children)
                rows = max(1, (children_count + 1) // 2)  # 每行2个卡片，计算需要的行数
                
                # 更新网格高度 - 基于实际的行数计算，使用新的卡片高度120dp
                calculated_height = int(dp(120) * rows + dp(16) * max(0, rows - 1))
                modules_grid.height = calculated_height
                
                # 更新健康管理服务卡片高度
                # 计算卡片总高度：标题高度+内边距+网格高度
                title_height = int(dp(30))
                padding_height = int(dp(16) * 2)  # 上下内边距
                grid_height = int(modules_grid.height)
                total_height = int(title_height + padding_height + grid_height)
                
                health_modules_card.height = total_height
                # 确保绑定minimum_height以支持自适应内容
                health_modules_card.bind(minimum_height=health_modules_card.setter('height'))
                
                Logger.info(f"[HomepageScreen] 更新模块区域高度: 子组件数={children_count}, 行数={rows}, 网格高度={grid_height}, 总高度={total_height}")
        except Exception as e:
            Logger.error(f"[HomepageScreen] 更新模块区域高度失败: {e}")
            import traceback
            traceback.print_exc()

    def load_quick_actions(self):
        """加载快速操作"""
        quick_actions_data = [
            {
                'title': '健康状态总览',
                'icon': 'chart-line',
                'action': self.navigate_to_health_overview
            },
            {
                'title': '语音分诊',
                'icon': 'microphone',
                'action': self.navigate_to_voice_triage
            },
            {
                'title': '健康日记',
                'icon': 'book-open-variant',
                'action': self.navigate_to_health_diary
            }
        ]

        # 使用实例变量而不是ids字典
        quick_actions_box = getattr(self, 'quick_actions_box', None)
        if quick_actions_box:
            quick_actions_box.clear_widgets()

            for action in quick_actions_data:
                # 确保action是一个可调用的函数
                action_func = action['action']
                if action_func is not None and callable(action_func):
                    card = QuickActionCard(
                        title=action['title'],
                        icon=action['icon'],
                        subtitle="",  # 添加空的subtitle属性
                        on_release=action_func
                    )
                else:
                    # 如果action不可用，使用一个空的回调函数
                    def empty_callback(*args):
                        Logger.warning(f"[HomepageScreen] 快速操作 {action['title']} 的回调函数不可用")
                    
                    card = QuickActionCard(
                        title=action['title'],
                        icon=action['icon'],
                        subtitle="",  # 添加空的subtitle属性
                        on_release=empty_callback
                    )
                quick_actions_box.add_widget(card)
            
            # 更新快速操作区域高度 - 紧凑布局
            # 使用调度机制确保高度计算在下一帧完成
            Clock.schedule_once(lambda dt: self._update_quick_actions_height(), 0)

    def _update_quick_actions_height(self):
        """更新快速操作区域高度"""
        try:
            # 更新快速操作内容区域高度
            quick_actions_box = getattr(self, 'quick_actions_box', None)
            quick_actions_card = getattr(self, 'quick_actions_card', None)
            
            if quick_actions_box:
                # 确保高度正确计算 - 每个QuickActionCard高度为60dp
                item_height = int(dp(60))
                spacing = int(dp(12))
                content_height = int(len(quick_actions_box.children) * item_height + max(0, (len(quick_actions_box.children) - 1) * spacing))
                quick_actions_box.height = int(content_height or dp(60))
                
            if quick_actions_card and quick_actions_box:
                # 更新快速操作卡片高度，确保包含内容区域和内边距
                content_height = int(quick_actions_box.height if quick_actions_box else dp(60))
                # 计算总高度：标题栏高度 + 内容区域高度 + 上下内边距
                header_height = int(dp(40))  # 标题栏高度
                padding_height = int(dp(36))  # 上下内边距 (16+20)
                total_height = int(header_height + content_height + padding_height)
                
                # 确保最小高度
                min_height = int(dp(120))  # 最小高度
                quick_actions_card.height = int(max(total_height, min_height))
                
                Logger.info(f"[HomepageScreen] 更新快速操作区域高度: 内容高度={content_height}, 总高度={total_height}")
        except Exception as e:
            Logger.error(f"[HomepageScreen] 更新快速操作区域高度失败: {e}")
            # 出错时设置默认高度
            quick_actions_card = getattr(self, 'quick_actions_card', None)
            if quick_actions_card:
                quick_actions_card.height = int(dp(200))

    def do_content_setup(self):
        """在content_container中添加内容"""
        try:
            # 安全地获取content_container
            content_container = None
            if hasattr(self, 'ids') and isinstance(self.ids, dict):
                content_container = self.ids.get('content_container')
            
            if not content_container:
                Logger.error(f"[HomepageScreen] ERROR: 无法找到content_container")
                return
            
            # 清空content_container中的现有内容
            content_container.clear_widgets()
            
            # 创建欢迎区域
            from kivymd.uix.card import MDCard
            from kivymd.uix.label import MDLabel
            from kivymd.uix.gridlayout import MDGridLayout
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.button import MDIconButton
            
            # 主内容区域 - 直接添加到content_container，不再使用ScrollView
            main_layout = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                height=int(self.height if self.height > 0 else dp(800)),  # 初始高度
                padding=[int(dp(16)), int(dp(16)), int(dp(16)), int(dp(100))],  # 减小左右内边距
                spacing=int(dp(20))  # 稍微减小间距
            )
            main_layout.bind(minimum_height=main_layout.setter('height'))
            
            # 欢迎区域
            welcome_card = MDCard(
                orientation='vertical',
                size_hint_y=None,
                padding=[int(dp(20)), int(dp(16)), int(dp(20)), int(dp(16))],
                spacing=int(dp(8)),
                radius=[int(dp(16))],
                elevation=2,
                md_bg_color=getattr(self.app.theme, 'CARD_BACKGROUND', [0.95, 0.95, 0.95, 1]) if self.app and hasattr(self.app, 'theme') else [0.95, 0.95, 0.95, 1]
            )
            # 绑定卡片高度到其最小高度
            welcome_card.bind(minimum_height=welcome_card.setter('height'))
            
            welcome_layout = MDBoxLayout(
                orientation='vertical',
                spacing=int(dp(4))
            )
            
            welcome_label = MDLabel(
                text="欢迎回来！",
                font_style="Body",
                role="large",
                bold=True,
                halign="left",
                size_hint_y=None,
                height=int(dp(30)),
                theme_text_color="Custom",
                text_color=getattr(self.app.theme, 'TEXT_PRIMARY', [0, 0, 0, 1]) if self.app and hasattr(self.app, 'theme') else [0, 0, 0, 1]
            )
            # 保存welcome_label的引用，以便后续可以更新
            self.welcome_label = welcome_label
            
            welcome_layout.add_widget(welcome_label)
            welcome_card.add_widget(welcome_layout)
            main_layout.add_widget(welcome_card)
            
            # 四大功能模块区域 (健康管理服务，MDCard)
            self.health_modules_card = MDCard(
                orientation='vertical',
                size_hint_y=None,
                padding=[int(dp(16)), int(dp(16)), int(dp(16)), int(dp(16))],  # 减小内边距
                spacing=int(dp(12)),  # 减小间距
                radius=[int(dp(16))],
                elevation=2,
                md_bg_color=getattr(self.app.theme, 'CARD_BACKGROUND', [0.95, 0.95, 0.95, 1]) if self.app and hasattr(self.app, 'theme') else [0.95, 0.95, 0.95, 1]
            )
            # 绑定卡片高度到其最小高度
            self.health_modules_card.bind(minimum_height=self.health_modules_card.setter('height'))
            
            # 偮康管理服务标题
            module_title = MDLabel(
                text="健康管理服务",
                font_style="Body",
                role="large",
                bold=True,
                halign="left",
                size_hint_y=None,
                height=int(dp(30)),
                theme_text_color="Custom",
                text_color=getattr(self.app.theme, 'PRIMARY_DARK', [0.2, 0.6, 1, 1]) if self.app and hasattr(self.app, 'theme') else [0.2, 0.6, 1, 1]
            )
            self.health_modules_card.add_widget(module_title)
            
            # 模块网格布局
            modules_grid = MDGridLayout(
                cols=2,
                size_hint_y=None,
                spacing=int(dp(12))  # 稍微减小间距
            )
            # 保存modules_grid的引用
            self.modules_grid = modules_grid
            self.health_modules_card.add_widget(modules_grid)
            
            # 绑定网格高度到其最小高度，确保正确计算
            modules_grid.bind(minimum_height=modules_grid.setter('height'))
            
            main_layout.add_widget(self.health_modules_card)
            
            # 快速操作区域 (MDCard)
            self.quick_actions_card = MDCard(
                orientation='vertical',
                size_hint_y=None,
                padding=[int(dp(20)), int(dp(16)), int(dp(20)), int(dp(20))],
                spacing=int(dp(16)),
                radius=[int(dp(16))],
                elevation=2,
                md_bg_color=getattr(self.app.theme, 'CARD_BACKGROUND', [0.95, 0.95, 0.95, 1]) if self.app and hasattr(self.app, 'theme') else [0.95, 0.95, 0.95, 1]
            )
            # 绑定卡片高度到其最小高度
            self.quick_actions_card.bind(minimum_height=self.quick_actions_card.setter('height'))
            
            # 快速操作标题栏，包含增加和删除按钮
            quick_actions_header = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=int(dp(40)),
                spacing=int(dp(8))
            )
            
            quick_actions_title = MDLabel(
                text="快速操作",
                font_style="Body",
                role="large",
                bold=True,
                halign="left",
                size_hint_x=1,
                size_hint_y=None,
                height=int(dp(30)),
                theme_text_color="Custom",
                text_color=getattr(self.app.theme, 'PRIMARY_DARK', [0.2, 0.6, 1, 1]) if self.app and hasattr(self.app, 'theme') else [0.2, 0.6, 1, 1]
            )
            
            # 添加按钮
            add_button = MDIconButton(
                icon="plus",
                font_size=int(dp(24)),
                theme_icon_color="Custom",
                icon_color=getattr(self.app.theme, 'SUCCESS_COLOR', [0, 1, 0, 1]) if self.app and hasattr(self.app, 'theme') else [0, 1, 0, 1],
                size_hint_x=None,
                width=int(dp(40))
            )
            # 使用更安全的方式绑定事件
            def _on_add_quick_action(instance):
                if hasattr(self, 'on_add_quick_action') and self.on_add_quick_action is not None and callable(self.on_add_quick_action):
                    self.on_add_quick_action()
                else:
                    Logger.warning("[HomepageScreen] on_add_quick_action 方法不可用")
            
            add_button.bind(on_release=_on_add_quick_action)
            
            # 删除按钮
            remove_button = MDIconButton(
                icon="minus",
                font_size=int(dp(24)),
                theme_icon_color="Custom",
                icon_color=getattr(self.app.theme, 'ERROR_COLOR', [1, 0, 0, 1]) if self.app and hasattr(self.app, 'theme') else [1, 0, 0, 1],
                size_hint_x=None,
                width=int(dp(40))
            )
            # 使用更安全的方式绑定事件
            def _on_remove_quick_action(instance):
                if hasattr(self, 'on_remove_quick_action') and self.on_remove_quick_action is not None and callable(self.on_remove_quick_action):
                    self.on_remove_quick_action()
                else:
                    Logger.warning("[HomepageScreen] on_remove_quick_action 方法不可用")
            
            remove_button.bind(on_release=_on_remove_quick_action)
            
            quick_actions_header.add_widget(quick_actions_title)
            quick_actions_header.add_widget(add_button)
            quick_actions_header.add_widget(remove_button)
            self.quick_actions_card.add_widget(quick_actions_header)
            
            # 快速操作内容
            quick_actions_box = MDBoxLayout(
                orientation='vertical',
                size_hint_y=None,
                spacing=int(dp(12))
            )
            # 保存quick_actions_box的引用
            self.quick_actions_box = quick_actions_box
            self.quick_actions_card.add_widget(quick_actions_box)
            
            # 绑定快速操作内容区域高度到其最小高度，确保自适应内容
            quick_actions_box.bind(minimum_height=quick_actions_box.setter('height'))
            main_layout.add_widget(self.quick_actions_card)
            
            # 直接将main_layout添加到content_container，不再使用ScrollView
            content_container.add_widget(main_layout)
            
            # 加载模块数据
            self.load_modules()
            self.load_quick_actions()
            
            Logger.info("[HomepageScreen] 成功添加内容到content_container")
        except Exception as e:
            Logger.error(f"[HomepageScreen] 添加内容到content_container失败: {e}")
            import traceback
            traceback.print_exc()

    def on_enter(self, *args):
        """进入屏幕时调用"""
        # 强制校验登录状态
        app = MDApp.get_running_app()

        # 检查是否已登录
        is_logged_in = False
        user_data = getattr(app, 'user_data', None)
        if user_data is not None and user_data.get('username'):
            # 检查cloud_api的认证状态
            try:
                from utils.cloud_api import get_cloud_api
                cloud_api = get_cloud_api()
                if cloud_api and cloud_api.is_authenticated():
                    is_logged_in = True
            except Exception as e:
                print(f"检查cloud_api认证状态时出错: {e}")

        if not is_logged_in:
            # 未登录或认证无效，强制跳转回登录页并提示
            if self.manager:
                self.manager.current = 'login_screen'
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="请先登录"))
            snackbar.open()
            return

        # 已登录，刷新用户数据
        self.load_user_data()
        
        # 页面内容将在do_content_setup中加载

    def load_user_data(self):
        """加载用户数据"""
        try:
            # 获取应用实例
            app = MDApp.get_running_app()

            # 检查是否有用户数据
            user_data = getattr(app, 'user_data', None)
            if user_data is not None:
                # 确保user_data不为None
                if user_data is not None:
                    # 获取用户名
                    self.user_name = user_data.get('username', 'XXX')
                    # 获取用户性别
                    gender = user_data.get('gender', '')
                    if gender.lower() in ['female', 'f', '女', '女性']:
                        self.user_gender = "女士"
                    else:
                        self.user_gender = "先生"
                    # 设置欢迎消息
                    self.set_welcome_message(self.user_name, self.user_gender)
            else:
                # 没有用户数据，使用默认值
                self.user_name = "访客"
                self.user_gender = "先生"
                self.set_welcome_message(self.user_name, self.user_gender)
        except Exception as e:
            Logger.error(f"加载用户数据失败: {str(e)}")
            # 使用默认值
            self.user_name = "访客"
            self.user_gender = "先生"
            self.set_welcome_message(self.user_name, self.user_gender)

    def set_welcome_message(self, name=None, gender=None):
        """设置欢迎消息"""
        try:
            if name is None:
                name = self.user_name
            if gender is None:
                gender = self.user_gender
            # 使用实例变量而不是ids字典
            welcome_label = getattr(self, 'welcome_label', None)
            # 根据时间段设置不同的问候语
            now = datetime.now()
            hour = now.hour
            greeting = ""
            if 5 <= hour < 12:
                greeting = "早上好"
            elif 12 <= hour < 14:
                greeting = "中午好"
            elif 14 <= hour < 18:
                greeting = "下午好"
            else:
                greeting = "晚上好"
            if welcome_label:
                welcome_label.text = f"{greeting}，{name} {gender}！"
        except Exception as e:
            Logger.error(f"设置欢迎消息失败: {str(e)}")

    def go_back(self):
        """返回上一级"""
        # 在测试环境中，不执行跳转操作
        from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
        snackbar = MDSnackbar(MDSnackbarText(text="返回功能在测试环境中暂不可用"))
        snackbar.open()

    def on_profile(self):
        """用户资料按钮点击"""
        # 跳转到用户资料页面
        if self.manager and self.manager.has_screen('profile_page'):
            self.manager.current = 'profile_page'
        else:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="用户资料功能开发中"))
            snackbar.open()

    # 四大功能模块事件处理
    def on_health_data(self, *args):
        """健康数据管理"""
        # 导航到健康资料管理页面
        app = MDApp.get_running_app()
        sm = app.root if app else None
        # 检查是否已经有健康资料管理屏幕
        if sm and not sm.has_screen("health_data_management_screen"):
            # 导入并添加健康资料管理屏幕
            from screens.health_data_management_screen import HealthDataManagementScreen
            sm.add_widget(HealthDataManagementScreen(name="health_data_management_screen"))
        # 导航到健康资料管理屏幕
        if sm:
            sm.transition.direction = 'left'
            sm.current = "health_data_management_screen"

    def on_health_risk(self, *args):
        """健康风险管理"""
        if self.manager and self.manager.has_screen('health_risk_screen'):
            self.manager.current = 'health_risk_screen'
        else:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="健康风险管理功能开发中"))
            snackbar.open()

    def on_chronic_disease(self, *args):
        """慢性病管理"""
        if self.manager and self.manager.has_screen('chronic_disease_screen'):
            self.manager.current = 'chronic_disease_screen'
        else:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="慢性病管理功能开发中"))
            snackbar.open()

    def on_medical_service(self, *args):
        """医疗服务"""
        if self.manager and self.manager.has_screen('medical_service_screen'):
            self.manager.current = 'medical_service_screen'
        else:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            snackbar = MDSnackbar(MDSnackbarText(text="医疗服务功能开发中"))
            snackbar.open()

    # 快速操作导航方法
    # 删除重复的方法声明，保留后面完整实现的版本

    def add_health_data_modules(self):
        """添加健康资料管理模块"""
        app = MDApp.get_running_app()
        # 修复访问ids字典的方式
        health_data_grid = self.ids.get('health_data_grid') if hasattr(self, 'ids') else None
        if not health_data_grid:
            Logger.warning("health_data_grid not found in ids")
            return
            
        health_data_grid.clear_widgets()

        # 健康状态总览 - 只能查看，不可编辑
        health_overview = ModuleCard(
            icon="chart-box",
            title="健康状态总览",
            description="查看自动总结的健康信息",
            bg_color=app.theme.HEALTH_DATA_LIGHT if app and hasattr(app, 'theme') else [0.9, 1, 0.9, 1],
            icon_color=app.theme.HEALTH_DATA_COLOR if app and hasattr(app, 'theme') else [0.3, 0.8, 0.3, 1],
            on_release=lambda x: self.navigate_to_health_overview()
        )
        health_data_grid.add_widget(health_overview)

        # 基本健康信息问卷
        basic_health = ModuleCard(
            icon="clipboard-text",
            title="基本健康信息",
            description="录入、修改、提交健康信息",
            bg_color=app.theme.HEALTH_DATA_LIGHT if app and hasattr(app, 'theme') else [0.9, 1, 0.9, 1],
            icon_color=app.theme.HEALTH_DATA_COLOR if app and hasattr(app, 'theme') else [0.3, 0.8, 0.3, 1],
            on_release=lambda x: self.navigate_to_basic_health()
        )
        health_data_grid.add_widget(basic_health)

        # 医疗记录管理 - 整合住院、门诊、检验、技诊报告
        medical_records = ModuleCard(
            icon="folder-medical",
            title="医疗记录管理",
            description="住院、门诊、检验、技诊报告统一管理",
            bg_color=app.theme.HEALTH_DATA_LIGHT if app and hasattr(app, 'theme') else [0.9, 1, 0.9, 1],
            icon_color=app.theme.HEALTH_DATA_COLOR if app and hasattr(app, 'theme') else [0.3, 0.8, 0.3, 1],
            on_release=lambda x: self.navigate_to_medical_records()
        )
        health_data_grid.add_widget(medical_records)

        # 调查问卷/评估量表
        survey = ModuleCard(
            icon="clipboard-check",
            title="调查问卷/评估量表",
            description="搜索、填写、查看结果",
            bg_color=app.theme.HEALTH_DATA_LIGHT if app and hasattr(app, 'theme') else [0.9, 1, 0.9, 1],
            icon_color=app.theme.HEALTH_DATA_COLOR if app and hasattr(app, 'theme') else [0.3, 0.8, 0.3, 1],
            on_release=lambda x: self.navigate_to_survey()
        )
        health_data_grid.add_widget(survey)

        # 用药管理 - 整合用药记录和管理
        medication_management = ModuleCard(
            icon="pill",
            title="用药管理",
            description="药品记录、剂量管理、服药提醒",
            bg_color=app.theme.HEALTH_DATA_LIGHT if app and hasattr(app, 'theme') else [0.9, 1, 0.9, 1],
            icon_color=app.theme.HEALTH_DATA_COLOR if app and hasattr(app, 'theme') else [0.3, 0.8, 0.3, 1],
            on_release=lambda x: self.navigate_to_medication_management()
        )
        health_data_grid.add_widget(medication_management)

        # 健康日记
        health_diary = ModuleCard(
            icon="book-open-variant",
            title="健康日记",
            description="血压、血糖、体重等日常管理",
            bg_color=app.theme.HEALTH_DATA_LIGHT if app and hasattr(app, 'theme') else [0.9, 1, 0.9, 1],
            icon_color=app.theme.HEALTH_DATA_COLOR if app and hasattr(app, 'theme') else [0.3, 0.8, 0.3, 1],
            on_release=lambda x: self.navigate_to_health_diary()
        )
        health_data_grid.add_widget(health_diary)

        # 健康文档管理 - 资料上传和管理
        health_document = ModuleCard(
            icon="folder-upload",
            title="健康文档管理",
            description="直接上传、二维码、拍照上传",
            bg_color=app.theme.HEALTH_DATA_LIGHT if app and hasattr(app, 'theme') else [0.9, 1, 0.9, 1],
            icon_color=app.theme.HEALTH_DATA_COLOR if app and hasattr(app, 'theme') else [0.3, 0.8, 0.3, 1],
            on_release=lambda x: self.navigate_to_health_document()
        )
        health_data_grid.add_widget(health_document)

        # 其它记录
        other_records = ModuleCard(
            icon="text-box",
            title="其它记录",
            description="文字记录其他健康信息",
            bg_color=app.theme.HEALTH_DATA_LIGHT if app and hasattr(app, 'theme') else [0.9, 1, 0.9, 1],
            icon_color=app.theme.HEALTH_DATA_COLOR if app and hasattr(app, 'theme') else [0.3, 0.8, 0.3, 1],
            on_release=lambda x: self.navigate_to_other_records()
        )
        health_data_grid.add_widget(other_records)

        # 管理日志 - 健康顾问专用
        user_role = getattr(app, 'user_role', 'personal') if app else 'personal'
        if user_role in ['consultant', 'admin', 'supermanager']:
            management_log = ModuleCard(
                icon="clipboard-list",
                title="管理日志",
                description="健康顾问管理记录",
                bg_color=app.theme.HEALTH_DATA_LIGHT if app and hasattr(app, 'theme') else [0.9, 1, 0.9, 1],
                icon_color=app.theme.HEALTH_DATA_COLOR if app and hasattr(app, 'theme') else [0.3, 0.8, 0.3, 1],
                on_release=lambda x: self.navigate_to_management_log()
            )
            health_data_grid.add_widget(management_log)

    def add_health_risk_modules(self):
        """添加健康风险管理模块"""
        app = MDApp.get_running_app()
        # 修复访问ids字典的方式
        health_risk_grid = self.ids.get('health_risk_grid') if hasattr(self, 'ids') else None
        if not health_risk_grid:
            Logger.warning("health_risk_grid not found in ids")
            return
            
        health_risk_grid.clear_widgets()

        # 恶性病风险评估
        malignant_risk = ModuleCard(
            icon="alert-circle",
            title="恶性病风险评估",
            description="基于健康状况的AI风险评估",
            bg_color=app.theme.HEALTH_RISK_LIGHT if app and hasattr(app, 'theme') else [1, 0.9, 0.8, 1],
            icon_color=app.theme.HEALTH_RISK_COLOR if app and hasattr(app, 'theme') else [1, 0.6, 0, 1],
            on_release=lambda x: self.navigate_to_malignant_risk()
        )
        health_risk_grid.add_widget(malignant_risk)

        # 慢性病风险评估
        chronic_risk = ModuleCard(
            icon="heart-pulse",
            title="慢性病风险评估",
            description="基于健康状况的慢性病风险分析",
            bg_color=app.theme.HEALTH_RISK_LIGHT if app and hasattr(app, 'theme') else [1, 0.9, 0.8, 1],
            icon_color=app.theme.HEALTH_RISK_COLOR if app and hasattr(app, 'theme') else [1, 0.6, 0, 1],
            on_release=lambda x: self.navigate_to_chronic_risk()
        )
        health_risk_grid.add_widget(chronic_risk)

        # 个性化体检方案订制
        personalized_checkup = ModuleCard(
            icon="clipboard-check-multiple",
            title="个性化体检方案",
            description="基于健康状况的AI体检方案定制",
            bg_color=app.theme.HEALTH_RISK_LIGHT if app and hasattr(app, 'theme') else [1, 0.9, 0.8, 1],
            icon_color=app.theme.HEALTH_RISK_COLOR if app and hasattr(app, 'theme') else [1, 0.6, 0, 1],
            on_release=lambda x: self.navigate_to_personalized_checkup()
        )
        health_risk_grid.add_widget(personalized_checkup)

    def add_medical_service_modules(self):
        """添加医疗服务模块"""
        app = MDApp.get_running_app()
        # 先清空现有内容
        # 修复访问ids字典的方式
        medical_service_grid = self.ids.get('medical_service_grid') if hasattr(self, 'ids') else None
        if not medical_service_grid:
            Logger.warning("medical_service_grid not found in ids")
            return
            
        medical_service_grid.clear_widgets()

        # 添加语音分诊服务
        triage_card = ModuleCard(
            icon="microphone",
            title="语音分诊",
            description="AI语音问诊，生成就诊建议",
            bg_color=app.theme.MEDICAL_SERVICE_LIGHT if app and hasattr(app, 'theme') else [0.9, 0.95, 1, 1],
            icon_color=app.theme.MEDICAL_SERVICE_COLOR if app and hasattr(app, 'theme') else [0.2, 0.6, 1, 1],
            on_release=lambda x: self.navigate_to_voice_triage()
        )
        medical_service_grid.add_widget(triage_card)

        # 添加陪诊服务 - 新功能
        companion_card = ModuleCard(
            icon="account-heart",
            title="陪诊服务",
            description="挂号、接送、住宿、饮食全程服务",
            bg_color=app.theme.MEDICAL_SERVICE_LIGHT if app and hasattr(app, 'theme') else [0.9, 0.95, 1, 1],
            icon_color=app.theme.MEDICAL_SERVICE_COLOR if app and hasattr(app, 'theme') else [0.2, 0.6, 1, 1],
            on_release=lambda x: self.navigate_to_medical_companion()
        )
        medical_service_grid.add_widget(companion_card)

    def navigate_to_profile(self, *args):
        """导航到个人资料页面"""
        from kivy.logger import Logger as KivyLogger
        KivyLogger.info("导航到个人资料页面")
        app = MDApp.get_running_app()
        sm = app.root if app else None
        # 检查是否已经有个人资料屏幕
        if sm and not sm.has_screen("profile_page"):
            # 导入并添加个人资料屏幕
            from screens.profile_page import ProfilePage
            sm.add_widget(ProfilePage(name="profile_page"))
        # 导航到个人资料屏幕
        if sm:
            sm.current = "profile_page"

    def on_view_more(self, category):
        """查看更多按钮点击事件"""
        if category == "health_data":
            # 导航到健康资料管理页面
            self.show_info("导航到健康资料管理页面")
        elif category == "health_risk":
            # 导航到健康风险管理页面
            self.show_info("导航到健康风险管理页面")
        elif category == "medical_service":
            # 导航到就医服务页面
            self.show_info("导航到就医服务页面")

    def toggle_menu(self):
        """菜单按钮点击事件"""
        # 显示菜单
        self.show_info("菜单功能待实现")

    def navigate_to_health_overview(self, *args):
        """导航到健康状态总览页面"""
        app = MDApp.get_running_app()
        sm = app.root if app else None
        if sm:
            sm.transition.direction = 'left'
            sm.current = 'health_overview_screen'

    def navigate_to_basic_health(self, *args):
        """导航到基本健康信息页面"""
        app = MDApp.get_running_app()
        sm = app.root if app else None
        if sm:
            sm.transition.direction = 'left'
            sm.current = 'basic_health_info_screen'

    def navigate_to_hospital_records(self, *args):
        """导航到住院资料页面"""
        app = MDApp.get_running_app()
        sm = app.root if app else None
        if sm:
            sm.transition.direction = 'left'
            sm.current = 'hospital_records'

    def navigate_to_outpatient_records(self, *args):
        """导航到门诊资料页面"""
        app = MDApp.get_running_app()
        sm = app.root if app else None
        if sm:
            sm.transition.direction = 'left'
            sm.current = 'outpatient_records'

    def navigate_to_lab_report(self, *args):
        app = MDApp.get_running_app()
        sm = app.root if app else None
        if sm:
            sm.transition.direction = 'left'
            sm.current = 'lab_report'

    def navigate_to_tech_diagnosis_report(self, *args):
        app = MDApp.get_running_app()
        sm = app.root if app else None
        if sm:
            sm.transition.direction = 'left'
            sm.current = 'tech_diagnosis_report'

    def navigate_to_physical_exam(self, *args):
        app = MDApp.get_running_app()
        sm = app.root if app else None
        if sm:
            sm.transition.direction = 'left'
            sm.current = 'physical_exam'

    def navigate_to_malignant_risk(self, *args):
        """导航到恶性病高风险评估页面"""
        self.show_info("导航到恶性病高风险评估页面")

    def navigate_to_chronic_risk(self, *args):
        """导航到慢性病高风险评估页面"""
        self.show_info("导航到慢性病高风险评估页面")

    def navigate_to_voice_triage(self, *args):
        """导航到语音分诊页面"""
        from kivy.logger import Logger as KivyLogger
        KivyLogger.info("导航到语音分诊页面")
        app = MDApp.get_running_app()
        sm = app.root if app else None
        # 检查是否已经有语音分诊屏幕
        if sm and not sm.has_screen("voice_triage_screen"):
            # 导入并添加语音分诊屏幕
            from screens.voice_triage_screen import VoiceTriageScreen
            sm.add_widget(VoiceTriageScreen(name="voice_triage_screen"))
        # 导航到语音分诊屏幕
        if sm:
            sm.current = "voice_triage_screen"

    def navigate_to_medical_companion(self, *args):
        """导航到陪诊服务页面"""
        self.navigate_to_companion_service()

    def navigate_to_survey(self, *args):
        """导航到评估量表页面"""
        try:
            # 导航到survey_screen，让用户先选择量表
            if self.manager:
                self.manager.current = 'survey_screen'
        except Exception as e:
            Logger.error(f"Navigation error: {e}")
            self.show_snackbar(self.NAVIGATION_ERROR_MESSAGE)

    def navigate_to_health_diary(self, *args):
        """导航到健康日记页面"""
        try:
            if self.manager:
                self.manager.current = 'health_diary_screen'
        except Exception as e:
            Logger.error(f"Navigation error: {e}")
            self.show_snackbar(self.NAVIGATION_ERROR_MESSAGE)

    def navigate_to_medication_management(self, *args):
        """导航到用药管理页面"""
        try:
            if self.manager:
                self.manager.current = 'medication_management_screen'
        except Exception as e:
            Logger.error(f"Navigation error: {e}")
            self.show_snackbar(self.NAVIGATION_ERROR_MESSAGE)

    def navigate_to_health_document(self, document_type="all"):
        """导航到健康资料管理页面
        Args:
            document_type (str, optional): 文档类型. Defaults to "all".
        """
        from kivy.logger import Logger as KivyLogger
        KivyLogger.info(f"导航到健康资料管理页面，类型: {document_type}")
        app = MDApp.get_running_app()
        sm = app.root if app else None
        # 检查是否已经有健康资料管理屏幕
        if sm and not sm.has_screen("health_document"):
            # 导入并添加健康资料管理屏幕
            from screens.health_document_screen import HealthDocumentScreen
            sm.add_widget(HealthDocumentScreen(name="health_document"))
        # 设置文档类型
        if sm and sm.has_screen("health_document"):
            health_document_screen = sm.get_screen("health_document")
            health_document_screen.document_type = document_type
        # 导航到健康资料管理屏幕
        if sm:
            sm.current = "health_document"

    def navigate_to_medical_records(self, *args):
        """导航到医疗记录管理页面（整合住院、门诊、检验、技诊报告）"""
        from kivy.logger import Logger as KivyLogger
        KivyLogger.info("导航到医疗记录管理页面")
        app = MDApp.get_running_app()
        sm = app.root if app else None
        # 检查是否已经有医疗记录管理屏幕
        if sm and not sm.has_screen("medical_records"):
            # 导入并添加医疗记录管理屏幕
            from screens.medical_records_screen import MedicalRecordsScreen
            sm.add_widget(MedicalRecordsScreen(name="medical_records"))
        # 导航到医疗记录管理屏幕
        if sm:
            sm.current = "medical_records"

    def navigate_to_personalized_checkup(self, *args):
        """导航到个性化体检方案页面"""
        from kivy.logger import Logger as KivyLogger
        KivyLogger.info("导航到个性化体检方案页面")
        app = MDApp.get_running_app()
        sm = app.root if app else None
        # 检查是否已经有个性化体检方案屏幕
        if sm and not sm.has_screen("personalized_checkup"):
            # 导入并添加个性化体检方案屏幕
            from screens.personalized_checkup_screen import PersonalizedCheckupScreen
            sm.add_widget(PersonalizedCheckupScreen(name="personalized_checkup"))
        # 导航到个性化体检方案屏幕
        if sm:
            sm.current = "personalized_checkup"

    def navigate_to_companion_service(self, *args):
        """导航到陪诊服务页面"""
        from kivy.logger import Logger as KivyLogger
        KivyLogger.info("导航到陪诊服务页面")
        app = MDApp.get_running_app()
        sm = app.root if app else None
        # 检查是否已经有陪诊服务屏幕
        if sm and not sm.has_screen("companion_service"):
            # 导入并添加陪诊服务屏幕
            from screens.companion_service_screen import CompanionServiceScreen
            sm.add_widget(CompanionServiceScreen(name="companion_service"))
        # 导航到陪诊服务屏幕
        if sm:
            sm.current = "companion_service"

    def navigate_to_other_records(self, *args):
        """导航到其它记录页面"""
        from kivy.logger import Logger as KivyLogger
        KivyLogger.info("导航到其它记录页面")
        app = MDApp.get_running_app()
        sm = app.root if app else None
        # 检查是否已经有其它记录屏幕
        if sm and not sm.has_screen("other_records"):
            # 导入并添加其它记录屏幕
            from screens.other_records_screen import OtherRecordsScreen
            sm.add_widget(OtherRecordsScreen(name="other_records"))
        # 导航到其它记录屏幕
        if sm:
            sm.current = "other_records"

    def show_snackbar(self, message):
        from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
        snackbar = MDSnackbar(MDSnackbarText(text=message), duration=2)
        snackbar.open()

    def navigate_to_management_log(self, instance=None):
        """导航到管理日志页面"""
        # 导航到管理日志页面
        app = MDApp.get_running_app()
        sm = app.root if app else None

        # 检查是否已经注册了管理日志屏幕
        if sm and not sm.has_screen('management_log'):
            from screens.management_log_screen import ManagementLogScreen
            management_log_screen = ManagementLogScreen(name='management_log')
            sm.add_widget(management_log_screen)

        if sm:
            sm.transition.direction = 'left'
            sm.current = 'management_log'

    def on_logout(self):
        """退出按钮点击事件"""
        # 使用KivyMD 2.0.1 dev0规范创建对话框
        from kivymd.uix.dialog import (
            MDDialog,
            MDDialogHeadlineText,
            MDDialogSupportingText,
            MDDialogButtonContainer
        )
        from kivymd.uix.button import MDButton, MDButtonText
        
        dialog = MDDialog(
            MDDialogHeadlineText(
                text="确认退出"
            ),
            MDDialogSupportingText(
                text="您确定要退出登录吗？"
            ),
            MDDialogButtonContainer(
                MDButton(
                    MDButtonText(text="取消"),
                    style="text",
                    on_release=lambda x: dialog.dismiss()
                ),
                MDButton(
                    MDButtonText(text="确定"),
                    style="filled",
                    on_release=lambda x: self.confirm_logout(dialog)
                )
            )
        )
        dialog.open()

    def confirm_logout(self, dialog):
        """确认退出"""
        # 关闭对话框
        dialog.dismiss()
        # 清除用户数据
        app = MDApp.get_running_app()
        if app:
            app.clear_user_data()
            # 导航到登录页面
            sm = app.root if app else None
            if sm:
                sm.transition.direction = 'right'
                sm.current = 'login_screen'

    def show_info(self, message):
        """显示信息提示"""
        # 使用应用程序的通知机制
        app = MDApp.get_running_app()
        if app and hasattr(app, 'show_notification'):
            app.show_notification(message)
        else:
            # 使用Snackbar作为备选
            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=message,
                ),
                pos_hint={"center_x": 0.5},
                duration=2,
            )
            snackbar.open()

    def on_add_quick_action(self):
        """处理添加快速操作按钮点击事件"""
        self.show_info("添加快速操作功能")
        # TODO: 实现添加快速操作的逻辑

    def on_remove_quick_action(self):
        """处理删除快速操作按钮点击事件"""
        self.show_info("删除快速操作功能")
        # TODO: 实现删除快速操作的逻辑

    def add_quick_action(self, title, icon, action):
        """动态添加快捷操作
        Args:
            title (str): 快捷操作标题
            icon (str): 图标名称
            action (callable): 点击事件处理函数
        """
        try:
            quick_actions_box = getattr(self, 'quick_actions_box', None)
            if quick_actions_box:
                card = QuickActionCard(
                    title=title,
                    icon=icon,
                    on_release=action
                )
                quick_actions_box.add_widget(card)
                
                # 调度更新高度
                Clock.schedule_once(lambda dt: self._update_quick_actions_height(), 0)
                
                Logger.info(f"[HomepageScreen] 添加快捷操作: {title}")
        except Exception as e:
            Logger.error(f"[HomepageScreen] 添加快捷操作失败: {e}")

    def remove_quick_action(self, title):
        """动态删除快捷操作
        Args:
            title (str): 要删除的快捷操作标题
        """
        try:
            quick_actions_box = getattr(self, 'quick_actions_box', None)
            if quick_actions_box:
                # 查找并删除匹配的快捷操作
                for child in quick_actions_box.children[:]:
                    if hasattr(child, 'title') and child.title == title:
                        quick_actions_box.remove_widget(child)
                        break
                
                # 调度更新高度
                Clock.schedule_once(lambda dt: self._update_quick_actions_height(), 0)
                
                Logger.info(f"[HomepageScreen] 删除快捷操作: {title}")
        except Exception as e:
            Logger.error(f"[HomepageScreen] 删除快捷操作失败: {e}")