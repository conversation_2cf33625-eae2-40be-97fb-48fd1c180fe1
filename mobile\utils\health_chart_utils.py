"""健康数据图表工具模块 - 集成图表组件实现
KivyMD 2.0.1 dev0 规范及 theme.py 配置遵循

整合原有的 health_chart_utils.py 和 pure_kivy_chart.py 功能
提供完整的图表绘制及数据处理功能
"""
import logging
from datetime import datetime
from typing import Dict, List, Any, Union, Optional, Tuple
import base64

# Kivy 相关导入
from kivy.uix.widget import Widget
from kivy.uix.label import Label
from kivy.graphics import Color, Line, Rectangle, Ellipse
from kivy.graphics.instructions import InstructionGroup
from kivy.metrics import dp, sp
from kivy.utils import get_color_from_hex

# KivyMD 相关导入
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.card import MDCard

# 主题配置导入
theme_available = False
AppTheme = None
try:
    from ..theme import AppTheme as AppTheme
    theme_available = True
    logging.debug("主题配置相对导入成功")
except ImportError:
    try:
        from mobile.theme import AppTheme as AppTheme
        theme_available = True
        logging.debug("主题配置绝对导入成功")
    except ImportError:
        try:
            from theme import AppTheme as AppTheme
            theme_available = True
            logging.debug("主题配置直接导入成功")
        except ImportError:
            logging.debug("主题配置导入失败")

# 颜色管理器
class ColorManager:
    """统一的颜色管理器"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._init_colors()
        return cls._instance
    
    def _init_colors(self):
        """初始化颜色配置"""
        try:
            # 尝试从主题获取颜色
            if theme_available and AppTheme is not None:
                self.primary = self._ensure_hex(getattr(AppTheme, 'PRIMARY_COLOR', '#2196F3'))
                self.secondary = self._ensure_hex(getattr(AppTheme, 'ACCENT_COLOR', '#FF9800'))
                self.success = self._ensure_hex(getattr(AppTheme, 'SUCCESS_COLOR', '#4CAF50'))
                self.warning = self._ensure_hex(getattr(AppTheme, 'WARNING_COLOR', '#FF5722'))
                self.error = self._ensure_hex(getattr(AppTheme, 'ERROR_COLOR', '#F44336'))
                self.background = self._ensure_hex(getattr(AppTheme, 'BACKGROUND_COLOR', '#FAFAFA'))
                self.text_secondary = self._ensure_hex(getattr(AppTheme, 'TEXT_SECONDARY', '#757575'))
            else:
                raise ImportError("主题不可用")
        except Exception as e:
            # 使用默认颜色
            self.primary = "#2196F3"
            self.secondary = "#FF9800"
            self.success = "#4CAF50"
            self.warning = "#FF5722"
            self.error = "#F44336"
            self.background = "#FAFAFA"
            self.text_secondary = "#757575"
        
        # 系列专用颜色
        self.series_colors = {
            '收缩压': "#FF0000",      # 红色
            '舒张压': "#0000FF",      # 蓝色
            '血糖值': "#00CC00",      # 绿色
            '心率': "#FFA500",        # 橙色
            '体重': "#800080",        # 紫色
            '体温': "#FF00FF",        # 粉色
        }
    
    def _ensure_hex(self, color):
        """确保颜色为十六进制格式"""
        if isinstance(color, list) and len(color) == 4:
            r, g, b, a = color
            return f"#{int(r*255):02x}{int(g*255):02x}{int(b*255):02x}"
        return color if isinstance(color, str) else "#2196F3"
    
    def get_series_color(self, series_name):
        """获取系列颜色"""
        return self.series_colors.get(series_name, self.primary)

# 基础图表渲染器
class BaseChartRenderer:
    """基础图表渲染器"""
    
    def __init__(self, color_manager):
        self.color_manager = color_manager
    
    def calculate_plot_area(self, widget):
        """计算绘图区域"""
        plot_x = widget.x + widget.margin_left
        plot_y = widget.y + widget.margin_bottom
        plot_width = widget.width - widget.margin_left - widget.margin_right
        plot_height = widget.height - widget.margin_top - widget.margin_bottom
        return plot_x, plot_y, plot_width, plot_height
    
    def calculate_data_range(self, y_data):
        """计算数据范围"""
        all_values = []
        for series_data in y_data.values():
            if isinstance(series_data, list):
                all_values.extend([v for v in series_data if isinstance(v, (int, float))])
        
        if not all_values:
            return 0, 1, 1
        
        y_min = min(all_values)
        y_max = max(all_values)
        y_range = y_max - y_min if y_max != y_min else 1
        return y_min, y_max, y_range

# 日志设置
logger = logging.getLogger(__name__)

class PureKivyChart(Widget):
    """纯 Kivy 图表组件
    
    使用 Kivy Canvas 直接绘制图表，不依赖外部图表库
    支持线性图表和柱状图两种类型
    """
    
    def __init__(self, **kwargs):
        """图表组件初始化"""
        super().__init__(**kwargs)
        
        # 图表设置
        self.chart_type = "bar"  # 默认为柱状图（直方图）
        self.data = {}
        
        # 初始化颜色管理器
        self.color_manager = ColorManager()
        self.renderer = BaseChartRenderer(self.color_manager)
        
        # 设置Canvas指令组
        self._setup_canvas_instructions()
        
        # 图表边距及尺寸
        self.margin_left = dp(60)
        self.margin_right = dp(120)  # 为图例预留空间，增加右边距
        self.margin_top = dp(60)     # 为图例预留空间，增加上边距
        self.margin_bottom = dp(60)
        
        # 图例设置
        self.legend_position = "top_right"  # 图例位置：右上角
        self.legend_size = dp(12)           # 减小图例尺寸
        self.show_data_labels = True        # 显示数据标签
        self.show_axis_scales = True        # 显示坐标轴刻度
        
        # 创建标签容器用于管理数据标签
        from kivy.uix.widget import Widget
        self.data_labels_container = Widget()
        self.add_widget(self.data_labels_container)
        
        # 绑定尺寸变化事件
        self.bind(size=self._update_chart, pos=self._update_chart)  # type: ignore
        
        logger.debug("PureKivyChart 初始化完成")

    def _setup_canvas_instructions(self):
        """设置Canvas指令组"""
        self.background_group = InstructionGroup()
        self.axes_group = InstructionGroup()
        self.data_group = InstructionGroup()
        self.labels_group = InstructionGroup()
        self.legend_group = InstructionGroup()
        
        # 确保canvas存在后再添加指令组
        if self.canvas:
            self.canvas.add(self.background_group)
            self.canvas.add(self.axes_group)
            self.canvas.add(self.data_group)
            self.canvas.add(self.labels_group)
            self.canvas.add(self.legend_group)

    def set_chart_type(self, chart_type: str):
        """设置图表类型
        
        Args:
            chart_type: 图表类型，'line' 或 'bar'
        """
        if chart_type in ['line', 'bar']:
            self.chart_type = chart_type
            self._update_chart()
            logger.debug(f"图表类型设置: {chart_type}")
        else:
            logger.warning(f"不支持的图表类型: {chart_type}")

    def validate_chart_data(self, data):
        """验证图表数据"""
        if not data or not isinstance(data, dict):
            raise ValueError("图表数据必须是非空字典")
        
        if 'x_data' not in data or not isinstance(data['x_data'], list):
            raise ValueError("必须提供x_data列表")
        
        if 'y_data' not in data or not isinstance(data['y_data'], dict):
            raise ValueError("必须提供y_data字典")
        
        # 验证数据长度一致性
        x_len = len(data['x_data'])
        for series_name, series_data in data['y_data'].items():
            if not isinstance(series_data, list):
                raise ValueError(f"系列{series_name}的数据必须是列表")
            if len(series_data) != x_len:
                raise ValueError(f"系列{series_name}的数据长度与x_data不一致")
        
        return True

    def update_data(self, data: Dict[str, Any]):
        """更新图表数据
        
        Args:
            data: 图表数据字典，格式:
                {
                    'title': '图表标题',
                    'x_data': ['时间1', '时间2', ...],
                    'y_data': {
                        '系列1': [值1, 值2, ...],
                        '系列2': [값1, 값2, ...]
                    },
                    'unit': '单位'
                }
        """
        try:
            self.validate_chart_data(data)
            self.data = data
            self._update_chart()
            logger.debug(f"图表数据更新完成，类型: {self.chart_type}")
        except ValueError as e:
            logger.error(f"图表数据验证失败: {e}")
            self._draw_error_message(f"数据错误: {e}")
        except Exception as e:
            logger.error(f"图表数据更新失败: {e}")
            self._draw_error_message(f"更新失败: {e}")

    def _clear_labels(self):
        """清除之前添加的标签组件"""
        try:
            # 移除所有 Label 类型的子组件
            labels_to_remove = [child for child in self.children if isinstance(child, Label)]
            for label in labels_to_remove:
                self.remove_widget(label)
        except Exception as e:
            logger.debug(f"标签移除时出错: {e}")
    
    def _update_chart(self, *args):
        """更新图表绘制"""
        if not self.data or not self.size[0] or not self.size[1]:
            return
        
        # 确保canvas存在
        if not self.canvas:
            return
        
        # 清除之前的绘制
        self.background_group.clear()
        self.axes_group.clear()
        self.data_group.clear()
        self.labels_group.clear()
        self.legend_group.clear()
        self._clear_labels()
        # 清空数据标签容器
        self.data_labels_container.clear_widgets()
        
        try:
            # 绘制各组件
            self._draw_background()
            self._draw_axes()
            
            if self.chart_type == "line":
                self._draw_line_chart()
            else:
                self._draw_bar_chart()
            self._draw_legend()
            
        except Exception as e:
            logger.error(f"图表绘制失败: {e}")
            self._draw_error_message(str(e))

    def _draw_background(self):
        """绘制背景"""
        # 确保canvas存在
        if not self.canvas:
            return
            
        # 清除之前的背景绘制
        self.background_group.clear()
        
        # 添加新的背景绘制指令
        color = get_color_from_hex(self.color_manager.background)
        self.background_group.add(Color(*color))
        self.background_group.add(Rectangle(pos=self.pos, size=self.size))

    def _draw_axes(self):
        """绘制坐标轴"""
        if not self.data.get('x_data') or not self.data.get('y_data'):
            return
        
        # 确保canvas存在
        if not self.canvas:
            return
            
        # 计算绘图区域
        plot_x, plot_y, plot_width, plot_height = self.renderer.calculate_plot_area(self)
        
        # 清除之前的坐标轴绘制
        self.axes_group.clear()
        
        # 添加新的坐标轴绘制指令
        # 设置坐标轴颜色
        color = get_color_from_hex(self.color_manager.text_secondary)
        self.axes_group.add(Color(*color))
        
        # X轴
        self.axes_group.add(Line(points=[plot_x, plot_y, plot_x + plot_width, plot_y], width=1))
        
        # Y轴
        self.axes_group.add(Line(points=[plot_x, plot_y, plot_x, plot_y + plot_height], width=1))
        
        # 绘制边框及标签
        self._draw_axis_labels(plot_x, plot_y, plot_width, plot_height)

    def _draw_axis_labels(self, plot_x, plot_y, plot_width, plot_height):
        """绘制坐标轴标签"""
        x_data = self.data.get('x_data', [])
        y_data = self.data.get('y_data', {})
        unit = self.data.get('unit', '')
        
        # 获取数据范围
        y_min, y_max, y_range = self.renderer.calculate_data_range(y_data)
        
        # 绘制 X 轴标签（时间点）
        if self.show_axis_scales and x_data:
            # X 轴自动调整优化：使数据在图表框中充满并均匀分布
            if len(x_data) == 1:
                # 单个数据点居中显示
                x_positions = [plot_x + plot_width / 2]
            else:
                # 多个数据点在图表框中从左边界到右边界均匀分布
                x_step = plot_width / (len(x_data) - 1) if len(x_data) > 1 else 0
                x_positions = [plot_x + i * x_step for i in range(len(x_data))]
            
            for i, (label, x_pos) in enumerate(zip(x_data, x_positions)):
                # 绘制网格线
                self.axes_group.add(Color(*get_color_from_hex(self.color_manager.text_secondary)))
                self.axes_group.add(Line(points=[x_pos, plot_y - dp(8), x_pos, plot_y], width=1))
                
                # 添加时间点标签
                try:
                    time_label = Label(
                        text=str(label),
                        font_size=sp(8),
                        color=get_color_from_hex(self.color_manager.text_secondary),
                        size_hint=(None, None),
                        size=(dp(60), dp(15)),
                        pos=(x_pos - dp(30), plot_y - dp(25)),
                        halign='center',
                        valign='middle'
                    )
                    time_label.text_size = time_label.size
                    self.add_widget(time_label)
                except Exception as e:
                    logger.debug(f"X轴标签绘制时出错: {e}")
        
        # 绘制 Y 轴标签（值刻度）
        if self.show_axis_scales:
            y_steps = 6  # 增加刻度密度
            for i in range(y_steps + 1):
                y_value = y_min + (y_range * i / y_steps)
                y_pos = plot_y + (plot_height * i / y_steps)
                
                # 绘制网格线
                self.axes_group.add(Color(*get_color_from_hex(self.color_manager.text_secondary)))
                self.axes_group.add(Line(points=[plot_x - dp(8), y_pos, plot_x, y_pos], width=1))
                
                # 添加值标签（不显示单位）
                try:
                    value_label = Label(
                        text=f"{int(y_value)}",
                        font_size=sp(8),
                        color=get_color_from_hex(self.color_manager.text_secondary),
                        size_hint=(None, None),
                        size=(dp(40), dp(15)),
                        pos=(plot_x - dp(50), y_pos - dp(7)),
                        halign='center',
                        valign='middle'
                    )
                    value_label.text_size = value_label.size
                    self.add_widget(value_label)
                except Exception as e:
                    logger.debug(f"Y轴标签绘制时出错: {e}")

    def _draw_line_chart(self):
        """绘制折线图"""
        x_data = self.data.get('x_data', [])
        y_data = self.data.get('y_data', {})
        
        if not x_data or not y_data:
            return
        
        # 确保canvas存在
        if not self.canvas:
            return
            
        # 计算绘图区域
        plot_x, plot_y, plot_width, plot_height = self.renderer.calculate_plot_area(self)
        
        # 获取数据范围
        y_min, y_max, y_range = self.renderer.calculate_data_range(y_data)
        
        # 清除之前的数据绘制
        self.data_group.clear()
        
        # 绘制各数据系列
        series_names = list(y_data.keys())
        for i, (series_name, series_data) in enumerate(y_data.items()):
            if not isinstance(series_data, list) or len(series_data) == 0:
                continue
            
            # 计算点坐标
            points = []
            x_step = plot_width / max(len(x_data) - 1, 1) if len(x_data) > 1 else 0
            
            for j, value in enumerate(series_data[:len(x_data)]):
                if isinstance(value, (int, float)):
                    x_pos = plot_x + j * x_step
                    y_pos = plot_y + ((value - y_min) / y_range) * plot_height
                    points.extend([x_pos, y_pos])
            
            if len(points) >= 4:  # 至少需要两个点
                # 设置线条颜色
                color = get_color_from_hex(self.color_manager.get_series_color(series_name))
                self.data_group.add(Color(*color))
                
                # 绘制线条
                self.data_group.add(Line(points=points, width=dp(2)))
                
                # 绘制数据点及数据标签
                for k in range(0, len(points), 2):
                    if k + 1 < len(points):
                        # 绘制数据点
                        self.data_group.add(Ellipse(pos=(points[k] - dp(3), points[k + 1] - dp(3)), size=(dp(6), dp(6))))
                        
                        # 添加数据标签
                        if self.show_data_labels:
                            try:
                                # 获取对应数据值（不显示单位）
                                data_index = k // 2
                                if data_index < len(series_data):
                                    value = series_data[data_index]
                                    
                                    # 创建数据标签（仅显示值，不显示单位）
                                    label_text = f"{int(value)}"
                                    data_label = Label(
                                        text=label_text,
                                        font_size=sp(9),
                                        color=get_color_from_hex(self.color_manager.text_secondary),
                                        size_hint=(None, None),
                                        size=(dp(40), dp(15)),
                                        pos=(points[k] - dp(20), points[k + 1] + dp(8)),
                                        halign='center',
                                        valign='middle'
                                    )
                                    data_label.text_size = data_label.size
                                    self.add_widget(data_label)
                                    
                            except Exception as e:
                                logger.debug(f"折线图数据标签绘制时出错: {e}")

    def _draw_bar_chart(self):
        """绘制柱状图"""
        x_data = self.data.get('x_data', [])
        y_data = self.data.get('y_data', {})
        
        if not x_data or not y_data:
            return
        
        # 确保canvas存在
        if not self.canvas:
            return
            
        # 计算绘图区域
        plot_x, plot_y, plot_width, plot_height = self.renderer.calculate_plot_area(self)
        
        # 获取数据范围
        all_values = []
        for series_data in y_data.values():
            if isinstance(series_data, list):
                all_values.extend([v for v in series_data if isinstance(v, (int, float))])
        
        if not all_values:
            return
        
        y_min = min(0, min(all_values))  # 柱状图 Y 轴从 0 开始
        y_max = max(all_values)
        y_range = y_max - y_min if y_max != y_min else 1
        
        # 清除之前的数据绘制
        self.data_group.clear()
        
        # 计算柱子宽度及位置
        num_series = len(y_data)
        
        # 计算 X 轴位置（与标签绘制一致）
        if len(x_data) == 1:
            # 单个数据点居中显示
            x_positions = [plot_x + plot_width / 2]
            bar_group_width = plot_width * 0.3  # 单个柱子组占据 30% 宽度
        else:
            # 多个数据点在图表框中从左边界到右边界均匀分布
            x_step = plot_width / (len(x_data) - 1) if len(x_data) > 1 else 0
            x_positions = [plot_x + i * x_step for i in range(len(x_data))]
            bar_group_width = plot_width / len(x_data) * 0.8  # 柱子组占据可用空间的 80%
        
        bar_width = bar_group_width / (num_series + 1)  # 留出间隔
        
        # 绘制各数据系列
        series_names = list(y_data.keys())
        for i, (series_name, series_data) in enumerate(y_data.items()):
            if not isinstance(series_data, list) or len(series_data) == 0:
                continue
            
            # 根据数据系列名称设置专用颜色
            color = get_color_from_hex(self.color_manager.get_series_color(series_name))
            self.data_group.add(Color(*color))
            
            # 绘制各柱子
            for j, value in enumerate(series_data[:len(x_data)]):
                if isinstance(value, (int, float)) and j < len(x_positions):
                    # 计算柱子位置及尺寸（使用新的 X 轴位置）
                    x_center = x_positions[j]
                    bar_x = x_center + (i - num_series/2 + 0.5) * bar_width
                    
                    bar_height = ((value - y_min) / y_range) * plot_height
                    bar_y = plot_y + ((0 - y_min) / y_range) * plot_height  # 从 0 开始
                    
                    if bar_height > 0:
                        self.data_group.add(Rectangle(pos=(bar_x, bar_y), size=(bar_width * 0.8, bar_height)))
                        
                        # 添加数据显示
                        if self.show_data_labels:
                            try:
                                # 数据标签位置（柱子顶部）
                                label_x = bar_x + (bar_width * 0.8) / 2
                                label_y = bar_y + bar_height + dp(5)
                                
                                # 确保标签在图表区域内
                                if (self.x + self.margin_left <= label_x <= self.x + self.width - self.margin_right and
                                    self.y + self.margin_bottom <= label_y <= self.y + self.height - self.margin_top):
                                    
                                    # 创建数据标签（仅显示值，不显示单位）
                                    label_text = f"{int(value)}"
                                    data_label = Label(
                                        text=label_text,
                                        font_size=sp(10),
                                        color=get_color_from_hex(self.color_manager.text_secondary),
                                        size_hint=(None, None),
                                        size=(dp(50), dp(20)),
                                        pos=(label_x - dp(25), label_y),
                                        halign='center',
                                        valign='middle'
                                    )
                                    data_label.text_size = data_label.size
                                    self.data_labels_container.add_widget(data_label)
                            except Exception as e:
                                logger.debug(f"数据标签绘制时出错: {e}")
    
    def _draw_legend(self):
        """绘制图例 - 位于右上角，尺寸较小"""
        y_data = self.data.get('y_data', {})
        
        if not y_data:
            return
        
        # 清除之前的图例绘制
        self.legend_group.clear()
        
        # 图例位置 - 右上角
        legend_x = self.x + self.width - self.margin_right + dp(10)
        legend_y = self.y + self.height - self.margin_top + dp(30)
        
        # 获取单位信息
        unit = self.data.get('unit', '')
        
        series_names = list(y_data.keys())
        for i, series_name in enumerate(series_names):
            y_pos = legend_y - i * dp(18)  # 减小间隔
            
            # 根据数据系列名称设置专用颜色（与柱状图一致）
            color = get_color_from_hex(self.color_manager.get_series_color(series_name))
            self.legend_group.add(Color(*color))
            self.legend_group.add(Rectangle(pos=(legend_x, y_pos), size=(self.legend_size, dp(8))))
            
            # 绘制图例文本
            text_color = get_color_from_hex(self.color_manager.text_secondary)
            
            # 为不同数据系列创建正确的单位显示图例标签
            if series_name in ["收缩压", "舒张压"]:
                legend_text = f"{series_name} (mmHg)"
            elif series_name == "心率":
                legend_text = f"{series_name} (次/分)"
            else:
                legend_text = series_name
            legend_label = Label(
                text=legend_text,
                font_size=sp(9),
                color=text_color,
                size_hint=(None, None),
                size=(dp(80), dp(15)),
                pos=(legend_x + self.legend_size + dp(5), y_pos - dp(2)),
                halign='left',
                valign='middle'
            )
            legend_label.text_size = legend_label.size
            self.add_widget(legend_label)

    def _draw_error_message(self, message: str = ""):
        """绘制错误信息"""
        # 确保canvas存在
        if not self.canvas:
            return
            
        # 清除之前的背景绘制
        self.background_group.clear()
        
        color = get_color_from_hex(self.color_manager.error)
        self.background_group.add(Color(*color))
        # 此处可以绘制文本，但 Kivy 的 Canvas 不直接支持文本
        # 在实际应用中可以使用 Label 组件来显示错误信息

class PureKivyChartView(MDCard):
    """纯 Kivy 图表视图组件
    
    封装 PureKivyChart，提供完整的图表界面
    """
    
    def __init__(self, **kwargs):
        """图表视图初始化"""
        super().__init__(**kwargs)
        
        # 卡片样式设置，从主题获取样式
        self.orientation = 'vertical'
        self.size_hint_y = None
        self.height = dp(350)
        self.radius = [dp(12)]
        self.elevation = 2
        self.padding = [dp(16), dp(16)]
        
        # 将 chart 属性初始化为 None，确保属性存在
        self.chart = None
        self.chart_title = None
        self.toggle_button = None
        self.current_data = {}
        
        try:
            # 创建标题及切换按钮
            header_layout = MDBoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(40),
                spacing=dp(10)
            )
            
            self.chart_title = MDLabel(
                text="健康数据图表",
                theme_text_color="Primary",
                size_hint_x=0.7,
                height=dp(40)
            )
            
            self.toggle_button = MDButton(
                MDButtonText(text="切换为柱状图"),
                style="outlined",
                size_hint_x=0.3,
                height=dp(36),
                on_release=self.toggle_chart_type
            )
            
            header_layout.add_widget(self.chart_title)
            header_layout.add_widget(self.toggle_button)
            self.add_widget(header_layout)
            
            # 创建图表组件
            self.chart = PureKivyChart()
            self.add_widget(self.chart)
            
            logger.debug("PureKivyChartView 初始化完成")
            
        except Exception as e:
            logger.error(f"PureKivyChartView 初始化失败: {e}")
            logger.exception(e)
    
    def toggle_chart_type(self, *args):
        """切换图表类型"""
        try:
            if self.chart and hasattr(self.chart, 'chart_type'):
                if self.chart.chart_type == "line":
                    self.chart.set_chart_type("bar")
                    if self.toggle_button and hasattr(self.toggle_button, 'children'):
                        self.toggle_button.children[0].text = "切换为折线图"
                else:
                    self.chart.set_chart_type("line")
                    if self.toggle_button and hasattr(self.toggle_button, 'children'):
                        self.toggle_button.children[0].text = "切换为柱状图"
                
                # 如果有当前数据则重新绘制图表
                if self.current_data:
                    self.chart.update_data(self.current_data)
                    
                logger.debug(f"图表类型切换: {self.chart.chart_type}")
            else:
                logger.warning("图表组件不可用，无法切换类型")
                
        except Exception as e:
            logger.error(f"图表类型切换失败: {e}")
    
    def set_chart_type(self, chart_type: str):
        """设置图表类型"""
        try:
            if self.chart and hasattr(self.chart, 'set_chart_type'):
                self.chart.set_chart_type(chart_type)
                
                # 更新按钮文本
                if self.toggle_button and hasattr(self.toggle_button, 'children'):
                    if chart_type == "line":
                        self.toggle_button.children[0].text = "切换为柱状图"
                    else:
                        self.toggle_button.children[0].text = "切换为折线图"
                        
                logger.debug(f"图表类型设置: {chart_type}")
            else:
                logger.warning("图表组件不可用，无法设置类型")
                
        except Exception as e:
            logger.error(f"图表类型设置失败: {e}")
    
    def update_chart(self, data: Dict[str, Any]):
        """更新图表数据
        
        Args:
            data: 图表数据字典
        """
        try:
            self.current_data = data
            
            # 更新标题
            if hasattr(self, 'chart_title') and self.chart_title and data.get('title'):
                self.chart_title.text = data['title']
            
            # 更新图表
            if self.chart and hasattr(self.chart, 'update_data'):
                self.chart.update_data(data)
                logger.debug(f"图表数据更新成功，类型: {getattr(self.chart, 'chart_type', 'unknown')}")
            else:
                logger.warning("图表组件不可用，无法更新数据")
            
        except Exception as e:
            logger.error(f"图表数据更新失败: {e}")
            # 显示错误信息
            if hasattr(self, 'chart_title') and self.chart_title:
                self.chart_title.text = f"图表更新失败: {e}"

class HealthChartView(PureKivyChartView):
    """健康数据图表视图组件
    继承 PureKivyChartView，提供额外功能
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.chart_type = "line"  # 默认图表类型：line 或 bar
        self.current_data = None
    
    def toggle_chart_type(self, *args):
        """切换图表类型"""
        try:
            if self.chart_type == "line":
                self.chart_type = "bar"
            else:
                self.chart_type = "line"
            
            # 更新图表类型
            self.set_chart_type(self.chart_type)
            
            # 如果有当前数据则重新绘制图表
            if self.current_data:
                self.update_chart(self.current_data)
                
            logger.debug(f"图表类型切换: {self.chart_type}")
        except Exception as e:
            logger.error(f"图表类型切换失败: {e}")

# 数据处理函数
def format_time_data(time_data: List[str]) -> List[str]:
    """格式化时间数据
    
    Args:
        time_data: 时间 字符串 列表，支持 ISO 格式或其他格式
        
    Returns:
        List[str]: 格式化的时间 字符串 列表
    """
    try:
        formatted_times = []
        for time_str in time_data:
            try:
                # ISO 格式 时间 解析 尝试
                if 'T' in time_str:
                    dt = datetime.fromisoformat(time_str.replace('T', ' '))
                    formatted_times.append(dt.strftime('%H:%M'))
                else:
                    # 如果不是 ISO 格式则返回原 字符串
                    formatted_times.append(time_str)
            except Exception:
                # 解析 失败 时 返回 原 字符串
                formatted_times.append(time_str)
        return formatted_times
    except Exception as e:
        logger.error(f"时间 数据 格式化 失败: {e}")
        return time_data

def create_health_chart_data(health_records: Optional[List[Dict[str, Any]]] = None, 
                           title: Optional[str] = None, 
                           x_data: Optional[List] = None, 
                           y_data: Optional[Dict[str, List]] = None, 
                           unit: Optional[str] = None) -> Dict[str, Any]:
    """从 健康 记录 生成 图表 数据，支持 两种 调用 方式
    
    方法 1 - 从 健康 记录 生成:
        health_records: 健康 记录 列表
        
    方法 2 - 直接 提供 数据:
        title: 图表 标题
        x_data: X 轴 数据 (时间 或 日期)
        y_data: Y 轴 数据 字典，键 是 数据 系列 名称，值 是 数字 列表
        unit: 数据 单位
        
    Returns:
        Dict[str, Any]: 图表 数据
    """
    try:
        # 方法 2: 直接 提供 数据
        if title is not None and x_data is not None and y_data is not None:
            return {
                'title': title,
                'x_data': x_data,
                'y_data': y_data,
                'unit': unit or ''
            }
        
        # 方法 1: 从 健康 记录 生成
        if health_records is None:
            health_records = []
            
        chart_data = {}
        
        for record in health_records:
            date_str = record.get('date', '')
            
            # 血压 数据 处理
            if 'blood_pressure' in record and record['blood_pressure']:
                bp_data = record['blood_pressure']
                if isinstance(bp_data, list) and bp_data:
                    for bp_record in bp_data:
                        if isinstance(bp_record, dict):
                            systolic = bp_record.get('systolic')
                            diastolic = bp_record.get('diastolic')
                            
                            if systolic:
                                if 'blood_pressure_systolic' not in chart_data:
                                    chart_data['blood_pressure_systolic'] = []
                                chart_data['blood_pressure_systolic'].append((date_str, systolic))
                            
                            if diastolic:
                                if 'blood_pressure_diastolic' not in chart_data:
                                    chart_data['blood_pressure_diastolic'] = []
                                chart_data['blood_pressure_diastolic'].append((date_str, diastolic))
            
            # 血糖 数据 处理
            if 'blood_sugar' in record and record['blood_sugar']:
                bs_data = record['blood_sugar']
                if isinstance(bs_data, list) and bs_data:
                    for bs_record in bs_data:
                        if isinstance(bs_record, dict):
                            value = bs_record.get('value')
                            if value:
                                if 'blood_sugar' not in chart_data:
                                    chart_data['blood_sugar'] = []
                                chart_data['blood_sugar'].append((date_str, value))
            
            # 体重 数据 处理
            if 'weight' in record and record['weight']:
                weight_data = record['weight']
                if isinstance(weight_data, dict):
                    value = weight_data.get('value')
                    if value:
                        if 'weight' not in chart_data:
                            chart_data['weight'] = []
                        chart_data['weight'].append((date_str, value))
        
        logger.debug(f"图表 数据 生成 成功，包含 {len(chart_data)} 个 数据 系列")
        return chart_data
        
    except Exception as e:
        logger.error(f"图表 数据 生成 失败: {e}")
        logger.exception(e)
        return {}

def create_health_chart(chart_data, chart_type='line', width=400, height=300):
    """健康 图表 生成
    
    Args:
        chart_data: 图表 数据 字典，包含 title, x_data, y_data, unit 等
        chart_type: 图表 类型 ('line', 'bar', 'scatter')
        width: 图表 宽度
        height: 图表 高度
    
    Returns:
        str: 图表 图像 的 base64 编码 字符串，失败 时 返回 None
    """
    try:
        # 简单 的 SVG 图表 占位符 返回
        title = chart_data.get('title', '健康 数据 图表')
        unit = chart_data.get('unit', '')
        x_data = chart_data.get('x_data', [])
        y_data = chart_data.get('y_data', [])
        
        # 简单 的 SVG 图表 生成
        svg_content = f'''
        <svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
            <text x="{width//2}" y="30" text-anchor="middle" font-family="Arial" font-size="14" fill="#333">{title}</text>
            <text x="{width//2}" y="{height//2}" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">图表 数据: {len(y_data)} 个 数据 点</text>
            <text x="{width//2}" y="{height//2 + 20}" text-anchor="middle" font-family="Arial" font-size="10" fill="#999">单位: {unit}</text>
            <text x="{width//2}" y="{height - 20}" text-anchor="middle" font-family="Arial" font-size="10" fill="#999">类型: {chart_type}</text>
        </svg>
        '''
        
        # 将 SVG 转换 为 base64
        svg_base64 = base64.b64encode(svg_content.encode('utf-8')).decode('utf-8')
        
        logger.info(f"图表 更新 成功，类型: {chart_type}")
        return svg_base64
        
    except Exception as e:
        logger.error(f"图表 生成 失败: {e}")
        logger.exception(e)
        return None

# 函数 和 类 导出
__all__ = ['HealthChartView', 'PureKivyChartView', 'PureKivyChart', 'create_health_chart_data', 'create_health_chart', 'format_time_data']